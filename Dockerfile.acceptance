FROM 632884248997.dkr.ecr.ap-south-1.amazonaws.com/gamma-dependencies:latest
# Accept service_name as build-arg
ARG service_name

# Ref: https://stackoverflow.com/questions/35560894/is-docker-arg-allowed-within-cmd-instruction
ENV service_name ${service_name}

COPY . /go/src/github.com/epifi/gamma/
WORKDIR /go/src/github.com/epifi/gamma/

# Run tests
CMD ACCEPTANCE_TEST=TRUE make test_acceptance target=acceptance
