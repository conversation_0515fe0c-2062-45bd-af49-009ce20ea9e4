FROM 632884248997.dkr.ecr.ap-south-1.amazonaws.com/gamma-dependencies:latest

# Accept service_name as build-arg
ARG dir
ARG host_workdir

# Ref: https://stackoverflow.com/questions/35560894/is-docker-arg-allowed-within-cmd-instruction
ENV dir ${dir}

# Server and worker binaries are build outside container (for faster build using go cache) resulting in storing build metadata using host working directory
# runtime.Caller is a util method used to get the current path of a file which in turn is used to find the config files for each server
# This util method returns the current path using metadata created at build time
# Using the same directory to avoid file not found error at the time of config loading for each server
COPY . ${host_workdir}
WORKDIR ${host_workdir}

# Run tests
CMD ACCEPTANCE_TEST=TRUE ENVIRONMENT=test gotestsum --format=standard-verbose --raw-command -- go tool test2json -t ./${dir}/output.test -test.v
