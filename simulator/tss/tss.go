package tss

import (
	"encoding/xml"
	"io"
	"net/http"
	"time"

	"github.com/pborman/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/simulator/config/genconf"
	"github.com/epifi/gamma/vendorgateway/aml"
)

type TssService struct {
	dynConf *genconf.Config
}

func NewTssService(conf *genconf.Config) *TssService {
	return &TssService{
		dynConf: conf,
	}
}

// nolint:funlen
func (s *TssService) GenerateScreeningAlert(w http.ResponseWriter, request *http.Request) {
	matchPansCfg := s.dynConf.AmlMatchFoundPans()
	matchStatus := s.dynConf.AMLScreeningMatchValue()
	if matchStatus != "Match" && matchStatus != "NotMatch" && matchStatus != "Error" {
		logger.ErrorNoCtx("match status is invalid", zap.String("match status value-", matchStatus))
		s.sendResponse(w, http.StatusInternalServerError, []byte(`{"Message": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r\n<A44ResponseModel xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\r\n<ErrorMessage>There is an error in XML document (0, 0).</ErrorMessage>\r\n  <ScreeningResults />\r\n</A44ResponseModel>"}`))
		return
	}

	reqBytes, rErr := io.ReadAll(request.Body)
	if rErr != nil {
		logger.ErrorNoCtx("error in reading request bytes")
		s.sendResponse(w, http.StatusInternalServerError, nil)
		return
	}
	jsonReq := &tss.ScreenCustomerRequest{}
	jErr := protojson.Unmarshal(reqBytes, jsonReq)
	if jErr != nil {
		logger.ErrorNoCtx("error in unmarshalling json")
		s.sendResponse(w, http.StatusInternalServerError, nil)
		return
	}
	xmlReq := &aml.ScreeningRequestData{}
	xErr := xml.Unmarshal([]byte(jsonReq.GetXmlString()), xmlReq)
	if xErr != nil {
		logger.ErrorNoCtx("error in unmarshalling xml")
		s.sendResponse(w, http.StatusInternalServerError, nil)
		return
	}
	if xmlReq.Records == nil || xmlReq.Records.Record == nil {
		logger.ErrorNoCtx("empty records")
		s.sendResponse(w, http.StatusInternalServerError, nil)
		return
	}

	pan := xmlReq.Records.Record.Pan
	if matchStatus != "Error" {
		if matchPansCfg.Get(pan) {
			matchStatus = "Match"
		} else {
			matchStatus = "NotMatch"
		}
	}

	obj := &aml.ScreeningResults{
		XMLName: xml.Name{},
		ScreeningResult: struct {
			Text             string `xml:",chardata"`
			RejectionMessage string `xml:"RejectionMessage"`
			RejectionCode    string `xml:"RejectionCode"`
			SystemName       string `xml:"SystemName"`
			RequestId        string `xml:"RequestId"`
			RecordIdentifier string `xml:"RecordIdentifier"`
			Matched          string `xml:"Matched"`
			CaseId           string `xml:"CaseId"`
			Link             string `xml:"Link"`
			AlertCount       string `xml:"AlertCount"`
			Alerts           struct {
				Text  string `xml:",chardata"`
				Alert []struct {
					Text                           string `xml:",chardata"`
					CaseId                         string `xml:"CaseId"`
					AlertId                        string `xml:"AlertId"`
					Source                         string `xml:"Source"`
					Rule                           string `xml:"Rule"`
					SourceUniqueId                 string `xml:"SourceUniqueId"`
					TrackwizzId                    string `xml:"TrackwizzId"`
					WatchListName                  string `xml:"WatchListName"`
					MatchingPercentage             string `xml:"MatchingPercentage"`
					IsActive                       string `xml:"IsActive"`
					WatchlistChangeLogActionableId struct {
						Text string `xml:",chardata"`
						Nil  string `xml:"nil,attr"`
					} `xml:"WatchlistChangeLogActionableId"`
					Activity       string `xml:"Activity"`
					AmlWatchlistId string `xml:"AmlWatchlistId"`
					MatchType      string `xml:"MatchType"`
				} `xml:"Alert"`
			} `xml:"Alerts"`
		}{
			SystemName:       "Epifi Tech",
			RequestId:        xmlReq.Records.Record.RequestId,
			RecordIdentifier: xmlReq.Records.Record.RecordIdentifier,
		},
	}
	obj.ScreeningResult.Matched = matchStatus
	switch matchStatus {
	case "Error":
		obj.ScreeningResult.RejectionCode = "RC749"
		obj.ScreeningResult.RejectionMessage = "ApplicationFormNumber had exceeded the database length.Allowed length is 100 characters max."
	case "Match":
		caseId := uuid.NewUUID().String()
		obj.ScreeningResult.CaseId = caseId
		obj.ScreeningResult.AlertCount = "1"
		obj.ScreeningResult.Alerts.Alert = []struct {
			Text                           string `xml:",chardata"`
			CaseId                         string `xml:"CaseId"`
			AlertId                        string `xml:"AlertId"`
			Source                         string `xml:"Source"`
			Rule                           string `xml:"Rule"`
			SourceUniqueId                 string `xml:"SourceUniqueId"`
			TrackwizzId                    string `xml:"TrackwizzId"`
			WatchListName                  string `xml:"WatchListName"`
			MatchingPercentage             string `xml:"MatchingPercentage"`
			IsActive                       string `xml:"IsActive"`
			WatchlistChangeLogActionableId struct {
				Text string `xml:",chardata"`
				Nil  string `xml:"nil,attr"`
			} `xml:"WatchlistChangeLogActionableId"`
			Activity       string `xml:"Activity"`
			AmlWatchlistId string `xml:"AmlWatchlistId"`
			MatchType      string `xml:"MatchType"`
		}{
			{
				CaseId:    caseId,
				AlertId:   "1",
				MatchType: "Confirmed",
			},
		}
	default:
		// do nothing
	}

	objResp, err := xml.Marshal(obj)
	if err != nil {
		logger.ErrorNoCtx("error while marshaling data in xml")
		s.sendResponse(w, http.StatusInternalServerError, []byte(`{"Message": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r\n<A44ResponseModel xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\r\n<ErrorMessage>There is an error in XML document (0, 0).</ErrorMessage>\r\n  <ScreeningResults />\r\n</A44ResponseModel>"}`))
		return
	}
	jsonObj, jErr := protojson.Marshal(&tss.ScreenCustomerResponse{ResultXml: string(objResp)})
	if jErr != nil {
		logger.ErrorNoCtx("error while marshaling data in json")
		s.sendResponse(w, http.StatusInternalServerError, []byte(`{"Message": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r\n<A44ResponseModel xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\r\n<ErrorMessage>There is an error in XML document (0, 0).</ErrorMessage>\r\n  <ScreeningResults />\r\n</A44ResponseModel>"}`))
		return
	}
	s.sendResponse(w, 200, jsonObj)
}

func (s *TssService) InitiateScreening(w http.ResponseWriter, request *http.Request) {
	res, err := s.getScreeningRes(request)
	if err != nil {
		logger.ErrorNoCtx("error handling screening request", zap.Error(err))
		s.sendResponse(w, http.StatusInternalServerError, []byte(`{"ValidationCode": "RC001", "ValidationDescription": "Internal error"}`))
		return
	}
	s.sendResponse(w, http.StatusOK, res)
}

func (s *TssService) getScreeningRes(request *http.Request) ([]byte, error) {
	reqBody, err := io.ReadAll(request.Body)
	if err != nil {
		return nil, errors.Wrap(err, "error reading request body")
	}
	as501Req := &tss.AS501Request{}
	err = protojson.Unmarshal(reqBody, as501Req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling request body json to AS501Request proto")
	}
	if len(as501Req.GetCustomerList()) == 0 {
		return nil, errors.New("customer list is empty")
	}
	defaultMatchVal := s.dynConf.AMLScreeningMatchValue()
	if defaultMatchVal != "Match" && defaultMatchVal != "NotMatch" {
		return nil, errors.Errorf("invalid default match value: %s", defaultMatchVal)
	}
	matchFoundPans := s.dynConf.AmlMatchFoundPans()
	customer := as501Req.GetCustomerList()[0]
	var matchVal string
	if matchFoundPans.Get(customer.GetPan()) {
		matchVal = "Match"
	} else {
		matchVal = defaultMatchVal
	}

	var customerResponse *tss.CustomerResponse
	if matchVal == "Match" {
		caseId := uuid.NewUUID().String()
		customerResponse = &tss.CustomerResponse{
			SourceSystemCustomerCode: customer.GetSourceSystemCustomerCode(),
			ValidationOutcome:        "Success",
			SuggestedAction:          "Review",
			PurposeResponse: []*tss.PurposeResponse{
				{
					Purpose:     "Initial Screening",
					PurposeCode: as501Req.GetPurpose(),
					Data: &tss.PurposeData{
						HitsDetected: "Yes",
						HitsCount:    1,
						ConfirmedHit: "No",
						CaseId:       caseId,
						CaseUrl:      "https://screenza.example.com/case/" + caseId,
						HitResponse: []*tss.HitResponse{
							{
								Source:                      "FBI Wanted Person",
								WatchlistSourceId:           "20",
								MatchType:                   "Probable",
								Score:                       80.0,
								ConfirmedMatchingAttributes: "Name",
							},
						},
					},
				},
			},
		}
	} else {
		// Default no-match response
		customerResponse = &tss.CustomerResponse{
			SourceSystemCustomerCode: customer.GetSourceSystemCustomerCode(),
			ValidationOutcome:        "Success",
			SuggestedAction:          "Proceed",
			PurposeResponse: []*tss.PurposeResponse{
				{
					Purpose:     "Initial Screening",
					PurposeCode: as501Req.GetPurpose(),
					Data: &tss.PurposeData{
						HitsDetected: "No",
						HitsCount:    0,
						ConfirmedHit: "No",
					},
				},
			},
		}
	}
	res := &tss.AS501Response{
		RequestId:        as501Req.GetRequestId(),
		OverallStatus:    "AcceptedByTW",
		CustomerResponse: []*tss.CustomerResponse{customerResponse},
	}
	resJson, err := protojson.Marshal(res)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling response to JSON")
	}
	return resJson, nil
}

// ListCases simulates the AS502 API for listing cases
func (s *TssService) ListCases(w http.ResponseWriter, request *http.Request) {
	res, err := s.getListScreeningCasesRes(request)
	if err != nil {
		logger.ErrorNoCtx("error handling screening request", zap.Error(err))
		s.sendResponse(w, http.StatusInternalServerError, []byte(`{"ValidationCode": "RC001", "ValidationDescription": "Internal error"}`))
		return
	}
	s.sendResponse(w, http.StatusOK, res)
}

func (s *TssService) getListScreeningCasesRes(req *http.Request) ([]byte, error) {
	reqBytes, err := io.ReadAll(req.Body)
	if err != nil {
		return nil, errors.Wrap(err, "error reading request body")
	}
	listCasesReq := &tss.ListCasesRequest{}
	err = protojson.Unmarshal(reqBytes, listCasesReq)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling list cases request")
	}
	mockCases := generateMockCases(listCasesReq.GetApiRequestDto())
	res := &tss.ListCasesResponse{
		RequestId: listCasesReq.GetRequestId(),
		ResponseData: &tss.ListCasesResponseData{
			CaseCount:   int32(len(mockCases)),
			CaseDetails: mockCases,
		},
	}
	resJson, err := protojson.Marshal(res)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling list cases response")
	}
	return resJson, nil
}

func generateMockCases(apiReq *tss.ListCasesRequestQuery) []*tss.CaseDetails {
	// Generate 2-5 mock cases
	caseCount := 3
	cases := make([]*tss.CaseDetails, caseCount)

	baseTime := time.Now().UTC()

	for i := 0; i < caseCount; i++ {
		caseId := uuid.NewUUID().String()
		customerCode := "CUST" + uuid.NewUUID().String()[:8]

		// Vary case categories and types based on request
		caseCategory := "Open"
		caseType := "Initial"
		if i%2 == 1 {
			caseCategory = "Pending"
			caseType = "WatchlistAdded"
		}

		cases[i] = &tss.CaseDetails{
			CaseId:                           caseId,
			CaseCreationDateTimeInUtc:        baseTime.Add(-time.Duration(i*24) * time.Hour).Format(time.RFC3339),
			SourceSystemName:                 apiReq.GetSourceSystemName(),
			SourceSystemCustomerCode:         customerCode,
			ApplicationRefNumber:             "APP" + uuid.NewUUID().String()[:8],
			CaseOf:                           "MainKYC",
			LinkedToSourceSystemCustomerCode: "",
			Relation:                         "",
			ScreeningProfile:                 "DEFAULT_PROFILE",
			ScreeningProfileName:             "Default Screening Profile",
			CustomerName:                     "Test Customer " + uuid.NewUUID().String()[:4],
			CaseType:                         caseType,
			InitialScreeningMode:             "API",
			OnboardingDecision:               "Proceed",
			TotalAlertCount:                  int32(2 + i),
			ConfirmedAlertCount:              int32(i),
			ProbableAlertCount:               int32(1),
			PendingForDecision:               int32(1),
			NoMatchCount:                     int32(0),
			TrueMatchCount:                   int32(i),
			CaseStage:                        "Investigation",
			CaseCategory:                     caseCategory,
			CurrentAssignee:                  "<EMAIL>",
			CaseClosureDateTimeInUtc:         "",
			FinalRemarks:                     "",
		}
	}
	return cases
}

// GetCaseDetails simulates the AS503 API for getting case details
func (s *TssService) GetCaseDetails(w http.ResponseWriter, request *http.Request) {
	res, err := s.getCaseDetailsRes(request)
	if err != nil {
		logger.ErrorNoCtx("error handling get case details request", zap.Error(err))
		s.sendResponse(w, http.StatusInternalServerError, []byte(`{"ValidationCode": "RC001", "ValidationDescription": "Internal error"}`))
		return
	}
	s.sendResponse(w, http.StatusOK, res)
}

func (s *TssService) getCaseDetailsRes(req *http.Request) ([]byte, error) {
	reqBytes, err := io.ReadAll(req.Body)
	if err != nil {
		return nil, errors.Wrap(err, "error reading request body")
	}
	getCaseDetailsReq := &tss.GetCaseDetailsRequest{}
	err = protojson.Unmarshal(reqBytes, getCaseDetailsReq)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling get case details request")
	}
	caseId := getCaseDetailsReq.GetApiRequestDto().GetCaseId()
	if caseId == "" {
		return nil, errors.New("case ID is required")
	}
	mockCaseDetails := generateDetailedMockCase(caseId)
	res := &tss.GetCaseDetailsResponse{
		RequestId: getCaseDetailsReq.GetRequestId(),
		ResponseData: &tss.GetCaseDetailsResponseData{
			CaseDetails: mockCaseDetails,
		},
	}
	resJson, err := protojson.Marshal(res)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling get case details response")
	}
	return resJson, nil
}

func generateDetailedMockCase(caseId string) *tss.CaseDetails {
	baseTime := time.Now().UTC()
	customerCode := "CUST" + uuid.NewUUID().String()[:8]

	// Generate mock case actions
	caseActions := []*tss.CaseAction{
		{
			UserName:      "<EMAIL>",
			DateTimeInUtc: baseTime.Add(-48 * time.Hour).Format(time.RFC3339),
			Action:        "Case created automatically",
		},
		{
			UserName:      "<EMAIL>",
			DateTimeInUtc: baseTime.Add(-24 * time.Hour).Format(time.RFC3339),
			Action:        "Case assigned for review",
		},
		{
			UserName:      "<EMAIL>",
			DateTimeInUtc: baseTime.Add(-2 * time.Hour).Format(time.RFC3339),
			Action:        "Investigation started",
		},
	}

	// Generate mock alert details
	alertDetails := []*tss.AlertDetails{
		{
			AlertId:            "ALERT-" + uuid.NewUUID().String()[:8],
			Source:             "FBI Wanted Person",
			WatchlistSourceId:  "20",
			MatchType:          "Confirmed",
			MatchingAttributes: []string{"Name", "Date of Birth"},
			SourceIdentification: []*tss.SourceIdentification{
				{
					SourceIdentificationId:    "1",
					SourceIdentificationKey:   "Name",
					SourceIdentificationValue: "John Doe",
				},
				{
					SourceIdentificationId:    "2",
					SourceIdentificationKey:   "DOB",
					SourceIdentificationValue: "1990-01-01",
				},
			},
			WatchlistName: "FBI Most Wanted",
			AlertDecision: "Pending",
			Comments: []*tss.Comment{
				{
					UserName:      "<EMAIL>",
					DateTimeInUtc: baseTime.Add(-1 * time.Hour).Format(time.RFC3339),
					Comment:       "Reviewing match details for potential false positive",
				},
			},
		},
		{
			AlertId:            "ALERT-" + uuid.NewUUID().String()[:8],
			Source:             "OFAC SDN",
			WatchlistSourceId:  "15",
			MatchType:          "Probable",
			MatchingAttributes: []string{"Name"},
			SourceIdentification: []*tss.SourceIdentification{
				{
					SourceIdentificationId:    "3",
					SourceIdentificationKey:   "Name",
					SourceIdentificationValue: "John D",
				},
			},
			WatchlistName: "OFAC Specially Designated Nationals",
			AlertDecision: "NoMatch",
			Comments: []*tss.Comment{
				{
					UserName:      "<EMAIL>",
					DateTimeInUtc: baseTime.Add(-30 * time.Minute).Format(time.RFC3339),
					Comment:       "Confirmed as false positive - different person",
				},
			},
		},
	}

	return &tss.CaseDetails{
		CaseId:                           caseId,
		CaseCreationDateTimeInUtc:        baseTime.Add(-48 * time.Hour).Format(time.RFC3339),
		SourceSystemName:                 "Epifi Tech",
		SourceSystemCustomerCode:         customerCode,
		ApplicationRefNumber:             "APP" + uuid.NewUUID().String()[:8],
		CaseOf:                           "MainKYC",
		LinkedToSourceSystemCustomerCode: "",
		Relation:                         "",
		ScreeningProfile:                 "DEFAULT_PROFILE",
		ScreeningProfileName:             "Default Screening Profile",
		CustomerName:                     "Test Customer " + uuid.NewUUID().String()[:4],
		CaseType:                         "Initial",
		InitialScreeningMode:             "API",
		OnboardingDecision:               "Proceed",
		TotalAlertCount:                  2,
		ConfirmedAlertCount:              1,
		ProbableAlertCount:               1,
		PendingForDecision:               1,
		NoMatchCount:                     1,
		TrueMatchCount:                   0,
		CaseStage:                        "Investigation",
		CaseCategory:                     "Open",
		CurrentAssignee:                  "<EMAIL>",
		CaseClosureDateTimeInUtc:         "",
		FinalRemarks:                     "",
		CaseActions:                      caseActions,
		AlertDetails:                     alertDetails,
	}
}

func (s *TssService) sendResponse(w http.ResponseWriter, statusCode int, resp []byte) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	_, _ = w.Write(resp)
}
