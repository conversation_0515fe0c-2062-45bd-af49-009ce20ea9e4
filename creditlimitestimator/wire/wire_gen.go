// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	"github.com/epifi/gamma/creditlimitestimator"
	"github.com/epifi/gamma/creditlimitestimator/config"
)

// Injectors from wire.go:

func InitialiseCreditLimitEstimatorSvc(config2 *config.Config, palClient preapprovedloan.PreApprovedLoanClient, ffClient firefly.FireflyClient, creditVgClient creditline.CreditLineClient, userClient user.UsersClient, actorClient actor.ActorClient, userGroupClient group.GroupClient, bankCustomerClient bankcust.BankCustomerServiceClient) *creditlimitestimator.Service {
	service := creditlimitestimator.NewService(config2, palClient, ffClient, creditVgClient, userClient, actorClient, userGroupClient, bankCustomerClient)
	return service
}
