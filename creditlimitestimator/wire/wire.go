//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"

	actorPb "github.com/epifi/gamma/api/actor"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	ffPb "github.com/epifi/gamma/api/firefly"
	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	creditlineVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	"github.com/epifi/gamma/creditlimitestimator"
	"github.com/epifi/gamma/creditlimitestimator/config"
)

func InitialiseCreditLimitEstimatorSvc(config *config.Config, palClient preapprovedloanPb.PreApprovedLoanClient, ffClient ffPb.FireflyClient, creditVgClient creditlineVgPb.CreditLineClient, userClient userPb.UsersClient, actorClient actorPb.ActorClient, userGroupClient userGroupPb.GroupClient, bankCustomerClient bankCustPb.BankCustomerServiceClient) *creditlimitestimator.Service {
	wire.Build(creditlimitestimator.NewService)
	return &creditlimitestimator.Service{}
}
