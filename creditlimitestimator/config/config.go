package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.LIMIT_ESTIMATOR_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	return conf, nil
}
func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

type Config struct {
	Application   *application
	Logging       *cfg.Logging
	Server        *server
	SecureLogging *cfg.SecureLogging
	Flags         *flags
}

type application struct {
	Environment string
	Name        string
	ServerName  string
}

type server struct {
	Ports *cfg.ServerPorts
}

type flags struct {
	EnableVendorLimitCheckForPal bool
	EnableVendorLimitCheckForCc  bool
}
