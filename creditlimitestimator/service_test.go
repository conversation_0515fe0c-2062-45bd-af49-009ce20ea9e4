package creditlimitestimator

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/bankcust"
	bcMocks "github.com/epifi/gamma/api/bankcust/mocks"
	clePb "github.com/epifi/gamma/api/credit_limit_estimator"
	"github.com/epifi/gamma/api/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ffMocks "github.com/epifi/gamma/api/firefly/mocks"
	"github.com/epifi/gamma/api/preapprovedloan"
	preapprovedloanMocks "github.com/epifi/gamma/api/preapprovedloan/mocks"
	"github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	clMocks "github.com/epifi/gamma/api/vendorgateway/lending/creditline/mocks"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

func TestService_GetCreditCardConservativeLimit(t *testing.T) {
	type args struct {
		ctx context.Context
		req *clePb.GetCreditCardConservativeLimitRequest
	}
	ctr := gomock.NewController(t)
	mockFfClient := ffMocks.NewMockFireflyClient(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)
	mockBcClient := bcMocks.NewMockBankCustomerServiceClient(ctr)
	mockCreditLineClient := clMocks.NewMockCreditLineClient(ctr)
	mockPalClient := preapprovedloanMocks.NewMockPreApprovedLoanClient(ctr)
	var ccOfferRes = &firefly.GetCreditCardOffersResponse{
		Status: rpc.StatusOk(),
		Offers: []*firefly.CreditCardOffer{
			{
				Id:               "offer-1",
				ActorId:          "actor-1",
				VendorOfferId:    "",
				Vendor:           ffEnumsPb.Vendor_FEDERAL,
				OfferConstraints: &firefly.OfferConstraints{Limit: &moneyPb.Money{CurrencyCode: moneyPkg.RupeeCurrencyCode, Units: 50000}},
			},
		},
	}
	tests := []struct {
		name       string
		args       args
		setUpMocks func()
		want       *clePb.GetCreditCardConservativeLimitResponse
		wantErr    bool
	}{
		{
			name: "user has loan and cc from external lenders",
			args: args{
				ctx: context.Background(),
				req: &clePb.GetCreditCardConservativeLimitRequest{
					ActorId:          "actor-1",
					Vendor:           ffEnumsPb.Vendor_FEDERAL,
					ShouldCallVendor: true,
				},
			},
			setUpMocks: func() {
				mockFfClient.EXPECT().GetCreditCardOffers(gomock.Any(), gomock.Any()).Return(ccOfferRes, nil)
				mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&user.GetUserResponse{Status: rpc.StatusOk()}, nil)
				mockBcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankcust.GetBankCustomerResponse{Status: rpc.StatusOk()}, nil)
				mockCreditLineClient.EXPECT().FetchCombinedCreditLimit(gomock.Any(), gomock.Any()).Return(&creditline.FetchCombinedCreditLimitResponse{
					Status:                   rpc.StatusOk(),
					TotalLoanAmountAvailed:   nil,
					TotalCreditAmountAvailed: nil,
					LoanAmountLast_30Days: &moneyPb.Money{
						CurrencyCode: moneyPkg.RupeeCurrencyCode,
						Units:        -20000,
						Nanos:        0,
					},
					CreditAmountLast_30Days: &moneyPb.Money{
						CurrencyCode: moneyPkg.RupeeCurrencyCode,
						Units:        10000,
						Nanos:        0,
					},
				}, nil)
				mockPalClient.EXPECT().EstimateCreditUtilised(gomock.Any(), gomock.Any()).Return(&preapprovedloan.EstimateCreditUtilisedResponse{
					Status: rpc.StatusOk(),
					CreditUtilised: &moneyPb.Money{
						CurrencyCode: moneyPkg.RupeeCurrencyCode,
						Units:        0,
						Nanos:        0,
					},
				}, nil)
			},
			want: &clePb.GetCreditCardConservativeLimitResponse{
				Status: rpc.StatusOk(),
				ConservativeLimit: &moneyPb.Money{
					CurrencyCode: moneyPkg.RupeeCurrencyCode,
					Units:        20000,
					Nanos:        0,
				},
				OfferId: "offer-1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(nil, mockPalClient, mockFfClient, mockCreditLineClient, mockUserClient, nil, nil, mockBcClient)
			tt.setUpMocks()
			got, err := s.GetCreditCardConservativeLimit(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCreditCardConservativeLimit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCreditCardConservativeLimit() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetLoanConservativeLimit(t *testing.T) {
	type args struct {
		ctx context.Context
		req *clePb.GetLoanConservativeLimitRequest
	}
	ctr := gomock.NewController(t)
	mockFfClient := ffMocks.NewMockFireflyClient(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)
	mockBcClient := bcMocks.NewMockBankCustomerServiceClient(ctr)
	mockCreditLineClient := clMocks.NewMockCreditLineClient(ctr)
	mockPalClient := preapprovedloanMocks.NewMockPreApprovedLoanClient(ctr)
	tests := []struct {
		name       string
		args       args
		setUpMocks func()
		want       *clePb.GetLoanConservativeLimitResponse
		wantErr    bool
	}{
		{
			name: "user has loan and cc from external lenders",
			args: args{
				ctx: context.Background(),
				req: &clePb.GetLoanConservativeLimitRequest{
					ActorId:          "actor-1",
					Vendor:           preapprovedloan.Vendor_FEDERAL,
					ShouldCallVendor: true,
				},
			},
			setUpMocks: func() {
				mockPalClient.EXPECT().GetOfferDetails(gomock.Any(), gomock.Any()).Return(&preapprovedloan.GetOfferDetailsResponse{
					Status:  rpc.StatusOk(),
					OfferId: "offer-1",
					OfferInfo: &preapprovedloan.GetOfferDetailsResponse_OfferInfo{
						MaxLoanAmount: &moneyPb.Money{
							CurrencyCode: moneyPkg.RupeeCurrencyCode,
							Units:        50000,
							Nanos:        0,
						},
					},
				}, nil)
				mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&user.GetUserResponse{Status: rpc.StatusOk()}, nil)
				mockBcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankcust.GetBankCustomerResponse{Status: rpc.StatusOk()}, nil)
				mockCreditLineClient.EXPECT().FetchCombinedCreditLimit(gomock.Any(), gomock.Any()).Return(&creditline.FetchCombinedCreditLimitResponse{
					Status:                   rpc.StatusOk(),
					TotalLoanAmountAvailed:   nil,
					TotalCreditAmountAvailed: nil,
					LoanAmountLast_30Days: &moneyPb.Money{
						CurrencyCode: moneyPkg.RupeeCurrencyCode,
						Units:        -20000,
						Nanos:        0,
					},
					CreditAmountLast_30Days: &moneyPb.Money{
						CurrencyCode: moneyPkg.RupeeCurrencyCode,
						Units:        20000,
						Nanos:        0,
					},
				}, nil)
				mockFfClient.EXPECT().EstimateCreditUtilised(gomock.Any(), gomock.Any()).Return(&firefly.EstimateCreditUtilisedResponse{
					Status: rpc.StatusOk(),
					CreditUtilised: &moneyPb.Money{
						CurrencyCode: moneyPkg.RupeeCurrencyCode,
						Units:        0,
						Nanos:        0,
					},
				}, nil)
			},
			want: &clePb.GetLoanConservativeLimitResponse{
				Status: rpc.StatusOk(),
				ConservativeLimit: &moneyPb.Money{
					CurrencyCode: moneyPkg.RupeeCurrencyCode,
					Units:        30000,
					Nanos:        0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setUpMocks()
			s := NewService(nil, mockPalClient, mockFfClient, mockCreditLineClient, mockUserClient, nil, nil, mockBcClient)
			got, err := s.GetLoanConservativeLimit(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoanConservativeLimit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLoanConservativeLimit() got = %v, want %v", got, tt.want)
			}
		})
	}
}
