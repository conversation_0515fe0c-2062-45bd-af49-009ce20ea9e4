package creditlimitestimator

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	actorPb "github.com/epifi/gamma/api/actor"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	clePb "github.com/epifi/gamma/api/credit_limit_estimator"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	creditlineVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	"github.com/epifi/gamma/creditlimitestimator/config"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
)

var (
	palToFfVendorMap = map[preapprovedloanPb.Vendor]ffEnumsPb.Vendor{
		preapprovedloanPb.Vendor_FEDERAL: ffEnumsPb.Vendor_FEDERAL,
	}
	ffToPalVendorMap = map[ffEnumsPb.Vendor]preapprovedloanPb.Vendor{
		ffEnumsPb.Vendor_FEDERAL: preapprovedloanPb.Vendor_FEDERAL,
	}
	palToVgVendorMap = map[preapprovedloanPb.Vendor]commonvgpb.Vendor{
		preapprovedloanPb.Vendor_FEDERAL: commonvgpb.Vendor_FEDERAL_BANK,
	}
	ccToVgVendorMap = map[ffEnumsPb.Vendor]commonvgpb.Vendor{
		ffEnumsPb.Vendor_FEDERAL: commonvgpb.Vendor_FEDERAL_BANK,
	}
	breDate = &date.Date{
		Year:  2022,
		Month: 10,
		Day:   22,
	}
)

const productId = "EPIFI"

type Service struct {
	config                *config.Config
	preApprovedLoanClient preapprovedloanPb.PreApprovedLoanClient
	fireflyClient         ffPb.FireflyClient
	creditVgClient        creditlineVgPb.CreditLineClient
	userClient            userPb.UsersClient
	actorClient           actorPb.ActorClient
	userGroupClient       userGroupPb.GroupClient
	bankCustomerClient    bankCustPb.BankCustomerServiceClient
}

func NewService(config *config.Config, preApprovedLoanClient preapprovedloanPb.PreApprovedLoanClient, fireflyClient ffPb.FireflyClient,
	creditVgClient creditlineVgPb.CreditLineClient, userClient userPb.UsersClient, actorClient actorPb.ActorClient, userGroupClient userGroupPb.GroupClient,
	bankCustomerClient bankCustPb.BankCustomerServiceClient) *Service {
	return &Service{
		config:                config,
		preApprovedLoanClient: preApprovedLoanClient,
		fireflyClient:         fireflyClient,
		creditVgClient:        creditVgClient,
		userClient:            userClient,
		actorClient:           actorClient,
		userGroupClient:       userGroupClient,
		bankCustomerClient:    bankCustomerClient,
	}
}

// nolint:funlen
func (s *Service) GetCreditCardConservativeLimit(ctx context.Context, req *clePb.GetCreditCardConservativeLimitRequest) (*clePb.GetCreditCardConservativeLimitResponse, error) {
	var (
		res               = &clePb.GetCreditCardConservativeLimitResponse{}
		conservativeLimit *moneyPb.Money
		plAmountAvailed   = money.ZeroINR().GetPb()
		ccAmountAvailed   = money.ZeroINR().GetPb()
		err               error
	)

	// Fetch CC offer
	ccoRes, rpcErr := s.fireflyClient.GetCreditCardOffers(ctx, &ffPb.GetCreditCardOffersRequest{
		ActorId:     req.GetActorId(),
		Vendor:      req.GetVendor(),
		CardProgram: req.GetCardProgram(),
	})
	if err = epifigrpc.RPCError(ccoRes, rpcErr); err != nil {
		res.Status = ccoRes.GetStatus()
		if res.Status == nil {
			res.Status = rpc.StatusInternal()
		}
		return res, nil
	}

	if ccoRes.GetOffers() == nil {
		logger.Error(ctx, "no offers found", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}
	ccOffer := ccoRes.GetOffers()[0]

	// we will skip checking for limit via vendor and through other lending programs in case of secured card
	// skip other checks for FI approved users as well
	if ccOffer.GetCardProgram().GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_SECURED ||
		ccOffer.GetCardProgram().GetCardProgramSource() == types.CardProgramSource_CARD_PROGRAM_SOURCE_FI_BRE_APPROVED {
		res.Status = rpc.StatusOk()
		res.OfferId = ccOffer.GetId()
		res.CardProgram = ccOffer.GetCardProgram()
		res.ConservativeLimit = ccOffer.GetOfferConstraints().GetLimit()
		return res, nil
	}

	if req.GetShouldCallVendor() {
		userRes, userErr := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{ActorId: req.GetActorId()},
		})
		if te := epifigrpc.RPCError(userRes, userErr); te != nil {
			logger.Error(ctx, "error fetching user", zap.Error(te))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		bcRes, bcErr := s.bankCustomerClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
			Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{ActorId: req.GetActorId()},
		})
		if te := epifigrpc.RPCError(bcRes, bcErr); te != nil {
			logger.Error(ctx, "error fetching bank customer", zap.Error(te))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		creditLineRes, clErr := s.creditVgClient.FetchCombinedCreditLimit(ctx, &creditlineVgPb.FetchCombinedCreditLimitRequest{
			Header:       &commonvgpb.RequestHeader{Vendor: ccToVgVendorMap[req.GetVendor()]},
			CifNumber:    bcRes.GetBankCustomer().GetVendorCustomerId(),
			MobileNumber: userRes.GetUser().GetProfile().GetPhoneNumber(),
			ProductId:    productId,
			PanNumber:    userRes.GetUser().GetProfile().GetPAN(),
			BreDate:      breDate,
		})
		if te := epifigrpc.RPCError(creditLineRes, clErr); te != nil {
			logger.Error(ctx, "error fetching combined credit limit", zap.Error(te))
			res.Status = rpc.StatusInternal()
			return res, nil
		} else {
			// Need to use both CC and PL amounts in last 30 days since there is no realtime offer API used currently
			ccAmountAvailed = creditLineRes.GetCreditAmountLast_30Days()
			// negate PL amount since it comes as negative from vendor
			plAmountAvailed = creditLineRes.GetLoanAmountLast_30Days()
			if money.IsNegative(plAmountAvailed) {
				plAmountAvailed = money.Negate(plAmountAvailed)
			}

			if money.IsPositive(creditLineRes.GetTotalCreditAmountAvailed()) || money.IsPositive(creditLineRes.GetTotalLoanAmountAvailed()) {
				res.Status = rpc.StatusResourceExhaustedWithDebugMsg("user already availed a cc or pl")
				return res, nil
			}
		}
	}

	// Fetch credit utilised estimate
	palRes, rpcErr := s.preApprovedLoanClient.EstimateCreditUtilised(ctx, &preapprovedloanPb.EstimateCreditUtilisedRequest{
		ActorId: req.GetActorId(),
		Vendor:  ffToPalVendorMap[req.GetVendor()],
		LoanHeader: &preapprovedloanPb.LoanHeader{
			LoanProgram: preapprovedloanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      preapprovedloanPb.Vendor_FEDERAL,
		},
	})
	if err = epifigrpc.RPCError(palRes, rpcErr); err != nil {
		logger.Error(ctx, "error estimating loan credit utilised", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if money.IsPositive(palRes.GetCreditUtilised()) {
		res.Status = rpc.StatusResourceExhaustedWithDebugMsg("user already availed a pl")
		return res, nil
	}

	if money.Compare(palRes.GetCreditUtilised(), plAmountAvailed) == 1 {
		plAmountAvailed = palRes.GetCreditUtilised()
	}

	totalAmountAvailed, err := money.Sum(plAmountAvailed, ccAmountAvailed)
	if err != nil {
		logger.Error(ctx, "error adding amounts", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	conservativeLimit, err = money.Subtract(ccOffer.GetOfferConstraints().GetLimit(), totalAmountAvailed)
	if err != nil {
		logger.Error(ctx, "error subtracting amounts", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if !money.IsPositive(conservativeLimit) {
		res.Status = rpc.StatusResourceExhaustedWithDebugMsg("conservative limit is not positive")
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.ConservativeLimit = conservativeLimit
	res.OfferId = ccOffer.GetId()
	res.CardProgram = ccOffer.GetCardProgram()
	return res, nil
}

// nolint:funlen
func (s *Service) GetLoanConservativeLimit(ctx context.Context, req *clePb.GetLoanConservativeLimitRequest) (*clePb.GetLoanConservativeLimitResponse, error) {
	var (
		res               = &clePb.GetLoanConservativeLimitResponse{}
		conservativeLimit *moneyPb.Money
		plAmountAvailed   = money.ZeroINR().GetPb()
		ccAmountAvailed   = money.ZeroINR().GetPb()
		err               error
	)

	loRes, rpcErr := s.preApprovedLoanClient.GetOfferDetails(ctx, &preapprovedloanPb.GetOfferDetailsRequest{
		ActorId: req.GetActorId(),
		OfferId: req.GetOfferId(),
		LoanHeader: &preapprovedloanPb.LoanHeader{
			LoanProgram: preapprovedloanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			Vendor:      req.GetVendor(),
		},
	})
	if err = epifigrpc.RPCError(loRes, rpcErr); err != nil {
		logger.Error(ctx, "error fetching loan offers", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = loRes.GetStatus()
		if res.Status == nil {
			res.Status = rpc.StatusInternal()
		}
		return res, nil
	}

	loanOfferLimit := loRes.GetOfferInfo().GetMaxLoanAmount()

	if req.GetShouldCallVendor() {
		userRes, userErr := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{ActorId: req.GetActorId()},
		})
		if te := epifigrpc.RPCError(userRes, userErr); te != nil {
			logger.Error(ctx, "error fetching user", zap.Error(te))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		bcRes, bcErr := s.bankCustomerClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
			Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{ActorId: req.GetActorId()},
		})

		if te := epifigrpc.RPCError(bcRes, bcErr); te != nil {
			logger.Error(ctx, "error fetching bank customer", zap.Error(te))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		creditLineRes, clErr := s.creditVgClient.FetchCombinedCreditLimit(ctx, &creditlineVgPb.FetchCombinedCreditLimitRequest{
			Header:       &commonvgpb.RequestHeader{Vendor: palToVgVendorMap[req.GetVendor()]},
			CifNumber:    bcRes.GetBankCustomer().GetVendorCustomerId(),
			MobileNumber: userRes.GetUser().GetProfile().GetPhoneNumber(),
			ProductId:    productId,
			PanNumber:    userRes.GetUser().GetProfile().GetPAN(),
			BreDate:      breDate,
		})
		if te := epifigrpc.RPCError(creditLineRes, clErr); te != nil {
			logger.Error(ctx, "error fetching combined credit limit", zap.Error(te))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		if money.IsPositive(creditLineRes.GetTotalCreditAmountAvailed()) || money.IsPositive(creditLineRes.GetTotalLoanAmountAvailed()) {
			res.Status = rpc.StatusResourceExhaustedWithDebugMsg("user already availed a cc or pl")
			return res, nil
		}
		// Consider only CC amount availed in last 30 days since realtime API is used to fetch PL limit which already considers other PLs availed
		ccAmountAvailed = creditLineRes.GetCreditAmountLast_30Days()
	}

	// Fetch credit utilised estimate
	ffRes, rpcErr := s.fireflyClient.EstimateCreditUtilised(ctx, &ffPb.EstimateCreditUtilisedRequest{
		ActorId: req.GetActorId(),
		Vendor:  palToFfVendorMap[req.GetVendor()],
	})
	if err = epifigrpc.RPCError(ffRes, rpcErr); err != nil {
		logger.Error(ctx, "error estimating credit card limit utilised", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if money.IsPositive(ffRes.GetCreditUtilised()) {
		res.Status = rpc.StatusResourceExhaustedWithDebugMsg("user already availed a cc")
		return res, nil
	}

	// max of cc amount already availed from internal check and vendor call if done
	if money.Compare(ffRes.GetCreditUtilised(), ccAmountAvailed) == 1 {
		ccAmountAvailed = ffRes.GetCreditUtilised()
	}

	totalAmountAvailed, err := money.Sum(plAmountAvailed, ccAmountAvailed)
	if err != nil {
		logger.Error(ctx, "error adding amounts", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	conservativeLimit, err = money.Subtract(loanOfferLimit, totalAmountAvailed)
	if err != nil {
		logger.Error(ctx, "error subtracting amounts", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if !money.IsPositive(conservativeLimit) {
		res.Status = rpc.StatusResourceExhaustedWithDebugMsg("conservative limit is not positive")
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.ConservativeLimit = conservativeLimit
	return res, nil
}
