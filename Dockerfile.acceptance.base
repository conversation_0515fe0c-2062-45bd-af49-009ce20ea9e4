# Base image for running acceptance tests.
# Has dependencies for liveness and AWS
# Image definition should be updated and a new image should be built everytime
# dependencies change and the image should be uploaded to Github packages.
# Instructions for building and pushing the image
# 1. docker build -f Dockerfile.acceptance.base .
# 2. Read https://docs.github.com/en/packages/working-with-a-github-packages-registry/working-with-the-container-registry#authenticating-to-the-container-registry
# 3. Using above documentation, generate a github personal access token and add it to env variable with `export CR_PAT=YOUR_TOKEN`
# 4. Login to github packages. echo $CR_PAT | docker login ghcr.io -u <username> --password-stdin
# 5. Build docker image: docker build -f Dockerfile.acceptance.base .
# 6. Tag the image: e.g. docker tag 17eeee29fc23  ghcr.io/epifi/gamma/acceptance:<next higher number>
# 7. Push the image: docker push ghcr.io/epifi/gamma/acceptance:15
# 8. Update image version in Dockerfile.acceptance.opt so that the new image gets picked up

FROM golang:1.24

RUN apt-get update && \
    apt-get install -y \
        python3 \
        python3-pip \
        python3-setuptools \
        groff \
        less \
        ffmpeg \
    && pip3 install --upgrade pip \
    && apt-get clean

RUN pip3 --no-cache-dir install --upgrade awscli

RUN aws configure set aws_access_key_id a && \
	aws configure set aws_secret_access a && \
	aws configure set default_region_name a && \
	aws configure set default_output_format json && \
	aws configure set aws_secret_access_key a && \
	aws configure set region a

COPY . /go/src/github.com/epifi/gamma/
WORKDIR /go/src/github.com/epifi/gamma/

# run go install to build all packages especially third partites which are modified rarely with new versions.
RUN export GOBIN=/go/src/github.com/epifi/gamma/bin
RUN go install -v ./cmd/...
RUN unset GOBIN

# remove the source code directory
RUN rm -rf /go/src/github.com/epifi/gamma
