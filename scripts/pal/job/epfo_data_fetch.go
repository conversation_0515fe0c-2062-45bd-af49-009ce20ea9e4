package job

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	commsPb "github.com/epifi/gamma/api/comms"
	digitapVgPb "github.com/epifi/gamma/api/vendorgateway/lending/digitap"
	"github.com/epifi/gamma/scripts/pal/config"
)

type EpfoDataFetchJob struct {
	conf            *config.Config
	digitapVgClient digitapVgPb.DigitapServiceClient
	commsClient     commsPb.CommsClient
}

func NewEpfoDataFetchJob(
	conf *config.Config,
	digitapVgClient digitapVgPb.DigitapServiceClient,
	commsClient commsPb.CommsClient,
) *EpfoDataFetchJob {
	return &EpfoDataFetchJob{
		conf:            conf,
		digitapVgClient: digitapVgClient,
		commsClient:     commsClient,
	}
}

// EpfoInputItem represents a single item in the input JSON array
type EpfoInputItem struct {
	Mobile          string `json:"mobile,omitempty"`
	Uan             string `json:"uan,omitempty"`
	Dob             string `json:"dob,omitempty"` // Expected format: YYYY-MM-DD
	EmployeeName    string `json:"employee_name,omitempty"`
	EmployerName    string `json:"employer_name,omitempty"`
	Pan             string `json:"pan,omitempty"`
	NameMatchMethod string `json:"name_match_method,omitempty"` // Expected: "fuzzy" or "exact"
}

// EpfoDataFetchJobArgs contains args for the job itself
// Sample input:
// --JobName=EPFO_DATA_FETCH
// --Arguments='{"email":"<EMAIL>"}'
// --FilePath1='epfo_input_sample.csv'
type EpfoDataFetchJobArgs struct {
	Email string `json:"email"`
}

// EpfoDataInput holds input details for a single request
type EpfoDataInput struct {
	Item        *EpfoInputItem `json:"item"`
	ClientReqId string         `json:"client_req_id"`
}

// EpfoDataResult holds the result (input, output, error) for a single item
type EpfoDataResult struct {
	InputData  *EpfoDataInput                   `json:"input_data"`
	OutputData *digitapVgPb.UanAdvancedResponse `json:"output_data,omitempty"`
	Error      string                           `json:"error,omitempty"`
}

func (j *EpfoDataFetchJob) GetArgs() interface{} {
	return &EpfoDataFetchJobArgs{}
}

func (j *EpfoDataFetchJob) Run(ctx context.Context, args ...interface{}) error {
	jobArgs := args[0].(*EpfoDataFetchJobArgs)
	email := strings.ToLower(strings.TrimSpace(jobArgs.Email))
	if email == "" {
		return errors.New("email is required")
	}
	emailWhitelisted := false
	for _, recipient := range j.conf.EpfoDataRecipients {
		if strings.EqualFold(strings.ToLower(recipient), email) {
			emailWhitelisted = true
			break
		}
	}
	if !emailWhitelisted {
		return errors.Errorf("email '%s' is not whitelisted", email)
	}
	if len(args[1].([]string)) == 0 {
		return errors.New("no file args provided")
	}
	jobFilePath := args[1].([]string)[0]
	inputItems, err := readInputCSV(jobFilePath)
	if err != nil {
		return errors.Wrap(err, "error reading input CSV")
	}
	var results []*EpfoDataResult
	logger.Info(ctx, "Starting EPFO data fetch job",
		zap.Int("item_count", len(inputItems)), zap.String("target_email", email))
	for i := range inputItems {
		// Process items sequentially for now, can be parallelized later if needed
		clientReqId := uuid.NewString()
		result := &EpfoDataResult{
			InputData: &EpfoDataInput{
				Item:        inputItems[i],
				ClientReqId: clientReqId,
			},
		}
		epfoDataRes, dataErr := j.getEpfoDataForItem(ctx, clientReqId, inputItems[i])
		if dataErr != nil {
			result.Error = dataErr.Error()
		} else {
			result.OutputData = epfoDataRes
		}
		results = append(results, result)
	}
	err = j.sendResultsEmail(ctx, email, results)
	if err != nil {
		return errors.Wrap(err, "error sending results email")
	}
	logger.Info(ctx, "Sent email report")
	return nil
}

var inputFileColumnNames = []string{"Mobile", "Uan", "Dob", "EmployeeName", "EmployerName", "Pan", "NameMatchMethod"}

// readInputCSV reads the input CSV file and converts it into a list of EpfoInputItem structs
func readInputCSV(filePath string) (inputItems []*EpfoInputItem, err error) {
	if filePath == "" {
		return nil, errors.New("input CSV file path is empty")
	}
	file, err := os.OpenFile(filepath.Clean(filePath), os.O_RDONLY, 0600)
	if err != nil {
		return nil, errors.Wrap(err, "error opening input CSV file")
	}
	defer func() {
		closeErr := file.Close()
		if closeErr != nil {
			err = closeErr
		}
	}()
	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return nil, errors.Wrap(err, "error reading CSV")
	}
	if len(rows) <= 1 {
		return nil, errors.New("input CSV file is empty or has no data rows")
	}
	if len(rows) > 101 {
		return nil, errors.Errorf("too many input items provided (%d), max limit is 100", len(rows)-1)
	}
	columnIndices := make(map[string]int)
	for i, column := range rows[0] {
		columnIndices[strings.TrimSpace(column)] = i
	}
	for _, col := range inputFileColumnNames {
		if _, ok := columnIndices[col]; !ok {
			return nil, errors.Wrapf(err, "required column not found in CSV header: %s", col)
		}
	}
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		item := &EpfoInputItem{
			Mobile:          row[columnIndices["Mobile"]],
			Uan:             row[columnIndices["Uan"]],
			Dob:             row[columnIndices["Dob"]],
			EmployeeName:    row[columnIndices["EmployeeName"]],
			EmployerName:    row[columnIndices["EmployerName"]],
			Pan:             row[columnIndices["Pan"]],
			NameMatchMethod: row[columnIndices["NameMatchMethod"]],
		}
		inputItems = append(inputItems, item)
	}
	return inputItems, nil
}

func parseDateString(dateStr string) (*date.Date, error) {
	if dateStr == "" {
		return nil, nil // Optional field, not provided
	}
	t, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return nil, errors.Wrapf(err, "invalid date format for '%s', expected YYYY-MM-DD", dateStr)
	}
	return datetime.TimeToDateInLoc(t, datetime.IST), nil
}

func parseNameMatchMethod(methodStr string) (digitapVgPb.NameMatchMethod, error) {
	if methodStr == "" {
		return digitapVgPb.NameMatchMethod_NAME_MATCH_METHOD_UNSPECIFIED, nil
	}
	switch strings.ToLower(methodStr) {
	case "fuzzy":
		return digitapVgPb.NameMatchMethod_NAME_MATCH_METHOD_FUZZY, nil
	case "exact":
		return digitapVgPb.NameMatchMethod_NAME_MATCH_METHOD_EXACT, nil
	default:
		return digitapVgPb.NameMatchMethod_NAME_MATCH_METHOD_UNSPECIFIED, errors.Errorf("invalid name_match_method: '%s', expected 'fuzzy' or 'exact'", methodStr)
	}
}

func (j *EpfoDataFetchJob) getEpfoDataForItem(ctx context.Context, clientReqId string, item *EpfoInputItem) (*digitapVgPb.UanAdvancedResponse, error) {
	mobileNum, err := strconv.ParseUint(item.Mobile, 10, 64)
	if err != nil {
		return nil, errors.Wrap(err, "error parsing mobile number")
	}
	dob, err := parseDateString(item.Dob)
	if err != nil {
		return nil, errors.Wrap(err, "error parsing date of birth")
	}
	nameMatchMethodEnum, err := parseNameMatchMethod(item.NameMatchMethod)
	if err != nil {
		return nil, errors.Wrap(err, "error parsing name match method")
	}
	vgReq := &digitapVgPb.UanAdvancedRequest{
		ClientRefNum: clientReqId,
		Mobile: &common.PhoneNumber{
			CountryCode:    91, // Assuming Indian numbers
			NationalNumber: mobileNum,
		},
		Uan:             item.Uan,
		Dob:             dob,
		EmployeeName:    item.EmployeeName,
		EmployerName:    item.EmployerName,
		Pan:             item.Pan,
		NameMatchMethod: nameMatchMethodEnum,
	}

	// The job calls Digitap directly instead of using the EPFO service layer,
	// because the input can vary — mobile number, PAN, UAN, etc. — while the EPFO table
	// only allows look-ups by actor ID.
	// Digitap charges ₹1.80 for a successful match and ₹0.25 for a miss.
	// Since the job is expected to make only 200–400 calls a month,
	// the scale doesn't justify enhancing the EPFO service’s storage layer at this point.
	res, err := j.digitapVgClient.UanAdvanced(ctx, vgReq)
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, errors.Wrap(err, "error calling UanAdvanced")
	}
	return res, nil
}

func (j *EpfoDataFetchJob) sendResultsEmail(ctx context.Context, emailAddress string, results []*EpfoDataResult) error {
	var csvBuffer strings.Builder
	writer := csv.NewWriter(&csvBuffer)
	var rows [][]string
	var headers []string
	headers = append(headers, inputFileColumnNames...)
	headers = append(headers, []string{"ClientReqId", "RawResponse", "Error"}...)
	rows = append(rows, headers)
	for _, result := range results {
		item := result.InputData.Item
		row := []string{
			item.Mobile,
			item.Uan,
			item.Dob,
			item.EmployeeName,
			item.EmployerName,
			item.Pan,
			item.NameMatchMethod,

			// Extra columns in output over input
			result.InputData.ClientReqId,
			result.OutputData.GetRawResponse(),
			result.Error,
		}
		rows = append(rows, row)
	}
	if err := writer.WriteAll(rows); err != nil {
		return errors.Wrap(err, "error writing CSV rows")
	}
	reportTime := time.Now().In(datetime.IST).Format("2006-01-02 03:04:05PM")
	request := &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_GUARANTEED,
		Medium: commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{
			EmailId: emailAddress,
		},
		Message: &commsPb.SendMessageRequest_Email{
			Email: &commsPb.EmailMessage{
				FromEmailId:   "<EMAIL>",
				FromEmailName: "EPFO Data Report",
				EmailOption: &commsPb.EmailOption{
					Option: &commsPb.EmailOption_InternalReportEmailOption{
						InternalReportEmailOption: &commsPb.InternalReportEmailOption{
							EmailType: commsPb.EmailType_INTERNAL_REPORT_EMAIL,
							Option: &commsPb.InternalReportEmailOption_InternalReportEmailOptionV1{
								InternalReportEmailOptionV1: &commsPb.InternalReportEmailOptionsV1{
									ReportTitle:     "EPFO Data Report",
									ReportDate:      reportTime,
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
								},
							},
						},
					},
				},
				Attachment: []*commsPb.EmailMessage_Attachment{
					{
						FileContent:    []byte(csvBuffer.String()),
						FileName:       fmt.Sprintf("epfo_data_report_%s.csv", reportTime),
						Disposition:    commsPb.Disposition_ATTACHMENT,
						AttachmentType: "text/csv",
					},
				},
			},
		},
	}
	res, err := j.commsClient.SendMessage(ctx, request)
	if err = epifigrpc.RPCError(res, err); err != nil {
		return errors.Wrap(err, "error calling SendMessage")
	}
	logger.Info(ctx, "successfully sent EPFO data report email", zap.String(logger.MESSAGE_ID, res.GetMessageId()))
	return nil
}
