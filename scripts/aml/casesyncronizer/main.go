package main

/*
	Ask for a date range. If not available, then use last 30 days from now.
	Using the date range query the list cases AML API.
	Check and create cases in DB if not created.



Create a RPC in AML service whose task is to sync cases from AML vendor to our DB.
It takes as input owner, case categories, case types, start date and end date and calls the ListCases RPC.
If case categories are not provided, it uses all categories except unspecified.
If case types are not provided, it uses all types except unspecified.

*/
