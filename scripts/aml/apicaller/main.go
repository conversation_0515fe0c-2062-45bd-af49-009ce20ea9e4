package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/vendorgateway/aml"
)

var (
	apiName = flag.String("api", "", "AML API to call: InitiateScreening, ListCases, GetCaseDetails")
	apiArgs = flag.String("args", "", "JSON string containing API arguments")
)

// InitiateScreeningArgs represents arguments for InitiateScreening API
type InitiateScreeningArgs struct {
	Owner           string                 `json:"owner,omitempty"`
	UserId          string                 `json:"user_id"`
	VendorRequestId string                 `json:"vendor_request_id"`
	Product         string                 `json:"product,omitempty"`
	Purpose         string                 `json:"purpose,omitempty"`
	UserDetails     map[string]interface{} `json:"user_details"`
}

// ListCasesArgs represents arguments for ListCases API
type ListCasesArgs struct {
	Owner          string   `json:"owner,omitempty"`
	CaseCategories []string `json:"case_categories,omitempty"`
	CaseTypes      []string `json:"case_types,omitempty"`
	FromDateTime   string   `json:"from_date_time,omitempty"`
	ToDateTime     string   `json:"to_date_time,omitempty"`
}

// GetCaseDetailsArgs represents arguments for GetCaseDetails API
type GetCaseDetailsArgs struct {
	Owner  string `json:"owner,omitempty"`
	CaseId string `json:"case_id"`
}

func main() {
	flag.Parse()
	if *apiName == "" || *apiArgs == "" {
		fmt.Printf("Usage: %s -api <api_name> -args '<json_string>'\n", os.Args[0])
		fmt.Println("Available APIs:")
		fmt.Println("  InitiateScreening - Initiate screening a user's details against money-laundering activities")
		fmt.Println("  ListCases         - List cases with information for cases in given date range")
		fmt.Println("  GetCaseDetails    - Get details of a specific case ID")
		fmt.Println("\nExamples:")
		fmt.Printf("  %s -api InitiateScreening -args '{\"user_id\":\"test-123\",\"vendor_request_id\":\"req-123\",\"user_details\":{\"name\":{\"first_name\":\"John\",\"last_name\":\"Doe\"},\"nationality\":\"INDIAN\"}}'\n", os.Args[0])
		fmt.Printf("  %s -api ListCases -args '{\"owner\":\"EPIFI\"}'\n", os.Args[0])
		fmt.Printf("  %s -api GetCaseDetails -args '{\"case_id\":\"case-123\"}'\n", os.Args[0])
		fmt.Println("\nFlags:")
		flag.PrintDefaults()
		os.Exit(1)
	}

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	amlClient := aml.NewAmlClient(vgConn)

	// Call the appropriate API based on api_name
	switch *apiName {
	case "InitiateScreening":
		callInitiateScreeningAPI(ctx, amlClient, *apiArgs)
	case "ListCases":
		callListCasesAPI(ctx, amlClient, *apiArgs)
	case "GetCaseDetails":
		callGetCaseDetailsAPI(ctx, amlClient, *apiArgs)
	default:
		fmt.Printf("Unknown API name: %s. Valid options: InitiateScreening, ListCases, GetCaseDetails\n", *apiName)
		os.Exit(1)
	}
}

func callInitiateScreeningAPI(ctx context.Context, client aml.AmlClient, argsJSON string) {
	var args InitiateScreeningArgs
	if err := json.Unmarshal([]byte(argsJSON), &args); err != nil {
		fmt.Printf("Error parsing InitiateScreening args: %v\n", err)
		os.Exit(1)
	}

	if args.UserId == "" {
		fmt.Println("Error: user_id is required for InitiateScreening API")
		os.Exit(1)
	}

	if args.UserDetails == nil {
		fmt.Println("Error: user_details is required for InitiateScreening API")
		os.Exit(1)
	}

	fmt.Printf("AML InitiateScreening Request with User ID: %s\n", args.UserId)

	req := &aml.InitiateScreeningRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: vendorgateway.Vendor_TSS,
		},
		UserId:          args.UserId,
		VendorRequestId: args.VendorRequestId,
		UserDetails:     buildCustomerDetails(args.UserDetails),
	}

	// Set owner
	switch strings.ToUpper(args.Owner) {
	case "SG":
		req.Owner = commontypes.Owner_OWNER_STOCK_GUARDIAN_TSP
	default:
		req.Owner = commontypes.Owner_OWNER_EPIFI_TECH
	}

	// Set product
	switch strings.ToUpper(args.Product) {
	case "MF":
		req.Product = aml.Product_PRODUCT_MUTUAL_FUND
	case "USS":
		req.Product = aml.Product_PRODUCT_US_STOCKS
	case "SA":
		req.Product = aml.Product_PRODUCT_BANK_ACCOUNT
	case "LOAN":
		req.Product = aml.Product_PRODUCT_LOAN
	default:
		fmt.Printf("Unknown Product: %s\n", args.Product)
		return
	}

	// Set purpose
	switch strings.ToUpper(args.Purpose) {
	case "CONTINUOUS_SCREENING":
		req.Purpose = aml.Purpose_PURPOSE_CONTINUOUS_SCREENING
	default:
		req.Purpose = aml.Purpose_PURPOSE_INITIAL_SCREENING
	}

	resp, err := client.InitiateScreening(ctx, req)
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		fmt.Printf("Error in AML InitiateScreening for User ID: %v, err: %+v\n", args.UserId, rpcErr)
		return
	}

	// Pretty print response
	respJSON, err := json.MarshalIndent(resp, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling response to JSON: %v\n", err)
		return
	}
	fmt.Printf("AML InitiateScreening Response:\n%s\n", respJSON)
}

func callListCasesAPI(ctx context.Context, client aml.AmlClient, argsJSON string) {
	var args ListCasesArgs
	if err := json.Unmarshal([]byte(argsJSON), &args); err != nil {
		fmt.Printf("Error parsing ListCases args: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("AML ListCases Request\n")

	req := &aml.ListCasesRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: vendorgateway.Vendor_TSS,
		},
	}

	// Set owner
	switch strings.ToUpper(args.Owner) {
	case "SG":
		req.Owner = commontypes.Owner_OWNER_STOCK_GUARDIAN_TSP
	default:
		req.Owner = commontypes.Owner_OWNER_EPIFI_TECH
	}

	// Set case categories
	if len(args.CaseCategories) > 0 {
		for _, cat := range args.CaseCategories {
			switch strings.ToUpper(cat) {
			case "OPEN":
				req.CaseCategories = append(req.CaseCategories, aml.CaseCategory_CASE_CATEGORY_OPEN)
			case "PENDING":
				req.CaseCategories = append(req.CaseCategories, aml.CaseCategory_CASE_CATEGORY_PENDING)
			case "COMPLETED":
				req.CaseCategories = append(req.CaseCategories, aml.CaseCategory_CASE_CATEGORY_COMPLETED)
			}
		}
	} else {
		req.CaseCategories = []aml.CaseCategory{
			aml.CaseCategory_CASE_CATEGORY_OPEN,
			aml.CaseCategory_CASE_CATEGORY_PENDING,
			aml.CaseCategory_CASE_CATEGORY_COMPLETED,
		}
	}

	// Set case types
	if len(args.CaseTypes) > 0 {
		for _, typ := range args.CaseTypes {
			switch strings.ToUpper(typ) {
			case "INITIAL":
				req.CaseTypes = append(req.CaseTypes, aml.CaseType_CASE_TYPE_INITIAL)
			case "WATCHLIST_ADDED":
				req.CaseTypes = append(req.CaseTypes, aml.CaseType_CASE_TYPE_WATCHLIST_ADDED)
			default:
				fmt.Printf("Unknown Case Type: %s\n", typ)
				return
			}
		}
	} else {
		req.CaseTypes = []aml.CaseType{aml.CaseType_CASE_TYPE_INITIAL, aml.CaseType_CASE_TYPE_WATCHLIST_ADDED}
	}

	// Set date range - default to last 30 days if not provided
	if args.FromDateTime != "" && args.ToDateTime != "" {
		if fromTime, err := time.Parse(time.RFC3339, args.FromDateTime); err == nil {
			req.FromDateTime = timestamppb.New(fromTime)
		}
		if toTime, err := time.Parse(time.RFC3339, args.ToDateTime); err == nil {
			req.ToDateTime = timestamppb.New(toTime)
		}
	} else {
		// Default to last 30 days
		now := time.Now()
		req.ToDateTime = timestamppb.New(now)
		req.FromDateTime = timestamppb.New(now.AddDate(0, 0, -30))
	}

	resp, err := client.ListCases(ctx, req)
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		fmt.Printf("Error in AML ListCases, err: %+v\n", rpcErr)
		return
	}

	// Pretty print response
	respJSON, err := json.MarshalIndent(resp, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling response to JSON: %v\n", err)
		return
	}
	fmt.Printf("AML ListCases Response:\n%s\n", respJSON)
}

func callGetCaseDetailsAPI(ctx context.Context, client aml.AmlClient, argsJSON string) {
	var args GetCaseDetailsArgs
	if err := json.Unmarshal([]byte(argsJSON), &args); err != nil {
		fmt.Printf("Error parsing GetCaseDetails args: %v\n", err)
		os.Exit(1)
	}

	if args.CaseId == "" {
		fmt.Println("Error: case_id is required for GetCaseDetails API")
		os.Exit(1)
	}

	fmt.Printf("AML GetCaseDetails Request with Case ID: %s\n", args.CaseId)

	req := &aml.GetCaseDetailsRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: vendorgateway.Vendor_TSS,
		},
		CaseId: args.CaseId,
	}

	// Set owner
	switch strings.ToUpper(args.Owner) {
	case "SG":
		req.Owner = commontypes.Owner_OWNER_STOCK_GUARDIAN_TSP
	default:
		req.Owner = commontypes.Owner_OWNER_EPIFI_TECH
	}

	resp, err := client.GetCaseDetails(ctx, req)
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		fmt.Printf("Error in AML GetCaseDetails for Case ID: %v, err: %+v\n", args.CaseId, rpcErr)
		return
	}

	// Pretty print response
	respJSON, err := json.MarshalIndent(resp, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling response to JSON: %v\n", err)
		return
	}
	fmt.Printf("AML GetCaseDetails Response:\n%s\n", respJSON)
}

func buildCustomerDetails(userDetails map[string]interface{}) *aml.CustomerDetails {
	details := &aml.CustomerDetails{}

	// Set name (mandatory)
	if name, ok := userDetails["name"].(map[string]interface{}); ok {
		details.Name = &commontypes.Name{}
		if firstName, ok := name["first_name"].(string); ok {
			details.Name.FirstName = firstName
		}
		if lastName, ok := name["last_name"].(string); ok {
			details.Name.LastName = lastName
		}
	}

	details.Nationality = commontypes.Nationality_NATIONALITY_INDIAN // Default

	// Set optional fields
	if panNumber, ok := userDetails["pan_number"].(string); ok {
		details.PanNumber = panNumber
	}

	if email, ok := userDetails["email"].(string); ok {
		details.Email = email
	}

	return details
}
