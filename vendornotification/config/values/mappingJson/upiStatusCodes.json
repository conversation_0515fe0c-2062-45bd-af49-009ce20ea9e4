{"StatusCodes": [{"RawStatusCode": "M16", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1284", "StatusCodeDescriptionPayer": "AI MODEL DECLINE", "StatusCodeDescriptionPayee": "AI MODEL DECLINE", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "ZE", "CustomerReversalCode": "*", "StatusCode": "UPI1180", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO VPA by the PSP", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO VPA by the PSP", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XC", "CustomerReversalCode": "*", "StatusCode": "UPI1181", "StatusCodeDescriptionPayer": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (BENEFICIARY)", "StatusCodeDescriptionPayee": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "UB", "CustomerReversalCode": "*", "StatusCode": "UPI1182", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON BENEFICIARY SIDE", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON BENEFICIARY SIDE", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "U31", "CustomerStatusCode": "ZY", "CustomerReversalCode": "*", "StatusCode": "UPI1199", "StatusCodeDescriptionPayer": "INACTIVE OR DORMANT ACCOUNT (BENEFICIARY)", "StatusCodeDescriptionPayee": "INACTIVE OR DORMANT ACCOUNT (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "ZI", "CustomerReversalCode": "*", "StatusCode": "UPI1200", "StatusCodeDescriptionPayer": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY BENEFICIARY", "StatusCodeDescriptionPayee": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY BENEFICIARY", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XQ", "CustomerReversalCode": "*", "StatusCode": "UPI1201", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO CARDHOLDER (BENEFICIARY)", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO CARDHOLDER (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "PS", "CustomerReversalCode": "*", "StatusCode": "UPI1202", "StatusCodeDescriptionPayer": "MAXIMUM BALANCE EXCEEDED AS SET BY BENEFICIARY BANK", "StatusCodeDescriptionPayee": "MAXIMUM BALANCE EXCEEDED AS SET BY BENEFICIARY BANK", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "YF", "CustomerReversalCode": "*", "StatusCode": "UPI1203", "StatusCodeDescriptionPayer": "BENEFICIARY ACCOUNT BLOCKED/FROZEN", "StatusCodeDescriptionPayee": "BENEFICIARY ACCOUNT BLOCKED/FROZEN", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "B3", "CustomerReversalCode": "*", "StatusCode": "UPI1237", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO THE ACCOUNT (EXAMPLE: MIN<PERSON> ACCOUNT, PROPRIETOR ACCOUNT, LEGAL CASE AGAINST THIS ACCOUNT ETC., NRE (AS PER BANK’S POLICY))", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO THE ACCOUNT (EXAMPLE: MIN<PERSON> ACCOUNT, PROPRIETOR ACCOUNT, LEGAL CASE AGAINST THIS ACCOUNT ETC., NRE (AS PER BANK’S POLICY))", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "DF", "CustomerReversalCode": "*", "StatusCode": "UPI1238", "StatusCodeDescriptionPayer": "DUPLICAT<PERSON> RRN FOUND IN THE TRANSACTION. (BENEFICIARY)", "StatusCodeDescriptionPayee": "DUPLICAT<PERSON> RRN FOUND IN THE TRANSACTION. (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "K1", "CustomerReversalCode": "*", "StatusCode": "UPI1239", "StatusCodeDescriptionPayer": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY REMITTER", "StatusCodeDescriptionPayee": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY REMITTER", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "M2", "CustomerReversalCode": "*", "StatusCode": "UPI1240", "StatusCodeDescriptionPayer": "Amount limit exceeded – Amount is passed more than the limit amount", "StatusCodeDescriptionPayee": "Amount limit exceeded – Amount is passed more than the limit amount", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XI", "CustomerReversalCode": "*", "StatusCode": "UPI1241", "StatusCodeDescriptionPayer": "ACCOUNT DOES NOT EXIST (BENEFICIARY) ", "StatusCodeDescriptionPayee": "ACCOUNT DOES NOT EXIST (BENEFICIARY) ", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XU", "CustomerReversalCode": "*", "StatusCode": "UPI1242", "StatusCodeDescriptionPayer": "CUT-OFF IS IN PROCESS (BENEFICIARY)", "StatusCodeDescriptionPayee": "CUT-OFF IS IN PROCESS (BENEFICIARY)", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XW", "CustomerReversalCode": "*", "StatusCode": "UPI1243", "StatusCodeDescriptionPayer": "TRANSACTION CANNOT BE COMPLETED. COMPLIANCE VIOLATION (BENEFICIARY)", "StatusCodeDescriptionPayee": "TRANSACTION CANNOT BE COMPLETED. COMPLIANCE VIOLATION (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "Y1", "CustomerReversalCode": "*", "StatusCode": "UPI1244", "StatusCodeDescriptionPayer": "BENEFICIARY CBS OFFLINE", "StatusCodeDescriptionPayee": "BENEFICIARY CBS OFFLINE", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "U31", "CustomerStatusCode": "YD", "CustomerReversalCode": "*", "StatusCode": "UPI1245", "StatusCodeDescriptionPayer": "DO NOT HONOUR (BENEFICIARY)", "StatusCodeDescriptionPayee": "DO NOT HONOUR (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "Z6", "CustomerReversalCode": "*", "StatusCode": "UPI1246", "StatusCodeDescriptionPayer": "NUMBER OF PIN TRIES EXCEEDED", "StatusCodeDescriptionPayee": "NUMBER OF PIN TRIES EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "Z7", "CustomerReversalCode": "*", "StatusCode": "UPI1247", "StatusCodeDescriptionPayer": "TRANSACTION FREQUENCY LIMIT EXCEEDED AS SET BY REMITTING MEMBER", "StatusCodeDescriptionPayee": "TRANSACTION FREQUENCY LIMIT EXCEEDED AS SET BY REMITTING MEMBER", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "Z8", "CustomerReversalCode": "*", "StatusCode": "UPI1248", "StatusCodeDescriptionPayer": "PER TRANSACTION LIMIT EXCEEDED AS SET BY REMITTING MEMBER ", "StatusCodeDescriptionPayee": "PER TRANSACTION LIMIT EXCEEDED AS SET BY REMITTING MEMBER ", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "ZU", "CustomerReversalCode": "*", "StatusCode": "UPI1249", "StatusCodeDescriptionPayer": "LIMIT EXCEEDED FOR REMITTING BANK/ISSUING BANK", "StatusCodeDescriptionPayee": "LIMIT EXCEEDED FOR REMITTING BANK/ISSUING BANK", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "LC", "CustomerReversalCode": "*", "StatusCode": "UPI1250", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS CREDIT FROM BANK’S POOL/BGL ACCOUNT", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS CREDIT FROM BANK’S POOL/BGL ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "NO", "CustomerReversalCode": "*", "StatusCode": "UPI1251", "StatusCodeDescriptionPayer": "NO ORIGINAL REQUEST FOUND DURING DEBIT/CREDIT", "StatusCodeDescriptionPayee": "NO ORIGINAL REQUEST FOUND DURING DEBIT/CREDIT", "DeclineType": "Technical"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XY", "CustomerReversalCode": "*", "StatusCode": "UPI1252", "StatusCodeDescriptionPayer": "REMITTER CBS OFFLINE", "StatusCodeDescriptionPayee": "REMITTER CBS OFFLINE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U31", "CustomerStatusCode": "ZE", "CustomerReversalCode": "*", "StatusCode": "UPI1253", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO VPA by the PSP", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO VPA by the PSP", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XE", "CustomerReversalCode": "*", "StatusCode": "UPI1254", "StatusCodeDescriptionPayer": "INVALID AMOUNT (BENEFICIARY)", "StatusCodeDescriptionPayee": "INVALID AMOUNT (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "YG", "CustomerReversalCode": "*", "StatusCode": "UPI1255", "StatusCodeDescriptionPayer": "MERCHANT ERROR (PAYEE PSP)", "StatusCodeDescriptionPayee": "MERCHANT ERROR (PAYEE PSP)", "DeclineType": "Technical"}, {"RawStatusCode": "U31", "CustomerStatusCode": "Z5", "CustomerReversalCode": "*", "StatusCode": "UPI1256", "StatusCodeDescriptionPayer": "INVALID BENEFICIARY CREDENTIALS ", "StatusCodeDescriptionPayee": "INVALID BENEFICIARY CREDENTIALS ", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "UT", "CustomerReversalCode": "*", "StatusCode": "UPI1257", "StatusCodeDescriptionPayer": "REMITTER/ISSUER UNAVAILABLE (TIMEOUT)", "StatusCodeDescriptionPayee": "REMITTER/ISSUER UNAVAILABLE (TIMEOUT)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XS", "CustomerReversalCode": "*", "StatusCode": "UPI1258", "StatusCodeDescriptionPayer": "RESTRICTED CARD, DECLINE (BENEFICIARY) ", "StatusCodeDescriptionPayee": "RESTRICTED CARD, DECLINE (BENEFICIARY) ", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XB", "CustomerReversalCode": "*", "StatusCode": "UPI1259", "StatusCodeDescriptionPayer": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (REMITTER)", "StatusCodeDescriptionPayee": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XP", "CustomerReversalCode": "*", "StatusCode": "UPI1260", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO CARDHOLDER (REMITTER)", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO CARDHOLDER (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "B1", "CustomerReversalCode": "*", "StatusCode": "UPI1261", "StatusCodeDescriptionPayer": "REGISTERED M<PERSON><PERSON>LE NUMBER LINKED TO THE ACCOUNT HAS BEEN CHANGED/REMOVED", "StatusCodeDescriptionPayee": "REGISTERED M<PERSON><PERSON>LE NUMBER LINKED TO THE ACCOUNT HAS BEEN CHANGED/REMOVED", "DeclineType": ""}, {"RawStatusCode": "U31", "CustomerStatusCode": "XH", "CustomerReversalCode": "*", "StatusCode": "UPI1262", "StatusCodeDescriptionPayer": "ACCOUNT DOES NOT EXIST (REMITTER) ", "StatusCodeDescriptionPayee": "ACCOUNT DOES NOT EXIST (REMITTER) ", "DeclineType": "Technical"}, {"RawStatusCode": "U31", "CustomerStatusCode": "IR", "CustomerReversalCode": "*", "StatusCode": "UPI1263", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON REMITTER SIDE", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON REMITTER SIDE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U31", "CustomerStatusCode": "ZM", "CustomerReversalCode": "*", "StatusCode": "UPI1264", "StatusCodeDescriptionPayer": "INVALID MPIN", "StatusCodeDescriptionPayee": "INVALID MPIN", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XK", "CustomerReversalCode": "*", "StatusCode": "UPI1274", "StatusCodeDescriptionPayer": "REQUESTED FUNCTION NOT SUPPORTED (BENEFICIARY) ", "StatusCodeDescriptionPayee": "REQUESTED FUNCTION NOT SUPPORTED (BENEFICIARY) ", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "XM", "CustomerReversalCode": "*", "StatusCode": "UPI1275", "StatusCodeDescriptionPayer": "EXPIRED CARD, DECLINE (BENEFICIARY)", "StatusCodeDescriptionPayee": "EXPIRED CARD, DECLINE (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "Z9", "CustomerReversalCode": "*", "StatusCode": "UPI1276", "StatusCodeDescriptionPayer": "INSUFFICIENT FUNDS IN CUSTOMER (REMITTER) ACCOUNT", "StatusCodeDescriptionPayee": "INSUFFICIENT FUNDS IN CUSTOMER (REMITTER) ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "ZD", "CustomerReversalCode": "*", "StatusCode": "UPI1277", "StatusCodeDescriptionPayer": "VALIDATION ERROR", "StatusCodeDescriptionPayee": "VALIDATION ERROR", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "ZP", "CustomerReversalCode": "*", "StatusCode": "UPI1278", "StatusCodeDescriptionPayer": "BANKS AS BENEFICIARY NOT LIVE ON PARTICULAR TXN TYPE", "StatusCodeDescriptionPayee": "BANKS AS BENEFICIARY NOT LIVE ON PARTICULAR TXN TYPE", "DeclineType": "Business"}, {"RawStatusCode": "U31", "CustomerStatusCode": "ZX", "CustomerReversalCode": "*", "StatusCode": "UPI1279", "StatusCodeDescriptionPayer": "INACTIVE OR DORMANT ACCOUNT (REMITTER)", "StatusCodeDescriptionPayee": "INACTIVE OR DORMANT ACCOUNT (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "YG", "CustomerReversalCode": "*", "StatusCode": "UPI1183", "StatusCodeDescriptionPayer": "MERCHANT ERROR (PAYEE PSP)", "StatusCodeDescriptionPayee": "MERCHANT ERROR (PAYEE PSP)", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "UP", "CustomerReversalCode": "*", "StatusCode": "UPI1190", "StatusCodeDescriptionPayer": "PSP TIME-OUT", "StatusCodeDescriptionPayee": "PSP TIME-OUT", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "U29", "CustomerStatusCode": "DF", "CustomerReversalCode": "*", "StatusCode": "UPI1191", "StatusCodeDescriptionPayer": "DUPLICAT<PERSON> RRN FOUND IN THE TRANSACTION (BENEFICIARY)", "StatusCodeDescriptionPayee": "DUPLICAT<PERSON> RRN FOUND IN THE TRANSACTION (BENEFICIARY)", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "U29", "CustomerStatusCode": "ZH", "CustomerReversalCode": "*", "StatusCode": "UPI1192", "StatusCodeDescriptionPayer": "INVALID VIRTUAL ADDRESS", "StatusCodeDescriptionPayee": "INVALID VIRTUAL ADDRESS", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "UX", "CustomerReversalCode": "*", "StatusCode": "UPI1193", "StatusCodeDescriptionPayer": "EXPIRED VIRTUAL ADDRESS", "StatusCodeDescriptionPayee": "EXPIRED VIRTUAL ADDRESS", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "RA", "CustomerReversalCode": "*", "StatusCode": "UPI1194", "StatusCodeDescriptionPayer": "PAYER AND PAYEE ACCOUNT SHOULD NOT BE EQUAL", "StatusCodeDescriptionPayee": "PAYER AND PAYEE ACCOUNT SHOULD NOT BE EQUAL", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "XQ", "CustomerReversalCode": "*", "StatusCode": "UPI1195", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO CARDHOLDER (BENEFICIARY)", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO CARDHOLDER (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "YD", "CustomerReversalCode": "*", "StatusCode": "UPI1205", "StatusCodeDescriptionPayer": "DO NOT HONOUR (BENEFICIARY)", "StatusCodeDescriptionPayee": "DO NOT HONOUR (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "ZI", "CustomerReversalCode": "*", "StatusCode": "UPI1206", "StatusCodeDescriptionPayer": "SUSPECTED FRAUD, DECLINE/TRANSACTIONS DECLINED BASED ON RISK SCORE BY BENEFICIARY", "StatusCodeDescriptionPayee": "SUSPECTED FRAUD, DECLINE/TRANSACTIONS DECLINED BASED ON RISK SCORE BY BENEFICIARY", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "B3", "CustomerReversalCode": "*", "StatusCode": "UPI1208", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO THE ACCOUNT (EXAMPLE: MIN<PERSON> ACCOUNT, PROPRIETOR ACCOUNT, LEGAL CASE AGAINST THIS ACCOUNT ETC., NRE (AS PER BANK’S POLICY))", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO THE ACCOUNT (EXAMPLE: MIN<PERSON> ACCOUNT, PROPRIETOR ACCOUNT, LEGAL CASE AGAINST THIS ACCOUNT ETC., NRE (AS PER BANK’S POLICY))", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "K1", "CustomerReversalCode": "*", "StatusCode": "UPI1209", "StatusCodeDescriptionPayer": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY REMITTER", "StatusCodeDescriptionPayee": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY REMITTER", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "SA", "CustomerReversalCode": "*", "StatusCode": "UPI1210", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED FOR THIS A/C TYPE (OD)", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED FOR THIS A/C TYPE (OD)", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "XV", "CustomerReversalCode": "*", "StatusCode": "UPI1211", "StatusCodeDescriptionPayer": "TRANSACTION CANNOT BE COMPLETED. COMPLIANCE VIOLATION (REMITTER)", "StatusCodeDescriptionPayee": "TRANSACTION CANNOT BE COMPLETED. COMPLIANCE VIOLATION (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "YF", "CustomerReversalCode": "*", "StatusCode": "UPI1212", "StatusCodeDescriptionPayer": "BENEFICIARY ACCOUNT BLOCKED/FROZEN", "StatusCodeDescriptionPayee": "BENEFICIARY ACCOUNT BLOCKED/FROZEN", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "Z8", "CustomerReversalCode": "*", "StatusCode": "UPI1213", "StatusCodeDescriptionPayer": "PER TRANSACTION LIMIT EXCEEDED AS SET BY REMITTING MEMBER ", "StatusCodeDescriptionPayee": "PER TRANSACTION LIMIT EXCEEDED AS SET BY REMITTING MEMBER ", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "ZG", "CustomerReversalCode": "*", "StatusCode": "UPI1214", "StatusCodeDescriptionPayer": "VPA RESTRICTED BY CUSTOMER", "StatusCodeDescriptionPayee": "VPA RESTRICTED BY CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "ZU", "CustomerReversalCode": "*", "StatusCode": "UPI1215", "StatusCodeDescriptionPayer": "LIMIT EXCEEDED FOR REMITTING BANK/ISSUING BANK", "StatusCodeDescriptionPayee": "LIMIT EXCEEDED FOR REMITTING BANK/ISSUING BANK", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "Z9", "CustomerReversalCode": "*", "StatusCode": "UPI1216", "StatusCodeDescriptionPayer": "INSUFFICIENT FUNDS IN CUSTOMER (REMITTER) ACCOUNT", "StatusCodeDescriptionPayee": "INSUFFICIENT FUNDS IN CUSTOMER (REMITTER) ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "XD", "CustomerReversalCode": "*", "StatusCode": "UPI1217", "StatusCodeDescriptionPayer": "INVALID AMOUNT (REMITTER)", "StatusCodeDescriptionPayee": "INVALID AMOUNT (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "XI", "CustomerReversalCode": "*", "StatusCode": "UPI1218", "StatusCodeDescriptionPayer": "ACCOUNT DOES NOT EXIST (BENEFICIARY) ", "StatusCodeDescriptionPayee": "ACCOUNT DOES NOT EXIST (BENEFICIARY) ", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "XB", "CustomerReversalCode": "*", "StatusCode": "UPI1219", "StatusCodeDescriptionPayer": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (REMITTER)", "StatusCodeDescriptionPayee": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "XY", "CustomerReversalCode": "*", "StatusCode": "UPI1220", "StatusCodeDescriptionPayer": "REMITTER CBS OFFLINE", "StatusCodeDescriptionPayee": "REMITTER CBS OFFLINE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U29", "CustomerStatusCode": "ZM", "CustomerReversalCode": "*", "StatusCode": "UPI1221", "StatusCodeDescriptionPayer": "INVALID MPIN", "StatusCodeDescriptionPayee": "INVALID MPIN", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "Z7", "CustomerReversalCode": "*", "StatusCode": "UPI1222", "StatusCodeDescriptionPayer": "TRANSACTION FREQUENCY LIMIT EXCEEDED AS SET BY REMITTING MEMBER", "StatusCodeDescriptionPayee": "TRANSACTION FREQUENCY LIMIT EXCEEDED AS SET BY REMITTING MEMBER", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "XP", "CustomerReversalCode": "*", "StatusCode": "UPI1223", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO CARDHOLDER (REMITTER)", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO CARDHOLDER (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "IR", "CustomerReversalCode": "*", "StatusCode": "UPI1224", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON REMITTER SIDE", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON REMITTER SIDE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U30", "CustomerStatusCode": "IR", "CustomerReversalCode": "*", "StatusCode": "UPI1283", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON REMITTER SIDE", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON REMITTER SIDE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U29", "CustomerStatusCode": "TM", "CustomerReversalCode": "*", "StatusCode": "UPI1225", "StatusCodeDescriptionPayer": "COLLECT REQUEST IS DECLINED AS REQUESTOR IS BLOCKED BY CUSTOMER", "StatusCodeDescriptionPayee": "COLLECT REQUEST IS DECLINED AS REQUESTOR IS BLOCKED BY CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "CA", "CustomerReversalCode": "*", "StatusCode": "UPI1265", "StatusCodeDescriptionPayer": "COMPLIANCE ERROR CODE FOR ACQUIRER", "StatusCodeDescriptionPayee": "COMPLIANCE ERROR CODE FOR ACQUIRER", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "UB", "CustomerReversalCode": "*", "StatusCode": "UPI1266", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON BENEFICIARY SIDE", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON BENEFICIARY SIDE", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "X1", "CustomerReversalCode": "*", "StatusCode": "UPI1267", "StatusCodeDescriptionPayer": "RESPONSE NOT RECEIVED WITHIN TAT AS SET BY PAYEE", "StatusCodeDescriptionPayee": "RESPONSE NOT RECEIVED WITHIN TAT AS SET BY PAYEE", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "XC", "CustomerReversalCode": "*", "StatusCode": "UPI1268", "StatusCodeDescriptionPayer": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (BENEFICIARY) ", "StatusCodeDescriptionPayee": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (BENEFICIARY) ", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "YE", "CustomerReversalCode": "*", "StatusCode": "UPI1269", "StatusCodeDescriptionPayer": "REMITTING ACCOUNT BLOCKED/FROZEN", "StatusCodeDescriptionPayee": "REMITTING ACCOUNT BLOCKED/FROZEN", "DeclineType": "Business"}, {"RawStatusCode": "U29", "CustomerStatusCode": "Z6", "CustomerReversalCode": "*", "StatusCode": "UPI1270", "StatusCodeDescriptionPayer": "NUMBER OF PIN TRIES EXCEEDED", "StatusCodeDescriptionPayee": "NUMBER OF PIN TRIES EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZY", "CustomerReversalCode": "*", "StatusCode": "UPI100", "StatusCodeDescriptionPayer": "INACTIVE OR DORMANT ACCOUNT (BENEFICIARY)", "StatusCodeDescriptionPayee": "INACTIVE OR DORMANT ACCOUNT (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZX", "CustomerReversalCode": "*", "StatusCode": "UPI101", "StatusCodeDescriptionPayer": "INACTIVE OR DORMANT ACCOUNT (REMITTER)", "StatusCodeDescriptionPayee": "INACTIVE OR DORMANT ACCOUNT (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZV", "CustomerReversalCode": "*", "StatusCode": "UPI102", "StatusCodeDescriptionPayer": "INVALID OTP", "StatusCodeDescriptionPayee": "INVALID OTP", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZU", "CustomerReversalCode": "*", "StatusCode": "UPI103", "StatusCodeDescriptionPayer": "LIMIT EXCEEDED FOR REMITTING BANK/ISSUING BANK", "StatusCodeDescriptionPayee": "LIMIT EXCEEDED FOR REMITTING BANK/ISSUING BANK", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZT", "CustomerReversalCode": "*", "StatusCode": "UPI104", "StatusCodeDescriptionPayer": "OTP TRANSACTION LIMIT EXCEEDED", "StatusCodeDescriptionPayee": "OTP TRANSACTION LIMIT EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZT", "CustomerReversalCode": "*", "StatusCode": "UPI105", "StatusCodeDescriptionPayer": "NUMBER OF OTP'S TRIES HAS BEEN EXCEEDED", "StatusCodeDescriptionPayee": "NUMBER OF OTP'S TRIES HAS BEEN EXCEEDE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZS", "CustomerReversalCode": "*", "StatusCode": "UPI106", "StatusCodeDescriptionPayer": "OTP EXPIRED", "StatusCodeDescriptionPayee": "OTP EXPIRED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZS", "CustomerReversalCode": "*", "StatusCode": "UPI107", "StatusCodeDescriptionPayer": "OTP TIME EXPIRED", "StatusCodeDescriptionPayee": "OTP TIME EXPIRED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZR", "CustomerReversalCode": "*", "StatusCode": "UPI108", "StatusCodeDescriptionPayer": "INVALID / INCORRECT OTP", "StatusCodeDescriptionPayee": "INVALID / INCORRECT OTP", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZR", "CustomerReversalCode": "*", "StatusCode": "UPI109", "StatusCodeDescriptionPayer": "INVALID / INCORRECT OTP", "StatusCodeDescriptionPayee": "INVALID / INCORRECT OTP", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZQ", "CustomerReversalCode": "*", "StatusCode": "UPI110", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS REVERSAL", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS REVERSAL", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZP", "CustomerReversalCode": "*", "StatusCode": "UPI111", "StatusCodeDescriptionPayer": "BANKS AS BENEFICIARY NOT LIVE ON PARTICULAR TXN TYPE", "StatusCodeDescriptionPayee": "BANKS AS BENEFICIARY NOT LIVE ON PARTICULAR TXN TYPE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZO", "CustomerReversalCode": "*", "StatusCode": "UPI112", "StatusCodeDescriptionPayer": "FUNCTIONALITY NOT YET AVAILABLE FOR CUSTOMER THROUGH THE PAYEE PSP", "StatusCodeDescriptionPayee": "FUNCTIONALITY NOT YET AVAILABLE FOR CUSTOMER THROUGH THE PAYEE PSP", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZN", "CustomerReversalCode": "*", "StatusCode": "UPI113", "StatusCodeDescriptionPayer": "FUNCTIONALITY NOT YET AVAILABLE FOR MERCHANT THROUGH THE ACQUIRING BANK", "StatusCodeDescriptionPayee": "FUNCTIONALITY NOT YET AVAILABLE FOR MERCHANT THROUGH THE ACQUIRING BANK", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZM", "CustomerReversalCode": "*", "StatusCode": "UPI114", "StatusCodeDescriptionPayer": "INVALID / INCORRECT MPIN", "StatusCodeDescriptionPayee": "INVALID / INCORRECT MPIN", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZM", "CustomerReversalCode": "*", "StatusCode": "UPI115", "StatusCodeDescriptionPayer": "INVALID MPIN", "StatusCodeDescriptionPayee": "INVALID MPIN", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZL", "CustomerReversalCode": "*", "StatusCode": "UPI116", "StatusCodeDescriptionPayer": "RECEIVED LATE RESPONSE", "StatusCodeDescriptionPayee": "RECEIVED LATE RESPONSE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZK", "CustomerReversalCode": "*", "StatusCode": "UPI117", "StatusCodeDescriptionPayer": "REMITTE<PERSON> SWITCH IS INOPERATIVE/NODE OFFLINE", "StatusCodeDescriptionPayee": "REMITTE<PERSON> SWITCH IS INOPERATIVE/NODE OFFLINE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZJ", "CustomerReversalCode": "*", "StatusCode": "UPI118", "StatusCodeDescriptionPayer": "BENEFICIARY OR ACQUIRING SWITCH IS INOPERATIVE/NODE OFFLINE", "StatusCodeDescriptionPayee": "BENEFICIARY OR ACQUIRING SWITCH IS INOPERATIVE/NODE OFFLINE", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZI", "CustomerReversalCode": "*", "StatusCode": "UPI119", "StatusCodeDescriptionPayer": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY BENEFICIARY", "StatusCodeDescriptionPayee": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY BENEFICIARY", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZH", "CustomerReversalCode": "*", "StatusCode": "UPI120", "StatusCodeDescriptionPayer": "INVALID VIRTUAL ADDRESS", "StatusCodeDescriptionPayee": "INVALID VIRTUAL ADDRESS", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZG", "CustomerReversalCode": "*", "StatusCode": "UPI121", "StatusCodeDescriptionPayer": "VPA RESTRICTED BY CUSTOMER", "StatusCodeDescriptionPayee": "VPA RESTRICTED BY CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZF", "CustomerReversalCode": "*", "StatusCode": "UPI122", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO DEVICE", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO DEVICE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZE", "CustomerReversalCode": "*", "StatusCode": "UPI123", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO VPA by the PSP", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO VPA by the PSP", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZD", "CustomerReversalCode": "*", "StatusCode": "UPI124", "StatusCodeDescriptionPayer": "VALIDATION ERROR", "StatusCodeDescriptionPayee": "VALIDATION ERROR", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZC", "CustomerReversalCode": "*", "StatusCode": "UPI125", "StatusCodeDescriptionPayer": "ACQUIRER/BENEFICIARY UNAVAILABLE", "StatusCodeDescriptionPayee": "ACQUIRER/BENEFICIARY UNAVAILABLE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZB", "CustomerReversalCode": "*", "StatusCode": "UPI126", "StatusCodeDescriptionPayer": "INVALID MERCHANT (PAYEE PSP)", "StatusCodeDescriptionPayee": "INVALID MERCHANT (PAYEE PSP)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ZA", "CustomerReversalCode": "*", "StatusCode": "UPI127", "StatusCodeDescriptionPayer": "TRANSACTION DECLINED BY CUSTOMER", "StatusCodeDescriptionPayee": "TRANSACTION DECLINED BY CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "Z9", "CustomerReversalCode": "*", "StatusCode": "UPI128", "StatusCodeDescriptionPayer": "INSUFFICIENT FUNDS IN CUSTOMER (REMITTER) ACCOUNT", "StatusCodeDescriptionPayee": "INSUFFICIENT FUNDS IN CUSTOMER (REMITTER) ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "Z8", "CustomerReversalCode": "*", "StatusCode": "UPI129", "StatusCodeDescriptionPayer": "PER TRANSACTION LIMIT EXCEEDED AS SET BY REMITTING MEMBER", "StatusCodeDescriptionPayee": "PER TRANSACTION LIMIT EXCEEDED AS SET BY REMITTING MEMBER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "Z7", "CustomerReversalCode": "*", "StatusCode": "UPI130", "StatusCodeDescriptionPayer": "TRANSACTION FREQUENCY LIMIT EXCEEDED AS SET BY REMITTING MEMBER", "StatusCodeDescriptionPayee": "TRANSACTION FREQUENCY LIMIT EXCEEDED AS SET BY REMITTING MEMBER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "Z6", "CustomerReversalCode": "*", "StatusCode": "UPI131", "StatusCodeDescriptionPayer": "NO OF PIN TRIES EXCEEDED", "StatusCodeDescriptionPayee": "NO OF PIN TRIES EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "Z6", "CustomerReversalCode": "*", "StatusCode": "UPI132", "StatusCodeDescriptionPayer": "NUMBER OF PIN TRIES EXCEEDED", "StatusCodeDescriptionPayee": "NUMBER OF PIN TRIES EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "Z5", "CustomerReversalCode": "*", "StatusCode": "UPI133", "StatusCodeDescriptionPayer": "INVALID BENEFICIARY CREDENTIALS", "StatusCodeDescriptionPayee": "INVALID BENEFICIARY CREDENTIALS", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "YI", "CustomerReversalCode": "*", "StatusCode": "UPI134", "StatusCodeDescriptionPayer": "INVALID RESPONSE CODE", "StatusCodeDescriptionPayee": "INVALID RESPONSE CODE", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "YH", "CustomerReversalCode": "*", "StatusCode": "UPI135", "StatusCodeDescriptionPayer": "MERCHANT ERROR(ACQUIRING BANK)", "StatusCodeDescriptionPayee": "MERCHANT ERROR(ACQUIRING BANK)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "YG", "CustomerReversalCode": "*", "StatusCode": "UPI136", "StatusCodeDescriptionPayer": "MERCHANT ERROR (PAYEE PSP)", "StatusCodeDescriptionPayee": "MERCHANT ERROR (PAYEE PSP)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "YF", "CustomerReversalCode": "*", "StatusCode": "UPI137", "StatusCodeDescriptionPayer": "BENEFICIARY ACCOUNT BLOCKED/FROZEN", "StatusCodeDescriptionPayee": "BENEFICIARY ACCOUNT BLOCKED/FROZEN", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "YE", "CustomerReversalCode": "*", "StatusCode": "UPI138", "StatusCodeDescriptionPayer": "REMITTING ACCOUNT BLOCKED/FROZEN", "StatusCodeDescriptionPayee": "REMITTING ACCOUNT BLOCKED/FROZEN", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "YD", "CustomerReversalCode": "*", "StatusCode": "UPI139", "StatusCodeDescriptionPayer": "DO NOT HONOUR (BENEFICIARY)", "StatusCodeDescriptionPayee": "DO NOT HONOUR (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "YC", "CustomerReversalCode": "*", "StatusCode": "UPI140", "StatusCodeDescriptionPayer": "DO NOT HONOUR (REMITTER)", "StatusCodeDescriptionPayee": "DO NOT HONOUR (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "YB", "CustomerReversalCode": "*", "StatusCode": "UPI141", "StatusCodeDescriptionPayer": "LOST OR STOLEN CARD (BENEFICIARY)", "StatusCodeDescriptionPayee": "LOST OR STOLEN CARD (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "YA", "CustomerReversalCode": "*", "StatusCode": "UPI142", "StatusCodeDescriptionPayer": "LOST OR STOLEN CARD (REMITTER)", "StatusCodeDescriptionPayee": "LOST OR STOLEN CARD (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XZ", "CustomerReversalCode": "*", "StatusCode": "UPI143", "StatusCodeDescriptionPayer": "BENEFICIARY CBS OFFLINE", "StatusCodeDescriptionPayee": "BENEFICIARY CBS OFFLINE", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "XY", "CustomerReversalCode": "*", "StatusCode": "UPI144", "StatusCodeDescriptionPayer": "REMITTER CBS OFFLINE", "StatusCodeDescriptionPayee": "REMITTER CBS OFFLINE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "*", "CustomerStatusCode": "XX", "CustomerReversalCode": "*", "StatusCode": "UPI145", "StatusCodeDescriptionPayer": "NO FINANCIAL ADDRESS RECORD FOUND", "StatusCodeDescriptionPayee": "NO FINANCIAL ADDRESS RECORD FOUND", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XW", "CustomerReversalCode": "*", "StatusCode": "UPI146", "StatusCodeDescriptionPayer": "TRANSACTION CANNOT BE COMPLETED COMPLIANCE VIOLATION (BENEFICIARY)", "StatusCodeDescriptionPayee": "TRANSACTION CANNOT BE COMPLETED COMPLIANCE VIOLATION (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XV", "CustomerReversalCode": "*", "StatusCode": "UPI147", "StatusCodeDescriptionPayer": "TRANSACTION CANNOT BE COMPLETED COMPLIANCE VIOLATION (REMITTER)", "StatusCodeDescriptionPayee": "TRANSACTION CANNOT BE COMPLETED COMPLIANCE VIOLATION (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XU", "CustomerReversalCode": "*", "StatusCode": "UPI148", "StatusCodeDescriptionPayer": "CUT-OFF IS IN PROCESS (BENEFICIARY)", "StatusCodeDescriptionPayee": "CUT-OFF IS IN PROCESS (BENEFICIARY)", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "*", "CustomerStatusCode": "XT", "CustomerReversalCode": "*", "StatusCode": "UPI149", "StatusCodeDescriptionPayer": "CUT-OFF IS IN PROCESS (REMITTER)", "StatusCodeDescriptionPayee": "CUT-OFF IS IN PROCESS (REMITTER)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "*", "CustomerStatusCode": "XS", "CustomerReversalCode": "*", "StatusCode": "UPI150", "StatusCodeDescriptionPayer": "RESTRICTED CARD, DECLINE (BENEFICIARY)", "StatusCodeDescriptionPayee": "RESTRICTED CARD, DECLINE (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XR", "CustomerReversalCode": "*", "StatusCode": "UPI151", "StatusCodeDescriptionPayer": "RESTRICTED CARD", "StatusCodeDescriptionPayee": "RESTRICTED CARD", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XR", "CustomerReversalCode": "*", "StatusCode": "UPI152", "StatusCodeDescriptionPayer": "RESTRICTED CARD, DECLINE (REMITTER)", "StatusCodeDescriptionPayee": "RESTRICTED CARD, DECLINE (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XQ", "CustomerReversalCode": "*", "StatusCode": "UPI153", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO CARDHOLDER (BENEFICIARY)", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO CARDHOLDER (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XP", "CustomerReversalCode": "*", "StatusCode": "UPI154", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO CARDHOLDER (REMITTER)", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO CARDHOLDER (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XO", "CustomerReversalCode": "*", "StatusCode": "UPI155", "StatusCodeDescriptionPayer": "NO CARD RECORD (BENEFICIARY)", "StatusCodeDescriptionPayee": "NO CARD RECORD (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XN", "CustomerReversalCode": "*", "StatusCode": "UPI156", "StatusCodeDescriptionPayer": "NO CARD RECORD FOUND", "StatusCodeDescriptionPayee": "NO CARD RECORD FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "XN", "CustomerReversalCode": "*", "StatusCode": "UPI157", "StatusCodeDescriptionPayer": "NO CARD RECORD (REMITTER)", "StatusCodeDescriptionPayee": "NO CARD RECORD (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "XM", "CustomerReversalCode": "*", "StatusCode": "UPI158", "StatusCodeDescriptionPayer": "EXPIRED CARD, DECLINE (BENEFICIARY)", "StatusCodeDescriptionPayee": "EXPIRED CARD, DECLINE (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XL", "CustomerReversalCode": "*", "StatusCode": "UPI159", "StatusCodeDescriptionPayer": "EXPIRED CARD DETAILS", "StatusCodeDescriptionPayee": "EXPIRED CARD DETAILS", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XL", "CustomerReversalCode": "*", "StatusCode": "UPI160", "StatusCodeDescriptionPayer": "EXPIRED CARD, DECLINE (REMITTER)", "StatusCodeDescriptionPayee": "EXPIRED CARD, DECLINE (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "XK", "CustomerReversalCode": "*", "StatusCode": "UPI161", "StatusCodeDescriptionPayer": "REQUESTED FUNCTION NOT SUPPORTED (BENEFICIARY)", "StatusCodeDescriptionPayee": "REQUESTED FUNCTION NOT SUPPORTED (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XJ", "CustomerReversalCode": "*", "StatusCode": "UPI162", "StatusCodeDescriptionPayer": "REQUESTED FUNCTION NOT SUPPORTED (REMITTER)", "StatusCodeDescriptionPayee": "REQUESTED FUNCTION NOT SUPPORTED (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XI", "CustomerReversalCode": "*", "StatusCode": "UPI163", "StatusCodeDescriptionPayer": "ACCOUNT DOES NOT EXIST (BENEFICIARY)", "StatusCodeDescriptionPayee": "ACCOUNT DOES NOT EXIST (BENEFICIARY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XH", "CustomerReversalCode": "*", "StatusCode": "UPI164", "StatusCodeDescriptionPayer": "ACCOUNT DOES NOT EXIST", "StatusCodeDescriptionPayee": "ACCOUNT DOES NOT EXIST", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "XH", "CustomerReversalCode": "*", "StatusCode": "UPI165", "StatusCodeDescriptionPayer": "ACCOUNT DOES NOT EXIST (REMITTER)", "StatusCodeDescriptionPayee": "ACCOUNT DOES NOT EXIST (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "XG", "CustomerReversalCode": "*", "StatusCode": "UPI166", "StatusCodeDescriptionPayer": "FORMAT ERROR (INVALID FORMAT) (BENEFICIARY)", "StatusCodeDescriptionPayee": "FORMAT ERROR (INVALID FORMAT) (BENEFICIARY)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "XF", "CustomerReversalCode": "*", "StatusCode": "UPI167", "StatusCodeDescriptionPayer": "FORMAT ERROR (INVALID FORMAT) (REMITTER)", "StatusCodeDescriptionPayee": "FORMAT ERROR (INVALID FORMAT) (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "XE", "CustomerReversalCode": "*", "StatusCode": "UPI168", "StatusCodeDescriptionPayer": "INVALID AMOUNT (BENEFICIARY)", "StatusCodeDescriptionPayee": "INVALID AMOUNT (BENEFICIARY)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "XD", "CustomerReversalCode": "*", "StatusCode": "UPI169", "StatusCodeDescriptionPayer": "INVALID AMOUNT (REMITTER)", "StatusCodeDescriptionPayee": "INVALID AMOUNT (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "XC", "CustomerReversalCode": "*", "StatusCode": "UPI170", "StatusCodeDescriptionPayer": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (BENEFICIARY)", "StatusCodeDescriptionPayee": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (BENEFICIARY)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "XB", "CustomerReversalCode": "*", "StatusCode": "UPI171", "StatusCodeDescriptionPayer": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (REMITTER)", "StatusCodeDescriptionPayee": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "X7", "CustomerReversalCode": "*", "StatusCode": "UPI172", "StatusCodeDescriptionPayer": "MERCHANT NOT REACHABLE (ACQURIER)", "StatusCodeDescriptionPayee": "MERCHANT NOT REACHABLE (ACQURIER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "X6", "CustomerReversalCode": "*", "StatusCode": "UPI173", "StatusCodeDescriptionPayer": "INVALID MERCHANT (ACQURIER)", "StatusCodeDescriptionPayee": "INVALID MERCHANT (ACQURIER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "X1", "CustomerReversalCode": "*", "StatusCode": "UPI174", "StatusCodeDescriptionPayer": "RESPONSE NOT RECEIVED WITHIN TAT AS SET BY PAYEE", "StatusCodeDescriptionPayee": "RESPONSE NOT RECEIVED WITHIN TAT AS SET BY PAYEE", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "VZ", "CustomerReversalCode": "*", "StatusCode": "UPI175", "StatusCodeDescriptionPayer": "PAYMENT STOPPED BY ATTACHMENT ORDER", "StatusCodeDescriptionPayee": "PAYMENT STOPPED BY ATTACHMENT ORDER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VY", "CustomerReversalCode": "*", "StatusCode": "UPI176", "StatusCodeDescriptionPayer": "PAYEE VPA IS INCORRECT (REMITTER)", "StatusCodeDescriptionPayee": "PAYEE VPA IS INCORRECT (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VX", "CustomerReversalCode": "*", "StatusCode": "UPI177", "StatusCodeDescriptionPayer": "MANDATE DECLINED AS PAYEE IS NON MERCHANT (PAYEE)", "StatusCodeDescriptionPayee": "MANDATE DECLINED AS PAYEE IS NON MERCHANT (PAYEE)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VW", "CustomerReversalCode": "*", "StatusCode": "UPI178", "StatusCodeDescriptionPayer": "MANDATE MODIFY REQUEST IS DECLINED (PAYEE)", "StatusCodeDescriptionPayee": "MANDATE MODIFY REQUEST IS DECLINED (PAYEE)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VV", "CustomerReversalCode": "*", "StatusCode": "UPI179", "StatusCodeDescriptionPayer": "MANDATE CAN NOT BE CREATED ON THIS VPA (PAYEE)", "StatusCodeDescriptionPayee": "MANDATE CAN NOT BE CREATED ON THIS VPA (PAYEE)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VU", "CustomerReversalCode": "*", "StatusCode": "UPI180", "StatusCodeDescriptionPayer": "MANDATE HAS EXPIRED", "StatusCodeDescriptionPayee": "MANDATE HAS EXPIRED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VU", "CustomerReversalCode": "*", "StatusCode": "UPI181", "StatusCodeDescriptionPayer": "MANDATE HAS EXPIRED", "StatusCodeDescriptionPayee": "MANDATE HAS EXPIRED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VT", "CustomerReversalCode": "*", "StatusCode": "UPI182", "StatusCodeDescriptionPayer": "MANDATE IS PAUSED", "StatusCodeDescriptionPayee": "MANDATE IS PAUSED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VS", "CustomerReversalCode": "*", "StatusCode": "UPI183", "StatusCodeDescriptionPayer": "DUPLICATE MANDATE REQUEST FOR SAME ITEM", "StatusCodeDescriptionPayee": "DUPLICATE MANDATE REQUEST FOR SAME ITEM", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VR", "CustomerReversalCode": "*", "StatusCode": "UPI184", "StatusCodeDescriptionPayer": "WITHDRA<PERSON><PERSON> STOPPED OWING TO LUNACY OF ACCOUNT HOLD", "StatusCodeDescriptionPayee": "WITHDRA<PERSON><PERSON> STOPPED OWING TO LUNACY OF ACCOUNT HOLD", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VQ", "CustomerReversalCode": "*", "StatusCode": "UPI185", "StatusCodeDescriptionPayer": "WITHD<PERSON><PERSON>L STOPPED OWING TO INSOLVENCY OF ACCOUNT", "StatusCodeDescriptionPayee": "WITHD<PERSON><PERSON>L STOPPED OWING TO INSOLVENCY OF ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VP", "CustomerReversalCode": "*", "StatusCode": "UPI186", "StatusCodeDescriptionPayer": "WITHDRA<PERSON>L STOPPED OWING TO DEATH OF ACCOUNT HOLDER", "StatusCodeDescriptionPayee": "WITHDRA<PERSON>L STOPPED OWING TO DEATH OF ACCOUNT HOLDER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VO", "CustomerReversalCode": "*", "StatusCode": "UPI187", "StatusCodeDescriptionPayer": "PAYMENT STOPPED BY COURT ORDER", "StatusCodeDescriptionPayee": "PAYMENT STOPPED BY COURT ORDER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VN", "CustomerReversalCode": "*", "StatusCode": "UPI188", "StatusCodeDescriptionPayer": "VAE DOES NOT EXIST", "StatusCodeDescriptionPayee": "VAE DOES NOT EXIST", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VM", "CustomerReversalCode": "*", "StatusCode": "UPI189", "StatusCodeDescriptionPayer": "NATURE OF DEBIT NOT ALLOWED IN ACCOUNT TYPE", "StatusCodeDescriptionPayee": "NATURE OF DEBIT NOT ALLOWED IN ACCOUNT TYPE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VM", "CustomerReversalCode": "*", "StatusCode": "UPI190", "StatusCodeDescriptionPayer": "NATURE OF DEBIT NOT ALLOWED IN ACCOUNT TYPE", "StatusCodeDescriptionPayee": "NATURE OF DEBIT NOT ALLOWED IN ACCOUNT TYPE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VL", "CustomerReversalCode": "*", "StatusCode": "UPI191", "StatusCodeDescriptionPayer": "MANDATE REGISTRATION NOT ALLOWED FOR CC PF PPF ACT (BANK'S POLICY)", "StatusCodeDescriptionPayee": "MANDATE REGISTRATION NOT ALLOWED FOR CC PF PPF ACT (BANK'S POLICY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VL", "CustomerReversalCode": "*", "StatusCode": "UPI192", "StatusCodeDescriptionPayer": "MANDATE REGISTRATION NOT ALLOWED FOR CC PF PPF ACT (BANK'S POLICY)", "StatusCodeDescriptionPayee": "MANDATE REGISTRATION NOT ALLOWED FOR CC PF PPF ACT (BANK'S POLICY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VK", "CustomerReversalCode": "*", "StatusCode": "UPI193", "StatusCodeDescriptionPayer": "NUMBER OF MANDATES ALLOWED ON THIS ACCOUNT HAS EXCEEDED ISSUER'S LIMIT (OPTIONAL: AS PER BANK'S POLICY)", "StatusCodeDescriptionPayee": "NUMBER OF MANDATES ALLOWED ON THIS ACCOUNT HAS EXCEEDED ISSUER'S LIMIT (OPTIONAL: AS PER BANK'S POLICY)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VJ", "CustomerReversalCode": "*", "StatusCode": "UPI194", "StatusCodeDescriptionPayer": "PAYER ACCOUNT HAS CHANGED (REMITTER)", "StatusCodeDescriptionPayee": "PAYER ACCOUNT HAS CHANGED (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "VI", "CustomerReversalCode": "*", "StatusCode": "UPI195", "StatusCodeDescriptionPayer": "EXECUTION DAY AND EXECUTION RULE MISMATCH (REMITTER)", "StatusCodeDescriptionPayee": "EXECUTION DAY AND EXECUTION RULE MISMATCH (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "VH", "CustomerReversalCode": "*", "StatusCode": "UPI196", "StatusCodeDescriptionPayer": "MANDATE SIGNATURE IS TAMPERED OR CORRUPT (REMITTER)", "StatusCodeDescriptionPayee": "MANDATE SIGNATURE IS TAMPERED OR CORRUPT (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "VG", "CustomerReversalCode": "*", "StatusCode": "UPI197", "StatusCodeDescriptionPayer": "PAYER VPA IS INCORRECT (REMITTER)", "StatusCodeDescriptionPayee": "PAYER VPA IS INCORRECT (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "VG", "CustomerReversalCode": "*", "StatusCode": "UPI198", "StatusCodeDescriptionPayer": "PAYER VPA IS INCORRECT (REMITTER)", "StatusCodeDescriptionPayee": "PAYER VPA IS INCORRECT (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "VF", "CustomerReversalCode": "*", "StatusCode": "UPI199", "StatusCodeDescriptionPayer": "UMN DOES NOT EXIST (REMITTER)", "StatusCodeDescriptionPayee": "UMN DOES NOT EXIST (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "VF", "CustomerReversalCode": "*", "StatusCode": "UPI200", "StatusCodeDescriptionPayer": "UMN DOES NOT EXIST (REMITTER)", "StatusCodeDescriptionPayee": "UMN DOES NOT EXIST (REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "VE", "CustomerReversalCode": "*", "StatusCode": "UPI201", "StatusCodeDescriptionPayer": "VAE EXIST", "StatusCodeDescriptionPayee": "VAE EXIST", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VE", "CustomerReversalCode": "*", "StatusCode": "UPI202", "StatusCodeDescriptionPayer": "ONETIME MANDATE IS ALREADY HONOURED", "StatusCodeDescriptionPayee": "ONETIME MANDATE IS ALREADY HONOURED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VD", "CustomerReversalCode": "*", "StatusCode": "UPI203", "StatusCodeDescriptionPayer": "INCORRECT AMOUNT RULE", "StatusCodeDescriptionPayee": "INCORRECT AMOUNT RULE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VC", "CustomerReversalCode": "*", "StatusCode": "UPI204", "StatusCodeDescriptionPayer": "INCORRECT RECURRENCE PATTERN RULE", "StatusCodeDescriptionPayee": "INCORRECT RECURRENCE PATTERN RULE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VB", "CustomerReversalCode": "*", "StatusCode": "UPI205", "StatusCodeDescriptionPayer": "INCORRECT RECURRENCE PATTERN", "StatusCodeDescriptionPayee": "INCORRECT RECURRENCE PATTERN", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "VA", "CustomerReversalCode": "*", "StatusCode": "UPI206", "StatusCodeDescriptionPayer": "MANDATE HAS BEEN REVOKED", "StatusCodeDescriptionPayee": "MANDATE HAS BEEN REVOKED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "UX", "CustomerReversalCode": "*", "StatusCode": "UPI207", "StatusCodeDescriptionPayer": "EXPIRED VIRTUAL ADDRESS", "StatusCodeDescriptionPayee": "EXPIRED VIRTUAL ADDRESS", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "UP", "CustomerReversalCode": "*", "StatusCode": "UPI208", "StatusCodeDescriptionPayer": "PSP TIME-OUT", "StatusCodeDescriptionPayee": "PSP TIME-OUT", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "UB", "CustomerReversalCode": "*", "StatusCode": "UPI209", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON BENEFICIARY SIDE", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON BENEFICIARY SIDE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "UA", "CustomerReversalCode": "*", "StatusCode": "UPI210", "StatusCodeDescriptionPayer": "PSP NOT SUPPORTED BY UPI", "StatusCodeDescriptionPayee": "PSP NOT SUPPORTED BY UPI", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "TM", "CustomerReversalCode": "*", "StatusCode": "UPI211", "StatusCodeDescriptionPayer": "COLLECT REQUEST IS DECLINED AS REQUESTOR IS BLOCKED BY CUSTOMER", "StatusCodeDescriptionPayee": "COLLECT REQUEST IS DECLINED AS REQUESTOR IS BLOCKED BY CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "SP", "CustomerReversalCode": "*", "StatusCode": "UPI212", "StatusCodeDescriptionPayer": "INVALID/INCORRECT ATM PIN", "StatusCodeDescriptionPayee": "INVALID/INCORRECT ATM PIN", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "SA", "CustomerReversalCode": "*", "StatusCode": "UPI213", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED FOR THIS A/C TYPE (OD)", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED FOR THIS A/C TYPE (OD)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "S3", "CustomerReversalCode": "*", "StatusCode": "UPI214", "StatusCodeDescriptionPayer": "PAYEE IS REPORTED AS SPAM UNDER RULE 1", "StatusCodeDescriptionPayee": "PAYEE IS REPORTED AS SPAM UNDER RULE 2", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "S2", "CustomerReversalCode": "*", "StatusCode": "UPI215", "StatusCodeDescriptionPayer": "PAYEE IS REPORTED AS SPAM UNDER RULE 0", "StatusCodeDescriptionPayee": "PAYEE IS REPORTED AS SPAM UNDER RULE 1", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "S1", "CustomerReversalCode": "*", "StatusCode": "UPI216", "StatusCodeDescriptionPayer": "PAYEE IS REPORTED AS SPAM UNDER RULE 1", "StatusCodeDescriptionPayee": "PAYEE IS REPORTED AS SPAM UNDER RULE 0", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "S0", "CustomerReversalCode": "*", "StatusCode": "UPI217", "StatusCodeDescriptionPayer": "SPAM COLLECT DECLINED BY PSP", "StatusCodeDescriptionPayee": "SPAM COLLECT DECLINED BY PSP", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "RZ", "CustomerReversalCode": "*", "StatusCode": "UPI218", "StatusCodeDescriptionPayer": "ACCOUNT IS ALREADY REGISTERED WITH MBEBA FLAG AS 'Y'", "StatusCodeDescriptionPayee": "ACCOUNT IS ALREADY REGISTERED WITH MBEBA FLAG AS 'Y'", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "RN", "CustomerReversalCode": "*", "StatusCode": "UPI219", "StatusCodeDescriptionPayer": "REGISTRATION IS TEMPORARY BLOCKED DUE TO MAXIMUM NO OF ATTEMPTS EXCEEDED", "StatusCodeDescriptionPayee": "REGISTRATION IS TEMPORARY BLOCKED DUE TO MAXIMUM NO OF ATTEMPTS EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "RM", "CustomerReversalCode": "*", "StatusCode": "UPI220", "StatusCodeDescriptionPayer": "INVALID MPIN ( VIOLATION OF POLICIES WHILE SETTING/CHANGING MPIN )", "StatusCodeDescriptionPayee": "INVALID MPIN ( VIOLATION OF POLICIES WHILE SETTING/CHANGING MPIN )", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "RA", "CustomerReversalCode": "*", "StatusCode": "UPI221", "StatusCodeDescriptionPayer": "PAYER AND PAYEE ACCOUNT SHOULD NOT BE EQUAL", "StatusCodeDescriptionPayee": "PAYER AND PAYEE ACCOUNT SHOULD NOT BE EQUAL", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QZ", "CustomerReversalCode": "*", "StatusCode": "UPI222", "StatusCodeDescriptionPayer": "MANDATE MODIFICATION DECLINED BY MERCHANT", "StatusCodeDescriptionPayee": "MANDATE MODIFICATION DECLINED BY MERCHANT", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QY", "CustomerReversalCode": "*", "StatusCode": "UPI223", "StatusCodeDescriptionPayer": "PAYEE INITIATED MANDATE CANNOT BE MODIFIED BY PAYER", "StatusCodeDescriptionPayee": "PAYEE INITIATED MANDATE CANNOT BE MODIFIED BY PAYER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QX", "CustomerReversalCode": "*", "StatusCode": "UPI224", "StatusCodeDescriptionPayer": "PAYER VPA IS INCORRECT (PAYEE)", "StatusCodeDescriptionPayee": "PAYER VPA IS INCORRECT (PAYEE)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QW", "CustomerReversalCode": "*", "StatusCode": "UPI225", "StatusCodeDescriptionPayer": "UMN DOES NOT EXIST (PAYEE)", "StatusCodeDescriptionPayee": "UMN DOES NOT EXIST (PAYEE)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QV", "CustomerReversalCode": "*", "StatusCode": "UPI226", "StatusCodeDescriptionPayer": "MA<PERSON><PERSON>E DECLINED AS PAYEE IS NON-MERCHANT (PAYER)", "StatusCodeDescriptionPayee": "MA<PERSON><PERSON>E DECLINED AS PAYEE IS NON-MERCHANT (PAYER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QU", "CustomerReversalCode": "*", "StatusCode": "UPI227", "StatusCodeDescriptionPayer": "PAYER ACCOUNT HAS CHANGED (PAYER)", "StatusCodeDescriptionPayee": "PAYER ACCOUNT HAS CHANGED (PAYER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "QU", "CustomerReversalCode": "*", "StatusCode": "UPI228", "StatusCodeDescriptionPayer": "PAYER ACCOUNT HAS CHANGED(PAYER)", "StatusCodeDescriptionPayee": "PAYER ACCOUNT HAS CHANGED(PAYER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "QT", "CustomerReversalCode": "*", "StatusCode": "UPI229", "StatusCodeDescriptionPayer": "MANDATE MODIFY REQUEST IS DECLINED (PAYER)", "StatusCodeDescriptionPayee": "MANDATE MODIFY REQUEST IS DECLINED (PAYER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QS", "CustomerReversalCode": "*", "StatusCode": "UPI230", "StatusCodeDescriptionPayer": "PAYER PROFILE DOES NOT EXIST (DE REGISTRATION/VPA REMOVED/UPDATED)", "StatusCodeDescriptionPayee": "PAYER PROFILE DOES NOT EXIST (DE REGISTRATION/VPA REMOVED/UPDATED)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QR", "CustomerReversalCode": "*", "StatusCode": "UPI231", "StatusCodeDescriptionPayer": "EXECUTION DAY AND EXECUTION RULE MISMATCH (PAYER)", "StatusCodeDescriptionPayee": "EXECUTION DAY AND EXECUTION RULE MISMATCH (PAYER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "QQ", "CustomerReversalCode": "*", "StatusCode": "UPI232", "StatusCodeDescriptionPayer": "MANDATE CANNOT BE CREATED ON THIS VPA (PAYER)", "StatusCodeDescriptionPayee": "MANDATE CANNOT BE CREATED ON THIS VPA (PAYER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QP", "CustomerReversalCode": "*", "StatusCode": "UPI233", "StatusCodeDescriptionPayer": "PAYER INITIATED MANDATE CANNOT BE MODIFIED BY PAYEE", "StatusCodeDescriptionPayee": "PAYER INITIATED MANDATE CANNOT BE MODIFIED BY PAYEE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QO", "CustomerReversalCode": "*", "StatusCode": "UPI234", "StatusCodeDescriptionPayer": "THIS MANDATE IS NON REVOKEABLE", "StatusCodeDescriptionPayee": "THIS MANDATE IS NON REVOKEABLE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QN", "CustomerReversalCode": "*", "StatusCode": "UPI235", "StatusCodeDescriptionPayer": "DUPLICATE MANDATE REQUEST", "StatusCodeDescriptionPayee": "DUPLICATE MANDATE REQUEST", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QN", "CustomerReversalCode": "*", "StatusCode": "UPI236", "StatusCodeDescriptionPayer": "DUPLICATE MANDATE REQUEST", "StatusCodeDescriptionPayee": "DUPLICATE MANDATE REQUEST", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QM", "CustomerReversalCode": "*", "StatusCode": "UPI237", "StatusCodeDescriptionPayer": "PAYER VPA IS INCORRECT (PAYER)", "StatusCodeDescriptionPayee": "PAYER VPA IS INCORRECT (PAYER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QL", "CustomerReversalCode": "*", "StatusCode": "UPI238", "StatusCodeDescriptionPayer": "MANDATE DEBIT IS BEYOND PSP SPECIFIED AMOUNT CAP", "StatusCodeDescriptionPayee": "MANDATE DEBIT IS BEYOND PSP SPECIFIED AMOUNT CAP", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QK", "CustomerReversalCode": "*", "StatusCode": "UPI239", "StatusCodeDescriptionPayer": "MANDATE REQUEST LIMIT HAS BREACHED", "StatusCodeDescriptionPayee": "MANDATE REQUEST LIMIT HAS BREACHED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QJ", "CustomerReversalCode": "*", "StatusCode": "UPI240", "StatusCodeDescriptionPayer": "UMN DOES NOT EXIST (PAYER)", "StatusCodeDescriptionPayee": "UMN DOES NOT EXIST (PAYER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "QJ", "CustomerReversalCode": "*", "StatusCode": "UPI241", "StatusCodeDescriptionPayer": "UMN DOES NOT EXIST (PAYER)", "StatusCodeDescriptionPayee": "UMN DOES NOT EXIST (PAYER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "QI", "CustomerReversalCode": "*", "StatusCode": "UPI242", "StatusCodeDescriptionPayer": "PAYEE VPA IS INCORRECT (PAYER)", "StatusCodeDescriptionPayee": "PAYEE VPA IS INCORRECT (PAYER)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QH", "CustomerReversalCode": "*", "StatusCode": "UPI243", "StatusCodeDescriptionPayer": "TXN AMOUNT DIFFERS FROM MANDATE AMOUNT", "StatusCodeDescriptionPayee": "TXN AMOUNT DIFFERS FROM MANDATE AMOUNT", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "QD", "CustomerReversalCode": "*", "StatusCode": "UPI244", "StatusCodeDescriptionPayer": "MANDATE HAS EXPIRED", "StatusCodeDescriptionPayee": "MANDATE HAS EXPIRED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QD", "CustomerReversalCode": "*", "StatusCode": "UPI245", "StatusCodeDescriptionPayer": "MANDATE HAS EXPIRED", "StatusCodeDescriptionPayee": "MANDATE HAS EXPIRED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QC", "CustomerReversalCode": "*", "StatusCode": "UPI246", "StatusCodeDescriptionPayer": "MANDATE HAS BEEN REVOKED", "StatusCodeDescriptionPayee": "MANDATE HAS BEEN REVOKED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QC", "CustomerReversalCode": "*", "StatusCode": "UPI247", "StatusCodeDescriptionPayer": "MANDATE HAS BEEN REVOKED", "StatusCodeDescriptionPayee": "MANDATE HAS BEEN REVOKED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QB", "CustomerReversalCode": "*", "StatusCode": "UPI248", "StatusCodeDescriptionPayer": "MANDATE IS ALREADY HONOURED", "StatusCodeDescriptionPayee": "MANDATE IS ALREADY HONOURED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "QA", "CustomerReversalCode": "*", "StatusCode": "UPI249", "StatusCodeDescriptionPayer": "MANDATE IS PAUSED BY USER", "StatusCodeDescriptionPayee": "MANDATE IS PAUSED BY USER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "PS", "CustomerReversalCode": "*", "StatusCode": "UPI250", "StatusCodeDescriptionPayer": "MAXIMUM BALANCE EXCEEDED AS SET BY BENEFICIARY BANK", "StatusCodeDescriptionPayee": "MAXIMUM BALANCE EXCEEDED AS SET BY BENEFICIARY BANK", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "OD", "CustomerReversalCode": "*", "StatusCode": "UPI251", "StatusCodeDescriptionPayer": "ORIGINAL DEBIT NOT FOUND", "StatusCodeDescriptionPayee": "ORIGINAL DEBIT NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "OC", "CustomerReversalCode": "*", "StatusCode": "UPI252", "StatusCodeDescriptionPayer": "ORIGINAL CREDIT NOT FOUND", "StatusCodeDescriptionPayee": "ORIGINAL CREDIT NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "NO", "CustomerReversalCode": "*", "StatusCode": "UPI253", "StatusCodeDescriptionPayer": "NO ORIGINAL REQUEST FOUND DURING DEBIT/CREDIT", "StatusCodeDescriptionPayee": "NO ORIGINAL REQUEST FOUND DURING DEBIT/CREDIT", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "ND", "CustomerReversalCode": "*", "StatusCode": "UPI254", "StatusCodeDescriptionPayer": "DEBIT NOT DONE", "StatusCodeDescriptionPayee": "DEBIT NOT DONE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "NC", "CustomerReversalCode": "*", "StatusCode": "UPI255", "StatusCodeDescriptionPayer": "CREDIT NOT DONE", "StatusCodeDescriptionPayee": "CREDIT NOT DONE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "MM", "CustomerReversalCode": "*", "StatusCode": "UPI256", "StatusCodeDescriptionPayer": "MANDATE REQUEST IS DECLINED BY MERCHANT (PAYEE)", "StatusCodeDescriptionPayee": "MANDATE REQUEST IS DECLINED BY MERCHANT (PAYEE)", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "LD", "CustomerReversalCode": "*", "StatusCode": "UPI257", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS DEBIT IN BANK'S POOL/BGL ACCOUNT", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS DEBIT IN BANK'S POOL/BGL ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "LD", "CustomerReversalCode": "*", "StatusCode": "UPI258", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS DEBIT IN BANK'S POOL/BGL ACCOUNT", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS DEBIT IN BANK'S POOL/BGL ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "LC", "CustomerReversalCode": "*", "StatusCode": "UPI259", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS CREDIT FROM BAN<PERSON>'S POOL/BGL ACCOUNT", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS CREDIT FROM BAN<PERSON>'S POOL/BGL ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "LC", "CustomerReversalCode": "*", "StatusCode": "UPI260", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS CREDIT FROM BAN<PERSON>'S POOL/BGL ACCOUNT", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS CREDIT FROM BAN<PERSON>'S POOL/BGL ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "K1", "CustomerReversalCode": "*", "StatusCode": "UPI261", "StatusCodeDescriptionPayer": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY REMITTER", "StatusCodeDescriptionPayee": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY REMITTER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "IR", "CustomerReversalCode": "*", "StatusCode": "UPI262", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON REMITTER SIDE", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS DUE TO INTERNAL EXCEPTION AT SERVER/CBS/ETC ON REMITTER SIDE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "*", "CustomerStatusCode": "IE", "CustomerReversalCode": "*", "StatusCode": "UPI264", "StatusCodeDescriptionPayer": "ADEQUAT<PERSON> FUNDS NOT A<PERSON><PERSON><PERSON><PERSON> IN THE ACCOUNT BECAUSE FUNDS HAVE BEEN BLOCKED FOR MANDATE", "StatusCodeDescriptionPayee": "ADEQUAT<PERSON> FUNDS NOT A<PERSON><PERSON><PERSON><PERSON> IN THE ACCOUNT BECAUSE FUNDS HAVE BEEN BLOCKED FOR MANDATE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "ID", "CustomerReversalCode": "*", "StatusCode": "UPI265", "StatusCodeDescriptionPayer": "DEBIT AMOUNT G<PERSON><PERSON>ER THAN BLOCKED AMOUNT", "StatusCodeDescriptionPayee": "DEBIT AMOUNT G<PERSON><PERSON>ER THAN BLOCKED AMOUNT", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "IC", "CustomerReversalCode": "*", "StatusCode": "UPI266", "StatusCodeDescriptionPayer": "DEBIT AMOUNT IS NOT BLOCKED FOR THE CUSTOMER", "StatusCodeDescriptionPayee": "DEBIT AMOUNT IS NOT BLOCKED FOR THE CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "IB", "CustomerReversalCode": "*", "StatusCode": "UPI267", "StatusCodeDescriptionPayer": "<PERSON><PERSON><PERSON><PERSON> MANDATE AFTER THE REMITTER UNBLOCKED THE AMOUNT", "StatusCodeDescriptionPayee": "<PERSON><PERSON><PERSON><PERSON> MANDATE AFTER THE REMITTER UNBLOCKED THE AMOUNT", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "IA", "CustomerReversalCode": "*", "StatusCode": "UPI268", "StatusCodeDescriptionPayer": "DUPLICATE BLOCK<PERSON>UND FOR MANDATE REQUEST", "StatusCodeDescriptionPayee": "DUPLICATE BLOCK<PERSON>UND FOR MANDATE REQUEST", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "HS", "CustomerReversalCode": "*", "StatusCode": "UPI269", "StatusCodeDescriptionPayer": "BANKS HSM IS DOWN", "StatusCodeDescriptionPayee": "BANKS HSM IS DOWN", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "*", "CustomerStatusCode": "HS", "CustomerReversalCode": "*", "StatusCode": "UPI270", "StatusCodeDescriptionPayer": "BANKS HSM IS DOWN(REMITTER)", "StatusCodeDescriptionPayee": "BANKS HSM IS DOWN(REMITTER)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "DT", "CustomerReversalCode": "*", "StatusCode": "UPI271", "StatusCodeDescriptionPayer": "DUPLICATE RRN FOUND IN THE TRANSACTION (REMITTER)", "StatusCodeDescriptionPayee": "DUPLICATE RRN FOUND IN THE TRANSACTION (REMITTER)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "*", "CustomerStatusCode": "DF", "CustomerReversalCode": "*", "StatusCode": "UPI272", "StatusCodeDescriptionPayer": "DUPLICAT<PERSON> RRN FOUND IN THE TRANSACTION (BENEFICIARY)", "StatusCodeDescriptionPayee": "DUPLICAT<PERSON> RRN FOUND IN THE TRANSACTION (BENEFICIARY)", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "*", "CustomerStatusCode": "CS", "CustomerReversalCode": "*", "StatusCode": "UPI273", "StatusCodeDescriptionPayer": "CREDIT SUCCESS", "StatusCodeDescriptionPayee": "CREDIT SUCCESS", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "CI", "CustomerReversalCode": "*", "StatusCode": "UPI274", "StatusCodeDescriptionPayer": "COMPLIANCE ERROR CODE FOR ISSUER", "StatusCodeDescriptionPayee": "COMPLIANCE ERROR CODE FOR ISSUER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "CA", "CustomerReversalCode": "*", "StatusCode": "UPI275", "StatusCodeDescriptionPayer": "COMPLIANCE ERROR CODE FOR ACQUIRER", "StatusCodeDescriptionPayee": "COMPLIANCE ERROR CODE FOR ACQUIRER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "BR", "CustomerReversalCode": "*", "StatusCode": "UPI276", "StatusCodeDescriptionPayer": "MOBILE NUMBER REGISTERED WITH MULTIPLE CUSTOMER IDS", "StatusCodeDescriptionPayee": "MOBILE NUMBER REGISTERED WITH MULTIPLE CUSTOMER IDS", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "B7", "CustomerReversalCode": "*", "StatusCode": "UPI277", "StatusCodeDescriptionPayer": "BANK CARD MANAGEMENT SYSTEM IS DOWN", "StatusCodeDescriptionPayee": "BANK CARD MANAGEMENT SYSTEM IS DOWN", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "B6", "CustomerReversalCode": "*", "StatusCode": "UPI278", "StatusCodeDescriptionPayer": "MISMATCH IN PAYMENT DETAILS", "StatusCodeDescriptionPayee": "MISMATCH IN PAYMENT DETAILS", "DeclineType": "Technical"}, {"RawStatusCode": "U30", "CustomerStatusCode": "B3", "CustomerReversalCode": "*", "StatusCode": "UPI1198", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO THE ACCOUNT (EXAMPLE: MIN<PERSON> ACCOUNT,PROPRIETOR ACCOUNT,LEGAL CASE AGAINST THIS ACCOUNT ETC., NRE (AS PER BANK’S POLICY))", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO THE ACCOUNT (EXAMPLE: MIN<PERSON> ACCOUNT,PROPRIETOR ACCOUNT,LEGAL CASE AGAINST THIS ACCOUNT ETC., NRE (AS PER BANK’S POLICY))", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "B3", "CustomerReversalCode": "*", "StatusCode": "UPI279", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO THE ACCOUNT (EXAMPLE: MINOR ACCOUNT, PROPRIETOR ACCOUNT, LEGAL CASE AGAINST THIS ACCOUNT ETC , NRE (AS PER BANK'S POLICY))", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO THE ACCOUNT (EXAMPLE: MINOR ACCOUNT, PROPRIETOR ACCOUNT, LEGAL CASE AGAINST THIS ACCOUNT ETC , NRE (AS PER BANK'S POLICY))", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "B3", "CustomerReversalCode": "*", "StatusCode": "UPI280", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO THE ACCOUNT", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO THE ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "B2", "CustomerReversalCode": "*", "StatusCode": "UPI281", "StatusCodeDescriptionPayer": "ACCOUNT LINKED WITH MULTIPLE NAMES", "StatusCodeDescriptionPayee": "ACCOUNT LINKED WITH MULTIPLE NAMES", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "B1", "CustomerReversalCode": "*", "StatusCode": "UPI282", "StatusCodeDescriptionPayer": "REGISTERED M<PERSON><PERSON>LE NUMBER LINKED TO THE ACCOUNT HAS BEEN CHANGED/REMOVED", "StatusCodeDescriptionPayee": "REGISTERED M<PERSON><PERSON>LE NUMBER LINKED TO THE ACCOUNT HAS BEEN CHANGED/REMOVED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "AM", "CustomerReversalCode": "*", "StatusCode": "UPI283", "StatusCodeDescriptionPayer": "MPIN NOT SET BY CUSTOMER", "StatusCodeDescriptionPayee": "MPIN NOT SET BY CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "AM", "CustomerReversalCode": "*", "StatusCode": "UPI284", "StatusCodeDescriptionPayer": "MPIN NOT SET BY CUSTOMER", "StatusCodeDescriptionPayee": "MPIN NOT SET BY CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "AJ", "CustomerReversalCode": "*", "StatusCode": "UPI285", "StatusCodeDescriptionPayer": "CARD IS NOT ACTIVE", "StatusCodeDescriptionPayee": "CARD IS NOT ACTIVE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "96", "CustomerReversalCode": "*", "StatusCode": "UPI286", "StatusCodeDescriptionPayer": "REVERSAL FAILURE", "StatusCodeDescriptionPayee": "REVERSAL FAILURE", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "59", "CustomerReversalCode": "*", "StatusCode": "UPI287", "StatusCodeDescriptionPayer": "SUSPECTED FRAUD, DECLINE/TRANSACTIONS DECLINED BASED ON RISKSCORE BY REMITTER", "StatusCodeDescriptionPayee": "SUSPECTED FRAUD, DECLINE/TRANSACTIONS DECLINED BASED ON RISKSCORE BY REMITTER", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "15", "CustomerReversalCode": "*", "StatusCode": "UPI288", "StatusCodeDescriptionPayer": "ISSUER NOT LIVE ON UPI", "StatusCodeDescriptionPayee": "ISSUER NOT LIVE ON UPI", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "FL", "CustomerReversalCode": "*", "StatusCode": "UPI1095", "StatusCodeDescriptionPayer": "First Transaction Limit exceeded The user has crossed the Rs 5000 transaction limit for the first 24hrs post onboarding", "StatusCodeDescriptionPayee": "First Transaction Limit exceeded The user has crossed the Rs 5000 transaction limit for the first 24hrs post onboarding", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "FP", "CustomerReversalCode": "*", "StatusCode": "UPI1096", "StatusCodeDescriptionPayer": "Freeze Period for first time user Post 1st transaction approval, a 12 hr freeze period is initiated by the bank", "StatusCodeDescriptionPayee": "Freeze Period for first time user Post 1st transaction approval, a 12 hr freeze period is initiated by the bank", "DeclineType": "Business"}, {"RawStatusCode": "34", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI292", "StatusCodeDescriptionPayer": "SUSPECTED FRAUD", "StatusCodeDescriptionPayee": "SUSPECTED FRAUD", "DeclineType": "Business"}, {"RawStatusCode": "U16", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI293", "StatusCodeDescriptionPayer": "RISK THRESHOLD EXCEEDED", "StatusCodeDescriptionPayee": "RISK THRESHOLD EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "21", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI294", "StatusCodeDescriptionPayer": "NO ACTION TAKEN (FULL REVERSAL)", "StatusCodeDescriptionPayee": "NO ACTION TAKEN (FULL REVERSAL)", "DeclineType": "Business"}, {"RawStatusCode": "32", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI295", "StatusCodeDescriptionPayer": "PARTIAL REVERSAL", "StatusCodeDescriptionPayee": "PARTIAL REVERSAL", "DeclineType": "Business"}, {"RawStatusCode": "51", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI296", "StatusCodeDescriptionPayer": "NOT SUFFICIENT FUNDS", "StatusCodeDescriptionPayee": "NOT SUFFICIENT FUNDS", "DeclineType": "Business"}, {"RawStatusCode": "57", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI297", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO ACCOUNT HOLDER", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO ACCOUNT HOLDER", "DeclineType": "Business"}, {"RawStatusCode": "61", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI298", "StatusCodeDescriptionPayer": "EXCEEDS TRANSACTION AMOUNT LIMIT", "StatusCodeDescriptionPayee": "EXCEEDS TRANSACTION AMOUNT LIMIT", "DeclineType": "Business"}, {"RawStatusCode": "75", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI299", "StatusCodeDescriptionPayer": "EXCESSIVE PIN TRIES", "StatusCodeDescriptionPayee": "EXCESSIVE PIN TRIES", "DeclineType": "Business"}, {"RawStatusCode": "A01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI300", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "A01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI301", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "A02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI302", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT ADDRESS TYPE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT ADDRESS TYPE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "A02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI303", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT ADDRESS TYPE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT ADDRESS TYPE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "A03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI304", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "A03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI305", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "A04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI306", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT NAME MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT NAME MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "A04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI307", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT NAME MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT NAME MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "A05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI308", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL AADHAR MUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL AADHAR MUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "A05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI309", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL AADHAR MUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL AADHAR MUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "A06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI310", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL ACCOUNTMUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL ACCOUNTMUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "A06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI311", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL ACCOUNTMUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL ACCOUNTMUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "A07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI312", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL MOBILE MUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL MOBILE MUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "A07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI313", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL MOBILE MUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL MOBILE MUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "A08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI314", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL CARD MUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL CARD MUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "A08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI315", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL CARD MUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL CARD MUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "A09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI316", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL VALUE INCORRECT FORMAT <NAME>", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL VALUE INCORRECT FORMAT <NAME>", "DeclineType": "Technical"}, {"RawStatusCode": "A09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI317", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL VALUE INCORRECT FORMAT <NAME>", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL VALUE INCORRECT FORMAT <NAME>", "DeclineType": "Technical"}, {"RawStatusCode": "A10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI318", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL VALUE MUST BE PRESENT FOR / NAME", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL VALUE MUST BE PRESENT FOR / NAME", "DeclineType": "Technical"}, {"RawStatusCode": "A11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI319", "StatusCodeDescriptionPayer": "PAYER/PAYEE <PERSON>AR BASED TRANSACTIONS ARE NOT SUPPORTED PRESENTLY", "StatusCodeDescriptionPayee": "PAYER/PAYEE <PERSON>AR BASED TRANSACTIONS ARE NOT SUPPORTED PRESENTLY", "DeclineType": "Business"}, {"RawStatusCode": "A12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI320", "StatusCodeDescriptionPayer": "PAYER/PAYEE IFSC BASED TRANSACTIONS ARE NOT SUPPORTED PRESENTLY", "StatusCodeDescriptionPayee": "PAYER/PAYEE IFSC BASED TRANSACTIONS ARE NOT SUPPORTED PRESENTLY", "DeclineType": "Business"}, {"RawStatusCode": "A13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI321", "StatusCodeDescriptionPayer": "PAYER/PAYEE MMID BASED TRANSACTIONS ARE NOT SUPPORTED PRESENTLY", "StatusCodeDescriptionPayee": "PAYER/PAYEE MMID BASED TRANSACTIONS ARE NOT SUPPORTED PRESENTLY", "DeclineType": "Business"}, {"RawStatusCode": "A14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI322", "StatusCodeDescriptionPayer": "PAYER/PAYEE CARD BASED TRANSACTIONS ARE NOT SUPPORTED PRESENTLY", "StatusCodeDescriptionPayee": "PAYER/PAYEE CARD BASED TRANSACTIONS ARE NOT SUPPORTED PRESENTLY", "DeclineType": "Business"}, {"RawStatusCode": "B01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI323", "StatusCodeDescriptionPayer": "PAYEES NOT PRESENT", "StatusCodeDescriptionPayee": "PAYEES NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "B01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI324", "StatusCodeDescriptionPayer": "PAYEES NOT PRESENT", "StatusCodeDescriptionPayee": "PAYEES NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "B02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI325", "StatusCodeDescriptionPayer": "Beneficiary UPI ID is not valid", "StatusCodeDescriptionPayee": "Beneficiary UPI ID is not valid", "DeclineType": "Business"}, {"RawStatusCode": "B02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI326", "StatusCodeDescriptionPayer": "Beneficiary UPI ID is not valid", "StatusCodeDescriptionPayee": "Beneficiary UPI ID is not valid", "DeclineType": "Business"}, {"RawStatusCode": "B03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI327", "StatusCodeDescriptionPayer": "PAYEE ADDR MUST BE VALID VPA MAXLENGTH 255", "StatusCodeDescriptionPayee": "PAYEE ADDR MUST BE VALID VPA MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "B03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI328", "StatusCodeDescriptionPayer": "PAYEE ADDR MUST BE VALID VPA MAXLENGTH 255", "StatusCodeDescriptionPayee": "PAYEE ADDR MUST BE VALID VPA MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "B04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI329", "StatusCodeDescriptionPayer": "PAYEE SEQNUM NUMERIC MINLENGTH 1 MAXLENGTH 3", "StatusCodeDescriptionPayee": "PAYEE SEQNUM NUMERIC MINLENGTH 1 MAXLENGTH 3", "DeclineType": "Technical"}, {"RawStatusCode": "B04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI330", "StatusCodeDescriptionPayer": "PAYEE SEQNUM NUMERIC MINLENGTH 1 MAXLENGTH 3", "StatusCodeDescriptionPayee": "PAYEE SEQNUM NUMERIC MINLENGTH 1 MAXLENGTH 3", "DeclineType": "Technical"}, {"RawStatusCode": "B05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI331", "StatusCodeDescriptionPayer": "PAYEE NAME ALPHANUMERIC MINLENGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYEE NAME ALPHANUMERIC MINLENGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "B05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI332", "StatusCodeDescriptionPayer": "PAYEE NAME ALPHANUMERIC MINLENGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYEE NAME ALPHANUMERIC MINLENGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "B06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI333", "StatusCodeDescriptionPayer": "PAYEE TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "PAYEE TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "B06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI334", "StatusCodeDescriptionPayer": "PAYEE TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "PAYEE TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "B07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI335", "StatusCodeDescriptionPayer": "PAYEE CODE NUMERIC OF LENGTH 4", "StatusCodeDescriptionPayee": "PAYEE CODE NUMERIC OF LENGTH 4", "DeclineType": "Technical"}, {"RawStatusCode": "B07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI336", "StatusCodeDescriptionPayer": "PAYEE CODE NUMERIC OF LENGTH 4", "StatusCodeDescriptionPayee": "PAYEE CODE NUMERIC OF LENGTH 4", "DeclineType": "Technical"}, {"RawStatusCode": "B08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI337", "StatusCodeDescriptionPayer": "PAYER</PAYEE> ADDRESS CANNOT BE CHANGED", "StatusCodeDescriptionPayee": "PAYER</PAYEE> ADDRESS CANNOT BE CHANGED", "DeclineType": "Technical"}, {"RawStatusCode": "B09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI338", "StatusCodeDescriptionPayer": "MULTIPLE PAYEES NOT ALLOWED", "StatusCodeDescriptionPayee": "MULTIPLE PAYEES NOT ALLOWED", "DeclineType": "Business"}, {"RawStatusCode": "BA1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI339", "StatusCodeDescriptionPayer": "TXN TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "TXN TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "BA2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI340", "StatusCodeDescriptionPayer": "AMOUNT TAG MUST BE PRESENT", "StatusCodeDescriptionPayee": "AMOUNT TAG MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "BA3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI341", "StatusCodeDescriptionPayer": "BAL DATA SHOULD BE PRESENT/VALID IF TXN TYPE=BALCHK", "StatusCodeDescriptionPayee": "BAL DATA SHOULD BE PRESENT/VALID IF TXN TYPE=BALCHK", "DeclineType": "Technical"}, {"RawStatusCode": "BT", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI342", "StatusCodeDescriptionPayer": "ACQUIRER/BENEFICIARY UNAVAILABLE(TIMEOUT)", "StatusCodeDescriptionPayee": "ACQUIRER/BENEFICIARY UNAVAILABLE(TIMEOUT)", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "C01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI343", "StatusCodeDescriptionPayer": "PAYER/PAYEE CREDS NOT PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CREDS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI344", "StatusCodeDescriptionPayer": "PAYER/PAYEE CREDS NOT PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CREDS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI345", "StatusCodeDescriptionPayer": "PAYER/PAYEE CREDS CRED MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CREDS CRED MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI346", "StatusCodeDescriptionPayer": "PAYER/PAYEE CREDS CRED MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CREDS CRED MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI347", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED DATA IS WRONG", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED DATA IS WRONG", "DeclineType": "Technical"}, {"RawStatusCode": "C03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI348", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED DATA IS WRONG", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED DATA IS WRONG", "DeclineType": "Technical"}, {"RawStatusCode": "C04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI349", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED AADHAR MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED AADHAR MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI350", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED AADHAR MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED AADHAR MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI351", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED OTP MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED OTP MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI352", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED OTP MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED OTP MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI353", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED PIN MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED PIN MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI354", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED PIN MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED PIN MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI355", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED CARD MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED CARD MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI356", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED CARD MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED CARD MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI357", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED PREAPPROVED MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED PREAPPROVED MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI358", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED PREAPPROVED MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED PREAPPROVED MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI359", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED DATA MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED DATA MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI360", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED DATA MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED DATA MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI361", "StatusCodeDescriptionPayer": "PAYER/PAYEE  CRED DATA ENCRYPTED AUTHENTICATION MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE  CRED DATA ENCRYPTED AUTHENTICATION MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI362", "StatusCodeDescriptionPayer": "PAYER/PAYEE  CRED DATA ENCRYPTED AUTHENTICATION MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE  CRED DATA ENCRYPTED AUTHENTICATION MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "C11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI363", "StatusCodeDescriptionPayer": "AYER/PAYEE CRED SHOULD NOT BE SENT", "StatusCodeDescriptionPayee": "AYER/PAYEE CRED SHOULD NOT BE SENT", "DeclineType": "Technical"}, {"RawStatusCode": "C12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI364", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED CODE SHOULD NOT BE PRESENT AND BE EITHER NPCI OR UIDAI", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED CODE SHOULD NOT BE PRESENT AND BE EITHER NPCI OR UIDAI", "DeclineType": "Technical"}, {"RawStatusCode": "D01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI365", "StatusCodeDescriptionPayer": "PAYER/PAYEE DEVICE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE DEVICE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "D01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI366", "StatusCodeDescriptionPayer": "PAYER/PAYEE DEVICE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE DEVICE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "D02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI367", "StatusCodeDescriptionPayer": "PAYER/PAYEE DEVICE TAGS MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE DEVICE TAGS MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "D02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI368", "StatusCodeDescriptionPayer": "PAYER/PAYEE DEVICE TAGS MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE DEVICE TAGS MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "D03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI369", "StatusCodeDescriptionPayer": "PAYER/PAYEE TAG DEVICE NAME/VALUE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE TAG DEVICE NAME/VALUE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "D03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI370", "StatusCodeDescriptionPayer": "PAYER/PAYEE TAG DEVICE NAME/VALUE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE TAG DEVICE NAME/VALUE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "D04 - D11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI371", "StatusCodeDescriptionPayer": "SAME VALIDATION MESSAGE BASED ON DEVICE TYPE", "StatusCodeDescriptionPayee": "SAME VALIDATION MESSAGE BASED ON DEVICE TYPE", "DeclineType": "Technical"}, {"RawStatusCode": "D04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI372", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "D12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI373", "StatusCodeDescriptionPayer": "TELECOM VALUE MUST BE MINLENGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "TELECOM VALUE MUST BE MINLENGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "D12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI374", "StatusCodeDescriptionPayer": "TELECOM VALUE MUST BE MINLENGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "TELECOM VALUE MUST BE MINLENGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "D13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI375", "StatusCodeDescriptionPayer": "TELECOM TAG IS ALLOWED ONLY FOR TYPE=USDC/USDB", "StatusCodeDescriptionPayee": "TELECOM TAG IS ALLOWED ONLY FOR TYPE=USDC/USDB", "DeclineType": "Technical"}, {"RawStatusCode": "D13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI376", "StatusCodeDescriptionPayer": "TELECOM TAG IS ALLOWED ONLY FOR TYPE=USDC/USDB", "StatusCodeDescriptionPayee": "TELECOM TAG IS ALLOWED ONLY FOR TYPE=USDC/USDB", "DeclineType": "Technical"}, {"RawStatusCode": "E01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI377", "StatusCodeDescriptionPayer": "<RESP> MUST BE PRESENT", "StatusCodeDescriptionPayee": "<RESP> MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "E01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI378", "StatusCodeDescriptionPayer": "<RESP> MUST BE PRESENT", "StatusCodeDescriptionPayee": "<RESP> MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "E02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI379", "StatusCodeDescriptionPayer": "RESP MSGID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "RESP MSGID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "E02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI380", "StatusCodeDescriptionPayer": "RESP MSGID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "RESP MSGID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "E03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI381", "StatusCodeDescriptionPayer": "RESP RESULT MUST BE PRESENT ALPHANUMERIC MIN LENGTH 1 MAX LENGTH 20", "StatusCodeDescriptionPayee": "RESP RESULT MUST BE PRESENT ALPHANUMERIC MIN LENGTH 1 MAX LENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "E03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI382", "StatusCodeDescriptionPayer": "RESP RESULT MUST BE PRESENT ALPHANUMERIC MIN LENGTH 1 MAX LENGTH 20", "StatusCodeDescriptionPayee": "RESP RESULT MUST BE PRESENT ALPHANUMERIC MIN LENGTH 1 MAX LENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "E04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI383", "StatusCodeDescriptionPayer": "RESP ERRORCODE MUST BE PRESENT", "StatusCodeDescriptionPayee": "RESP ERRORCODE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "E04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI384", "StatusCodeDescriptionPayer": "RESP ERRORCODE MUST BE PRESENT", "StatusCodeDescriptionPayee": "RESP ERRORCODE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "E05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI385", "StatusCodeDescriptionPayer": "RESP ERRORCODE SHOULD NOT BE PRESENT", "StatusCodeDescriptionPayee": "RESP ERRORCODE SHOULD NOT BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "E05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI386", "StatusCodeDescriptionPayer": "RESP ERRORCODE SHOULD NOT BE PRESENT", "StatusCodeDescriptionPayee": "RESP ERRORCODE SHOULD NOT BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "E06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI387", "StatusCodeDescriptionPayer": "RESP ERRORCODE MUST BE SUCCESS OR FAILURE", "StatusCodeDescriptionPayee": "RESP ERRORCODE MUST BE SUCCESS OR FAILURE", "DeclineType": "Technical"}, {"RawStatusCode": "E07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI388", "StatusCodeDescriptionPayer": "TYPE IS MANDATORY & ALPHANUMERIC", "StatusCodeDescriptionPayee": "TYPE IS MANDATORY & ALPHANUMERIC", "DeclineType": "Technical"}, {"RawStatusCode": "E08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI389", "StatusCodeDescriptionPayer": "SEQNUM IS MANDATORY & NUMERIC", "StatusCodeDescriptionPayee": "SEQNUM IS MANDATORY & NUMERIC", "DeclineType": "Technical"}, {"RawStatusCode": "E09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI390", "StatusCodeDescriptionPayer": "ADDR IS MANDATORY & ALPHANUMERIC", "StatusCodeDescriptionPayee": "ADDR IS MANDATORY & ALPHANUMERIC", "DeclineType": "Technical"}, {"RawStatusCode": "E10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI391", "StatusCodeDescriptionPayer": "SETTLEAMOUNT IS MANDATORY & DECIMAL", "StatusCodeDescriptionPayee": "SETTLEAMOUNT IS MANDATORY & DECIMAL", "DeclineType": "Technical"}, {"RawStatusCode": "E11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI392", "StatusCodeDescriptionPayer": "SETTLECURRENCY IS MANDATORY & TEXT", "StatusCodeDescriptionPayee": "SETTLECURRENCY IS MANDATORY & TEXT", "DeclineType": "Technical"}, {"RawStatusCode": "E12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI393", "StatusCodeDescriptionPayer": "APPROVALNUM IS MANDATORY & TEXT", "StatusCodeDescriptionPayee": "APPROVALNUM IS MANDATORY & TEXT", "DeclineType": "Technical"}, {"RawStatusCode": "E13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI394", "StatusCodeDescriptionPayer": "RES<PERSON><PERSON><PERSON> IS MANDATORY & ALPHANUMERIC AND MUST BE VALID", "StatusCodeDescriptionPayee": "RES<PERSON><PERSON><PERSON> IS MANDATORY & ALPHANUMERIC AND MUST BE VALID", "DeclineType": "Technical"}, {"RawStatusCode": "E14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI395", "StatusCodeDescriptionPayer": "SETTLEAMOUNT OF <PERSON><PERSON><PERSON><PERSON> CASES CANNOT BE MORE THAN ZERO", "StatusCodeDescriptionPayee": "SETTLEAMOUNT OF <PERSON><PERSON><PERSON><PERSON> CASES CANNOT BE MORE THAN ZERO", "DeclineType": "Technical"}, {"RawStatusCode": "E15", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI396", "StatusCodeDescriptionPayer": "INVALID RESPONSE CODE FOR THIS API", "StatusCodeDescriptionPayee": "INVALID RESPONSE CODE FOR THIS API", "DeclineType": "Technical"}, {"RawStatusCode": "E16", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI397", "StatusCodeDescriptionPayer": "REF ACNUM MUST BE OF MINLENGTH 1 MAXLENGTH 16", "StatusCodeDescriptionPayee": "REF ACNUM MUST BE OF MINLENGTH 1 MAXLENGTH 16", "DeclineType": "Business"}, {"RawStatusCode": "E17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI398", "StatusCodeDescriptionPayer": "REF CODE MUST BE OF LENGTH 4", "StatusCodeDescriptionPayee": "REF CODE MUST BE OF LENGTH 4", "DeclineType": "Technical"}, {"RawStatusCode": "E18", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI399", "StatusCodeDescriptionPayer": "REF IFSC MUST BE OF LENGTH 11", "StatusCodeDescriptionPayee": "REF IFSC MUST BE OF LENGTH 11", "DeclineType": "Business"}, {"RawStatusCode": "E19", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI400", "StatusCodeDescriptionPayer": "REF ACCTYPE MUST BE PRESENT", "StatusCodeDescriptionPayee": "REF ACCTYPE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "F01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI401", "StatusCodeDescriptionPayer": "REGDETAILS MUST BE PRESENT", "StatusCodeDescriptionPayee": "REGDETAILS MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "F02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI402", "StatusCodeDescriptionPayer": "REGDETAILS DETAIL MUST BE PRESENT", "StatusCodeDescriptionPayee": "REGDETAILS DETAIL MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "F03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI403", "StatusCodeDescriptionPayer": "REGDETAILS DETAIL NAME/VALUE SHOULD BE PRESENT", "StatusCodeDescriptionPayee": "REGDETAILS DETAIL NAME/VALUE SHOULD BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "F04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI404", "StatusCodeDescriptionPayer": "REGDETAILS DETAIL NAME NOT VALID", "StatusCodeDescriptionPayee": "REGDETAILS DETAIL NAME NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "F05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI405", "StatusCodeDescriptionPayer": "REGDETAILS CRED NOT PRESENT", "StatusCodeDescriptionPayee": "REGDETAILS CRED NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "F06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI406", "StatusCodeDescriptionPayer": "REGDETAILS CRED DATA IS WRONG", "StatusCodeDescriptionPayee": "REGDETAILS CRED DATA IS WRONG", "DeclineType": "Technical"}, {"RawStatusCode": "F07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI407", "StatusCodeDescriptionPayer": "REGDETAILS CRED OTP MUST BE PRESENT", "StatusCodeDescriptionPayee": "REGDETAILS CRED OTP MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "F08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI408", "StatusCodeDescriptionPayer": "REGDETAILS CRED PINMUST BE PRESENT", "StatusCodeDescriptionPayee": "REGDETAILS CRED PINMUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "F09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI409", "StatusCodeDescriptionPayer": "REGDETAILS CRED DATA MUST BE PRESENT", "StatusCodeDescriptionPayee": "REGDETAILS CRED DATA MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "F10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI410", "StatusCodeDescriptionPayer": "REG<PERSON><PERSON>ILS CRED DATA ENCRYPTED AUTHENTICATION MUST BE PRESENT", "StatusCodeDescriptionPayee": "REG<PERSON><PERSON>ILS CRED DATA ENCRYPTED AUTHENTICATION MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "FM2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI411", "StatusCodeDescriptionPayer": "REGDETAILS TYPE MUST BE FORMAT2", "StatusCodeDescriptionPayee": "REGDETAILS TYPE MUST BE FORMAT2", "DeclineType": "Technical"}, {"RawStatusCode": "G01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI412", "StatusCodeDescriptionPayer": "HBTMSG MUST BE PRESENT", "StatusCodeDescriptionPayee": "HBTMSG MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI413", "StatusCodeDescriptionPayer": "HBTMSG TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "HBTMSG TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI414", "StatusCodeDescriptionPayer": "VALUE NOT VALID FOR HBTMSG TYPE", "StatusCodeDescriptionPayee": "VALUE NOT VALID FOR HBTMSG TYPE", "DeclineType": "Technical"}, {"RawStatusCode": "G04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI415", "StatusCodeDescriptionPayer": "VALUE NOT VALID FOR HBTMSGRESP RESULT", "StatusCodeDescriptionPayee": "VALUE NOT VALID FOR HBTMSGRESP RESULT", "DeclineType": "Technical"}, {"RawStatusCode": "G05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI416", "StatusCodeDescriptionPayer": "VALUE NOT VALID FOR HBTMSGRESP ERRORCODE", "StatusCodeDescriptionPayee": "VALUE NOT VALID FOR HBTMSGRESP ERRORCODE", "DeclineType": "Technical"}, {"RawStatusCode": "G11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI417", "StatusCodeDescriptionPayer": "VAELIST VAE OP/NAME MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "VAELIST VAE OP/NAME MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI418", "StatusCodeDescriptionPayer": "VAELIST VAE NAME MUST BE PRESENT, MAXLENGTH 99", "StatusCodeDescriptionPayee": "VAELIST VAE NAME MUST BE PRESENT, MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "G13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI419", "StatusCodeDescriptionPayer": "VAELIST VAE ADDR MUST BE VALID VPA, MAXLENGTH 255", "StatusCodeDescriptionPayee": "VAELIST VAE ADDR MUST BE VALID VPA, MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "G14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI420", "StatusCodeDescriptionPayer": "VAELIST VAE LOGO MUST BE VALID, MAXLENGTH 255", "StatusCodeDescriptionPayee": "VAELIST VAE LOGO MUST BE VALID, MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "G15", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI421", "StatusCodeDescriptionPayer": "VAELIST VAE URL MUST BE VALID URL, MAXLENGTH 255", "StatusCodeDescriptionPayee": "VAELIST VAE URL MUST BE VALID URL, MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "G17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI422", "StatusCodeDescriptionPayer": "VAELIST VAE KEY CODE MUST BE PRESENT", "StatusCodeDescriptionPayee": "VAELIST VAE KEY CODE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G18", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI423", "StatusCodeDescriptionPayer": "VAELIST VAE KEY TYPE MUST BE PRESENT", "StatusCodeDescriptionPayee": "VAELIST VAE KEY TYPE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G19", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI424", "StatusCodeDescriptionPayer": "VAELIST VAE KEY KI MUST BE PRESENT", "StatusCodeDescriptionPayee": "VAELIST VAE KEY KI MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G20", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI425", "StatusCodeDescriptionPayer": "VAELIST VAE KEY KEYVALUE MUST BE PRESENT", "StatusCodeDescriptionPayee": "VAELIST VAE KEY KEYVALUE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G21", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI426", "StatusCodeDescriptionPayer": "REQMSG MUST BE PRESENT", "StatusCodeDescriptionPayee": "REQMSG MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G22", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI427", "StatusCodeDescriptionPayer": "REQMSG TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "REQMSG TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G23", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI428", "StatusCodeDescriptionPayer": "VALUE NOT VALID FOR REQMSG TYPE", "StatusCodeDescriptionPayee": "VALUE NOT VALID FOR REQMSG TYPE", "DeclineType": "Technical"}, {"RawStatusCode": "G24", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI429", "StatusCodeDescriptionPayer": "REQMSG ADDR MUST BE VALID VPA, MAXLENGTH 255", "StatusCodeDescriptionPayee": "REQMSG ADDR MUST BE VALID VPA, MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "G25", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI430", "StatusCodeDescriptionPayer": "CERTIFICATE NOT FOUND", "StatusCodeDescriptionPayee": "CERTIFICATE NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "G26", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI431", "StatusCodeDescriptionPayer": "SIGNATURE ERROR", "StatusCodeDescriptionPayee": "SIGNATURE ERROR", "DeclineType": "Technical"}, {"RawStatusCode": "G27", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI432", "StatusCodeDescriptionPayer": "SIGNATURE MISMATCH", "StatusCodeDescriptionPayee": "SIGNATURE MISMATCH", "DeclineType": "Technical"}, {"RawStatusCode": "G51", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI433", "StatusCodeDescriptionPayer": "RESP MERCHANT MUST BE PRESENT", "StatusCodeDescriptionPayee": "RESP MERCHANT MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G52", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI434", "StatusCodeDescriptionPayer": "RESP MSGID MUST BE PRESENT, MAXLENGTH 35", "StatusCodeDescriptionPayee": "RESP MSGID MUST BE PRESENT, MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "G53", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI435", "StatusCodeDescriptionPayer": "RESP RESULT MUST BE PRESENT ALPHANUMERIC, MIN LENGTH 1, MAX LENGTH 20", "StatusCodeDescriptionPayee": "RESP RESULT MUST BE PRESENT ALPHANUMERIC, MIN LENGTH 1, MAX LENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "G54", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI436", "StatusCodeDescriptionPayer": "RESP ERRORCODE MUST BE PRESENT", "StatusCodeDescriptionPayee": "RESP ERRORCODE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G55", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI437", "StatusCodeDescriptionPayer": "RESP ERRORCODE SHOULD NOT BE PRESENT", "StatusCodeDescriptionPayee": "RESP ERRORCODE SHOULD NOT BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G56", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI438", "StatusCodeDescriptionPayer": "RESP MASKNAME MUST BE PRESENT; MINLENGTH 1, MAXLENGTH 99", "StatusCodeDescriptionPayee": "RESP MASKNAME MUST BE PRESENT; MINLENGTH 1, MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "G61", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI439", "StatusCodeDescriptionPayer": "PAYER INFO MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER INFO MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G62", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI440", "StatusCodeDescriptionPayer": "PAYER INFO IDENTITY MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER INFO IDENTITY MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G63", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI441", "StatusCodeDescriptionPayer": "PAYER INFO IDENTITY TYPE MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER INFO IDENTITY TYPE MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "G64", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI442", "StatusCodeDescriptionPayer": "PAYER INFO IDENTITY VERIFIEDNAME MUST BE PRESENT, ALPHANUMERIC, MINLENGTH 1, MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYER INFO IDENTITY VERIFIEDNAME MUST BE PRESENT, ALPHANUMERIC, MINLENGTH 1, MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "G65", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI443", "StatusCodeDescriptionPayer": "PAYER INFO RATING VERIFIEDADDRESS MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 5", "StatusCodeDescriptionPayee": "PAYER INFO RATING VERIFIEDADDRESS MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 5", "DeclineType": "Technical"}, {"RawStatusCode": "G66", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI444", "StatusCodeDescriptionPayer": "ACCOUNT REFERENCE NUMBER MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "ACCOUNT REFERENCE NUMBER MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G67", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI445", "StatusCodeDescriptionPayer": "ACCOUNT TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "ACCOUNT TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G68", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI446", "StatusCodeDescriptionPayer": "ACCOUNT AEBA MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "ACCOUNT AEBA MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G69", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI447", "StatusCodeDescriptionPayer": "ACCOUNT MBEBA MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "ACCOUNT MBEBA MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G70", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI448", "StatusCodeDescriptionPayer": "ACCOUNT IFSC MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "ACCOUNT IFSC MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G71", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI449", "StatusCodeDescriptionPayer": "MASKED ACCOUNT NUMBER MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "MASKED ACCOUNT NUMBER MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G72", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI450", "StatusCodeDescriptionPayer": "ACCOUNT MMID MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "ACCOUNT MMID MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G73", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI451", "StatusCodeDescriptionPayer": "CREDS ALLOWED MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "CREDS ALLOWED MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G74", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI452", "StatusCodeDescriptionPayer": "INCORRECT CRED SUBTY<PERSON>E FOR THE MOBILE BANKING REGISTRATION FORMAT USED", "StatusCodeDescriptionPayee": "INCORRECT CRED SUBTY<PERSON>E FOR THE MOBILE BANKING REGISTRATION FORMAT USED", "DeclineType": "Business"}, {"RawStatusCode": "G75", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI453", "StatusCodeDescriptionPayer": "ACCOUNT NAME MUST BE PRESENT", "StatusCodeDescriptionPayee": "ACCOUNT NAME MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G76", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI454", "StatusCodeDescriptionPayer": "AADHAAR NUMBER MUST BE PRESENT IF AEBA=Y AND AADHAARCONSENT=Y", "StatusCodeDescriptionPayee": "AADHAAR NUMBER MUST BE PRESENT IF AEBA=Y AND AADHAARCONSENT=Y", "DeclineType": "Technical"}, {"RawStatusCode": "G77", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI455", "StatusCodeDescriptionPayer": "RESP CODE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "RESP CODE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G78", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI456", "StatusCodeDescriptionPayer": "RESP TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "RESP TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G79", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI457", "StatusCodeDescriptionPayer": "RESP IFSC MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "RESP IFSC MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G80", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI458", "StatusCodeDescriptionPayer": "RESP IIN MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "RESP IIN MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G81", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI459", "StatusCodeDescriptionPayer": "RESP SECUREPINURL MUST BE OF MINLENGTH 1 MAXLENGTH 100", "StatusCodeDescriptionPayee": "RESP SECUREPINURL MUST BE OF MINLENGTH 1 MAXLENGTH 100", "DeclineType": "Technical"}, {"RawStatusCode": "H02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI460", "StatusCodeDescriptionPayer": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "StatusCodeDescriptionPayee": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "DeclineType": "Technical"}, {"RawStatusCode": "H02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI461", "StatusCodeDescriptionPayer": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "StatusCodeDescriptionPayee": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "DeclineType": "Technical"}, {"RawStatusCode": "H02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI462", "StatusCodeDescriptionPayer": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "StatusCodeDescriptionPayee": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "DeclineType": "Technical"}, {"RawStatusCode": "H03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI463", "StatusCodeDescriptionPayer": "TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "H03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI464", "StatusCodeDescriptionPayer": "TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "H03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI465", "StatusCodeDescriptionPayer": "TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "H06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI466", "StatusCodeDescriptionPayer": "MSGID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "MSGID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "H06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI467", "StatusCodeDescriptionPayer": "MSGID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "MSGID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "H06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI468", "StatusCodeDescriptionPayer": "MSGID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "MSGID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "HM1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI469", "StatusCodeDescriptionPayer": "META TAG NAME MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "META TAG NAME MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "HM2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI470", "StatusCodeDescriptionPayer": "META TAG VALUE MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "META TAG VALUE MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "HV0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI471", "StatusCodeDescriptionPayer": "HEADER MUST BE PRESENT", "StatusCodeDescriptionPayee": "HEADER MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "HV1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI472", "StatusCodeDescriptionPayer": "HEADER VER DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "HEADER VER DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "HV2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI473", "StatusCodeDescriptionPayer": "HEADER TS MUST BE PRESENT", "StatusCodeDescriptionPayee": "HEADER TS MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "HV3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI474", "StatusCodeDescriptionPayer": "ORGID DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "ORGID DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "HV4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI475", "StatusCodeDescriptionPayer": "HEADER MSGID DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "HEADER MSGID DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "I01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI476", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "I01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI477", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "I02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI478", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO IDENTITY MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO IDENTITY MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "I02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI479", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO IDENTITY MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO IDENTITY MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "I03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI480", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO IDENTITY TYPE MUST BE PRESENT MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO IDENTITY TYPE MUST BE PRESENT MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "I03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI481", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO IDENTITY TYPE MUST BE PRESENT MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO IDENTITY TYPE MUST BE PRESENT MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "I04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI482", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO IDENTITY VERIFIEDNAME MUST BE PRESENT ALPHANUMERIC MINLENGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO IDENTITY VERIFIEDNAME MUST BE PRESENT ALPHANUMERIC MINLENGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "I04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI483", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO IDENTITY VERIFIEDNAME MUST BE PRESENT ALPHANUMERIC MINLENGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO IDENTITY VERIFIEDNAME MUST BE PRESENT ALPHANUMERIC MINLENGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "I05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI484", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO RATING WHITELISTED MUST BE PRESENT MINLENGTH 1 MAXLENGTH 5", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO RATING WHITELISTED MUST BE PRESENT MINLENGTH 1 MAXLENGTH 5", "DeclineType": "Technical"}, {"RawStatusCode": "I05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI485", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO RATING WHITELISTED MUST BE PRESENT MINLENGTH 1 MAXLENGTH 5", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO RATING WHITELISTED MUST BE PRESENT MINLENGTH 1 MAXLENGTH 5", "DeclineType": "Technical"}, {"RawStatusCode": "IM0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI486", "StatusCodeDescriptionPayer": "INITIATIONMODE SHOULD BE PRESENT AND VALUE(00-32)", "StatusCodeDescriptionPayee": "INITIATIONMODE SHOULD BE PRESENT AND VALUE(00-32)", "DeclineType": "Technical"}, {"RawStatusCode": "IM0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI487", "StatusCodeDescriptionPayer": "INITIATIONMODE SHOULD BE PRESENT AND VALUE(00-32)", "StatusCodeDescriptionPayee": "INITIATIONMODE SHOULD BE PRESENT AND VALUE(00-32)", "DeclineType": "Technical"}, {"RawStatusCode": "IM0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI488", "StatusCodeDescriptionPayer": "INITIATIONMODE SHOULD BE PRESENT AND VALUE(00-32)", "StatusCodeDescriptionPayee": "INITIATIONMODE SHOULD BE PRESENT AND VALUE(00-32)", "DeclineType": "Technical"}, {"RawStatusCode": "IM1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI489", "StatusCodeDescriptionPayer": "INITIATIONMODE=12 (FIR) NOT VALID FOR COLLECT", "StatusCodeDescriptionPayee": "INITIATIONMODE=12 (FIR) NOT VALID FOR COLLECT", "DeclineType": "Technical"}, {"RawStatusCode": "IM2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI490", "StatusCodeDescriptionPayer": "INITIATIONMODE=12 (FIR) NON-PREAPPROVED TRANSACTIONIS NOT ALLOWED", "StatusCodeDescriptionPayee": "INITIATIONMODE=12 (FIR) NON-PREAPPROVED TRANSACTIONIS NOT ALLOWED", "DeclineType": "Business"}, {"RawStatusCode": "IM3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI491", "StatusCodeDescriptionPayer": "CRED BLOCK SHOULD BE UPIMANDATE OR PREAPPROVED IF INITIATIONMODE=11", "StatusCodeDescriptionPayee": "CRED BLOCK SHOULD BE UPIMANDATE OR PREAPPROVED IF INITIATIONMODE=11", "DeclineType": "Technical"}, {"RawStatusCode": "IM3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI492", "StatusCodeDescriptionPayer": "CRED BLOCK SHOULD BE UPIMANDATE OR PREAPPROVED IF INITIATIONMODE=11", "StatusCodeDescriptionPayee": "CRED BLOCK SHOULD BE UPIMANDATE OR PREAPPROVED IF INITIATIONMODE=11", "DeclineType": "Technical"}, {"RawStatusCode": "IM4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI493", "StatusCodeDescriptionPayer": "UPI 2 0 IS ALLOWING FIR ONLY", "StatusCodeDescriptionPayee": "UPI 2 0 IS ALLOWING FIR ONLY", "DeclineType": "Technical"}, {"RawStatusCode": "IM5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI494", "StatusCodeDescriptionPayer": "Beneficiary PSP does not support UPI 2 0", "StatusCodeDescriptionPayee": "Beneficiary PSP does not support UPI 2 0", "DeclineType": "Business"}, {"RawStatusCode": "IM6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI495", "StatusCodeDescriptionPayer": "Partner Bank/PSP does not support UPI 2 0", "StatusCodeDescriptionPayee": "Partner Bank/PSP does not support UPI 2 0", "DeclineType": "Business"}, {"RawStatusCode": "J01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI496", "StatusCodeDescriptionPayer": "PAYEE NOT PRESENT", "StatusCodeDescriptionPayee": "PAYEE NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "J02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI497", "StatusCodeDescriptionPayer": "PAYEE ADDR MUST BE VALID VPA, MAXLENGTH 255", "StatusCodeDescriptionPayee": "PAYEE ADDR MUST BE VALID VPA, MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "J03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI498", "StatusCodeDescriptionPayer": "PAYEE NAME ALPHANUMERIC, MINLENGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYEE NAME ALPHANUMERIC, MINLENGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "J04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI499", "StatusCodeDescriptionPayer": "PAYEE SEQNUM NUMERIC, MINLENGTH 1 MAXLENGTH 3", "StatusCodeDescriptionPayee": "PAYEE SEQNUM NUMERIC, MINLENGTH 1 MAXLENGTH 3", "DeclineType": "Technical"}, {"RawStatusCode": "J07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI500", "StatusCodeDescriptionPayer": "PAYEE CODE NUMERIC OF LENGTH 4", "StatusCodeDescriptionPayee": "PAYEE CODE NUMERIC OF LENGTH 4", "DeclineType": "Technical"}, {"RawStatusCode": "J08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI501", "StatusCodeDescriptionPayer": "PAYEE TYPE MUST BE VALID", "StatusCodeDescriptionPayee": "PAYEE TYPE MUST BE VALID", "DeclineType": "Technical"}, {"RawStatusCode": "K01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI502", "StatusCodeDescriptionPayer": "PAYER/PAYEE AC MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE AC MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "K02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI503", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT ADDRTYPE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT ADDRTYPE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "K03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI504", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "K04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI505", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT NAME MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT NAME MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "K05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI506", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL AADHAR MUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL AADHAR MUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "K06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI507", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL ACCOUNT MUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL ACCOUNT MUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "K07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI508", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL MOBILE MUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL MOBILE MUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "K08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI509", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL CARD MUST BE PRESENT OR NOT VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL CARD MUST BE PRESENT OR NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "K09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI510", "StatusCodeDescriptionPayer": "PAYER/PAYEE ACCOUNT DETAIL VALUE MUST BE PRESENT FOR / NAME", "StatusCodeDescriptionPayee": "PAYER/PAYEE ACCOUNT DETAIL VALUE MUST BE PRESENT FOR / NAME", "DeclineType": "Technical"}, {"RawStatusCode": "K16", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI511", "StatusCodeDescriptionPayer": "REQUESTED PSP KEY IS NOT PRESENT IN UPI", "StatusCodeDescriptionPayee": "REQUESTED PSP KEY IS NOT PRESENT IN UPI", "DeclineType": "Technical"}, {"RawStatusCode": "K16", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI512", "StatusCodeDescriptionPayer": "REQUESTED PSP KEY IS NOT PRESENT IN UPI", "StatusCodeDescriptionPayee": "REQUESTED PSP KEY IS NOT PRESENT IN UPI", "DeclineType": "Technical"}, {"RawStatusCode": "KI1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI513", "StatusCodeDescriptionPayer": "VAE KEY TAGS MUST BE PRESENT", "StatusCodeDescriptionPayee": "VAE KEY TAGS MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "KI2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI514", "StatusCodeDescriptionPayer": "KEY CODE MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 20", "StatusCodeDescriptionPayee": "KEY CODE MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "KI3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI515", "StatusCodeDescriptionPayer": "KEY TYPE MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 20", "StatusCodeDescriptionPayee": "KEY TYPE MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "KI4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI516", "StatusCodeDescriptionPayer": "KEY KI MUST BE PRESENT IN FORMAT YYYYMMDD", "StatusCodeDescriptionPayee": "KEY KI MUST BE PRESENT IN FORMAT YYYYMMDD", "DeclineType": "Technical"}, {"RawStatusCode": "KI5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI517", "StatusCodeDescriptionPayer": "KEY KEYVALUE MUST BE PRESENT", "StatusCodeDescriptionPayee": "KEY KEYVALUE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "L01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI518", "StatusCodeDescriptionPayer": "RULE MUST BE PRESENT WITHIN RULES", "StatusCodeDescriptionPayee": "RULE MUST BE PRESENT WITHIN RULES", "DeclineType": "Technical"}, {"RawStatusCode": "L02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI519", "StatusCodeDescriptionPayer": "RULE ATTRIBUTE NAME MUST BE PRESENT; ALP<PERSON>NUMERIC; MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "RULE ATTRIBUTE NAME MUST BE PRESENT; ALP<PERSON>NUMERIC; MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "L03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI520", "StatusCodeDescriptionPayer": "VALUE MUST BE PRESENT; NUMERIC/DECIMAL", "StatusCodeDescriptionPayee": "VALUE MUST BE PRESENT; NUMERIC/DECIMAL", "DeclineType": "Technical"}, {"RawStatusCode": "L04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI521", "StatusCodeDescriptionPayer": "RULE ATTRIBUTE VALUE MUST BE PRESENT; NUMERIC; MINLENGTH 1 MAXLENGTH 255", "StatusCodeDescriptionPayee": "RULE ATTRIBUTE VALUE MUST BE PRESENT; NUMERIC; MINLENGTH 1 MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "L05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI522", "StatusCodeDescriptionPayer": "TECHNICAL ISSUE, PLEASE TRY AFTER SOME TIME", "StatusCodeDescriptionPayee": "TECHNICAL ISSUE, PLEASE TRY AFTER SOME TIME", "DeclineType": "Technical"}, {"RawStatusCode": "M01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI523", "StatusCodeDescriptionPayer": "PAYEE AMOUNT CUR MUST BE CONSISTENT", "StatusCodeDescriptionPayee": "PAYEE AMOUNT CUR MUST BE CONSISTENT", "DeclineType": "Technical"}, {"RawStatusCode": "M01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI524", "StatusCodeDescriptionPayer": "PAYEE AMOUNT CUR MUST BE CONSISTENT", "StatusCodeDescriptionPayee": "PAYEE AMOUNT CUR MUST BE CONSISTENT", "DeclineType": "Technical"}, {"RawStatusCode": "M02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI525", "StatusCodeDescriptionPayer": "PAYEE AMOUNT CUR IS INVALID", "StatusCodeDescriptionPayee": "PAYEE AMOUNT CUR IS INVALID", "DeclineType": "Technical"}, {"RawStatusCode": "M02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI526", "StatusCodeDescriptionPayer": "PAYEE AMOUNT CUR IS INVALID", "StatusCodeDescriptionPayee": "PAYEE AMOUNT CUR IS INVALID", "DeclineType": "Technical"}, {"RawStatusCode": "M03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI527", "StatusCodeDescriptionPayer": "PAYER & PAYEE TOTAL AMOUNT NOT MATCHING", "StatusCodeDescriptionPayee": "PAYER & PAYEE TOTAL AMOUNT NOT MATCHING", "DeclineType": "Technical"}, {"RawStatusCode": "M03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI528", "StatusCodeDescriptionPayer": "PAYER & PAYEE TOTAL AMOUNT NOT MATCHING", "StatusCodeDescriptionPayee": "PAYER & PAYEE TOTAL AMOUNT NOT MATCHING", "DeclineType": "Technical"}, {"RawStatusCode": "M04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI529", "StatusCodeDescriptionPayer": "ONE OR MORE PAYEE AMOUNT IS MISSING", "StatusCodeDescriptionPayee": "ONE OR MORE PAYEE AMOUNT IS MISSING", "DeclineType": "Technical"}, {"RawStatusCode": "M04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI530", "StatusCodeDescriptionPayer": "ONE OR MORE PAYEE AMOUNT IS MISSING", "StatusCodeDescriptionPayee": "ONE OR MORE PAYEE AMOUNT IS MISSING", "DeclineType": "Technical"}, {"RawStatusCode": "M05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI531", "StatusCodeDescriptionPayer": "PAYER AND PAYEE TOTAL AMOUNT NOT MATCHING", "StatusCodeDescriptionPayee": "PAYER AND PAYEE TOTAL AMOUNT NOT MATCHING", "DeclineType": "Technical"}, {"RawStatusCode": "M06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI532", "StatusCodeDescriptionPayer": "MORE THAN ONE PAYEE AMOUNT IS MISSING", "StatusCodeDescriptionPayee": "MORE THAN ONE PAYEE AMOUNT IS MISSING", "DeclineType": "Technical"}, {"RawStatusCode": "M07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI533", "StatusCodeDescriptionPayer": "PAYER AMOUNT SHOULD BE GREATER THAN TOTAL PAYEE AMOUNT", "StatusCodeDescriptionPayee": "PAYER AMOUNT SHOULD BE GREATER THAN TOTAL PAYEE AMOUNT", "DeclineType": "Technical"}, {"RawStatusCode": "M2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI534", "StatusCodeDescriptionPayer": "AMOUNT <PERSON>IMIT EXCEEDED FOR CUSTOMER", "StatusCodeDescriptionPayee": "AMOUNT <PERSON>IMIT EXCEEDED FOR CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "MA0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI535", "StatusCodeDescriptionPayer": "MANDATE NOT PRESENT", "StatusCodeDescriptionPayee": "MANDATE NOT PRESENT", "DeclineType": "Business"}, {"RawStatusCode": "MA1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI536", "StatusCodeDescriptionPayer": "MANDATE NAME ALPHANUMERIC; MINLENGTH 1 , MAXLENGTH 99", "StatusCodeDescriptionPayee": "MANDATE NAME ALPHANUMERIC; MINLENGTH 1 , MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "MA10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI537", "StatusCodeDescriptionPayer": "MANDATE UMN MUST BE PRESENT", "StatusCodeDescriptionPayee": "MANDATE UMN MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "MA11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI538", "StatusCodeDescriptionPayer": "MANDATE UMNSHOULD NOT BE PRESENT FOR PAYEE INITIATED TRANSACTION", "StatusCodeDescriptionPayee": "MANDATE UMNSHOULD NOT BE PRESENT FOR PAYEE INITIATED TRANSACTION", "DeclineType": "Technical"}, {"RawStatusCode": "MA2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI539", "StatusCodeDescriptionPayer": "MANDATE TXNID MUST BE PRESENT, MUST BE 35 CHARACTERS OF ALPHANUMERIC", "StatusCodeDescriptionPayee": "MANDATE TXNID MUST BE PRESENT, MUST BE 35 CHARACTERS OF ALPHANUMERIC", "DeclineType": "Technical"}, {"RawStatusCode": "MA3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI540", "StatusCodeDescriptionPayer": "MANDATE TXNID AND TXN ID MUST BE SAME", "StatusCodeDescriptionPayee": "MANDATE TXNID AND TXN ID MUST BE SAME", "DeclineType": "Technical"}, {"RawStatusCode": "MA4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI541", "StatusCodeDescriptionPayer": "MANDATE UMN MUST BE PRESENT", "StatusCodeDescriptionPayee": "MANDATE UMN MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "MA5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI542", "StatusCodeDescriptionPayer": "MANDATE UMN MUST BE PRESENT, LENGTH 32", "StatusCodeDescriptionPayee": "MANDATE UMN MUST BE PRESENT, LENGTH 32", "DeclineType": "Technical"}, {"RawStatusCode": "MA6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI543", "StatusCodeDescriptionPayer": "MANDATE TS MUST BE PRESENT AND SHOULD BE IN ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "MANDATE TS MUST BE PRESENT AND SHOULD BE IN ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "MA7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI544", "StatusCodeDescriptionPayer": "MANDATE REVOKEABLE MUST BE PRESENT, REVOKABLE TYPE MUST BE Y OR N", "StatusCodeDescriptionPayee": "MANDATE REVOKEABLE MUST BE PRESENT, REVOKABLE TYPE MUST BE Y OR N", "DeclineType": "Technical"}, {"RawStatusCode": "MA8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI545", "StatusCodeDescriptionPayer": "MANDATE SHARETOPAYEE 'N' IS APPLICABLE ONLY FOR ONETIME RECURRENCE PATTERN AS WELL AS FOR PAYER INITIATED TRANSACTIONS ONLY", "StatusCodeDescriptionPayee": "MANDATE SHARETOPAYEE 'N' IS APPLICABLE ONLY FOR ONETIME RECURRENCE PATTERN AS WELL AS FOR PAYER INITIATED TRANSACTIONS ONLY", "DeclineType": "Business"}, {"RawStatusCode": "MA9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI546", "StatusCodeDescriptionPayer": "MANDATE VALIDITY MUST BE PRESENT", "StatusCodeDescriptionPayee": "MANDATE VALIDITY MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "MB0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI547", "StatusCodeDescriptionPayer": "MANDATE VALIDITY START MUST BE PRESENT, DATE FORMAT DDMMYYYY, START DATE MUST BE TODAY'S DATE OR LATER", "StatusCodeDescriptionPayee": "MANDATE VALIDITY START MUST BE PRESENT, DATE FORMAT DDMMYYYY, START DATE MUST BE TODAY'S DATE OR LATER", "DeclineType": "Technical"}, {"RawStatusCode": "MB1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI548", "StatusCodeDescriptionPayer": "MANDATE VALIDITY END MUST BE PRESENT, DATE FORMAT DDMMYYYY, END DATE MUST BE GREATER THAN TODAY'S DATE", "StatusCodeDescriptionPayee": "MANDATE VALIDITY END MUST BE PRESENT, DATE FORMAT DDMMYYYY, END DATE MUST BE GREATER THAN TODAY'S DATE", "DeclineType": "Technical"}, {"RawStatusCode": "MB2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI549", "StatusCodeDescriptionPayer": "MANDATE AMOUNT MUST BE PRESENT, VALUE AND RULE SHOULD NOT BE EMPTY", "StatusCodeDescriptionPayee": "MANDATE AMOUNT MUST BE PRESENT, VALUE AND RULE SHOULD NOT BE EMPTY", "DeclineType": "Technical"}, {"RawStatusCode": "MB3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI550", "StatusCodeDescriptionPayer": "MANDATE AMOUNT RULE MUST BE PRESENT, RULE MUST BE EXACT/MAX", "StatusCodeDescriptionPayee": "MANDATE AMOUNT RULE MUST BE PRESENT, RULE MUST BE EXACT/MAX", "DeclineType": "Technical"}, {"RawStatusCode": "MB4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI551", "StatusCodeDescriptionPayer": "MANDATE RECURRENCE MUST BE PRESENT, <PERSON><PERSON><PERSON><PERSON> RECURRENCE PATTERN MUST BE ONETIME OR DAILY OR WEEKLY", "StatusCodeDescriptionPayee": "MANDATE RECURRENCE MUST BE PRESENT, <PERSON><PERSON><PERSON><PERSON> RECURRENCE PATTERN MUST BE ONETIME OR DAILY OR WEEKLY", "DeclineType": "Technical"}, {"RawStatusCode": "MB5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI552", "StatusCodeDescriptionPayer": "MANDATE RECURRENCE RULE MUST BE PRESENT, <PERSON><PERSON><PERSON><PERSON> RECURRENCE RULE TYPE MUST BE AFTER OR ON OR BEFORE", "StatusCodeDescriptionPayee": "MANDATE RECURRENCE RULE MUST BE PRESENT, <PERSON><PERSON><PERSON><PERSON> RECURRENCE RULE TYPE MUST BE AFTER OR ON OR BEFORE", "DeclineType": "Technical"}, {"RawStatusCode": "MB6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI553", "StatusCodeDescriptionPayer": "MANDATE RECURRENCE RULE MUST BE PRESENT, MAND<PERSON><PERSON> RECURRENCE RULE VALUE IN BETWEEN 1 TO 7 ONLY WHEN MANDATE RECURRENCE PATTERN IS WEEKLY", "StatusCodeDescriptionPayee": "MANDATE RECURRENCE RULE MUST BE PRESENT, MAND<PERSON><PERSON> RECURRENCE RULE VALUE IN BETWEEN 1 TO 7 ONLY WHEN MANDATE RECURRENCE PATTERN IS WEEKLY", "DeclineType": "Technical"}, {"RawStatusCode": "MB7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI554", "StatusCodeDescriptionPayer": "MANDATE RECURRENCE RULE NOT APPLICABLE FOR MANDATE RECURRENCE PATTERN ONETIME/DAILY/WEEKLY/FORTNIGHTLY/MONTHLY/ BIMONTHLY/QUARTERLY/HALFYEARLY/YEARLY/ASPRESENTED", "StatusCodeDescriptionPayee": "MANDATE RECURRENCE RULE NOT APPLICABLE FOR MANDATE RECURRENCE PATTERN ONETIME/DAILY/WEEKLY/FORTNIGHTLY/MONTHLY/ BIMONTHLY/QUARTERLY/HALFYEARLY/YEARLY/ASPRESENTED", "DeclineType": "Technical"}, {"RawStatusCode": "MC3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI555", "StatusCodeDescriptionPayer": "MANDATE UMNCANNOT BE GENERATED BY PAYEE", "StatusCodeDescriptionPayee": "MANDATE UMNCANNOT BE GENERATED BY PAYEE", "DeclineType": "Technical"}, {"RawStatusCode": "MC4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI556", "StatusCodeDescriptionPayer": "GLOBAL ADDRESS NOT SUPPORTED IN MANDATE", "StatusCodeDescriptionPayee": "GLOBAL ADDRESS NOT SUPPORTED IN MANDATE", "DeclineType": "Business"}, {"RawStatusCode": "MN0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI557", "StatusCodeDescriptionPayer": "MANDATE TAG DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE TAG DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MN1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI558", "StatusCodeDescriptionPayer": "MANDATE NAME DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE NAME DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MN2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI559", "StatusCodeDescriptionPayer": "MANDATE TXNID DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE TXNID DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MN3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI560", "StatusCodeDescriptionPayer": "MANDATE UMNDIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE UMNDIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MN4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI561", "StatusCodeDescriptionPayer": "MANDATE TS DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE TS DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MN5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI562", "StatusCodeDescriptionPayer": "MANDATE REVOKE<PERSON>LE DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE REVOKE<PERSON>LE DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MN6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI563", "StatusCodeDescriptionPayer": "MANDATE SHARETOPAYEE DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE SHARETOPAYEE DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MN7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI564", "StatusCodeDescriptionPayer": "MANDATE BLOCKFUND DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE BLOCKFUND DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MN8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI565", "StatusCodeDescriptionPayer": "MANDATE TYPE DIFFERS FROM ORIGINAL REQUEST, <PERSON><PERSON><PERSON><PERSON> AMOUNT DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE TYPE DIFFERS FROM ORIGINAL REQUEST, <PERSON><PERSON><PERSON><PERSON> AMOUNT DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MN9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI566", "StatusCodeDescriptionPayer": "MANDATE RECURRENCE PATTERN DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE RECURRENCE PATTERN DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MP0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI567", "StatusCodeDescriptionPayer": "BLOCKFUND IS ALLOWED ONLY IF THE PURPOSE=01", "StatusCodeDescriptionPayee": "BLOCKFUND IS ALLOWED ONLY IF THE PURPOSE=01", "DeclineType": "Business"}, {"RawStatusCode": "MP1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI568", "StatusCodeDescriptionPayer": "BLOCKFUND=Y IS ALLOWED FOR CREATE/UPDATE AND BLOCKFUND=N IS ALLOWED FOR REVOKE", "StatusCodeDescriptionPayee": "BLOCKFUND=Y IS ALLOWED FOR CREATE/UPDATE AND BLOCKFUND=N IS ALLOWED FOR REVOKE", "DeclineType": "Business"}, {"RawStatusCode": "MP2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI569", "StatusCodeDescriptionPayer": "MANDATE AMOUNT CAN ONLY BE UPDATED IF PURPOSE=01", "StatusCodeDescriptionPayee": "MANDATE AMOUNT CAN ONLY BE UPDATED IF PURPOSE=01", "DeclineType": "Business"}, {"RawStatusCode": "MP3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI570", "StatusCodeDescriptionPayer": "RECURRENCE PATTERN IS ALWAYS ONETIME IF PURPOSE=01", "StatusCodeDescriptionPayee": "RECURRENCE PATTERN IS ALWAYS ONETIME IF PURPOSE=01", "DeclineType": "Business"}, {"RawStatusCode": "MP4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI571", "StatusCodeDescriptionPayer": "AMOUNT RULE SHOULD BE ALWAYS MAX IF PURPOSE=01", "StatusCodeDescriptionPayee": "AMOUNT RULE SHOULD BE ALWAYS MAX IF PURPOSE=01", "DeclineType": "Business"}, {"RawStatusCode": "MP5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI572", "StatusCodeDescriptionPayer": "SHARETOPAYEE=Y FOR PAYER INITIATED IF PURPOSE=01", "StatusCodeDescriptionPayee": "SHARETOPAYEE=Y FOR PAYER INITIATED IF PURPOSE=01", "DeclineType": "Business"}, {"RawStatusCode": "MV0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI573", "StatusCodeDescriptionPayer": "MANDATE VALIDITY DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE VALIDITY DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MV1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI574", "StatusCodeDescriptionPayer": "MANDATE VALIDITY START DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE VALIDITY START DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "MV2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI575", "StatusCodeDescriptionPayer": "MANDATE VALIDITY END DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "MANDATE VALIDITY END DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "N01 - N10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI576", "StatusCodeDescriptionPayer": "SAME VALIDATION LIKE CREDS", "StatusCodeDescriptionPayee": "SAME VALIDATION LIKE CREDS", "DeclineType": "Technical"}, {"RawStatusCode": "O01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI577", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "O02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI578", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO IDENTITY MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO IDENTITY MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "O03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI579", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO IDENTITY TYPE MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO IDENTITY TYPE MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "O04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI580", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO IDENTITY VERIFIEDNAME MUST BE PRESENT, ALPHANUMERIC, MINLENGTH 1, MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO IDENTITY VERIFIEDNAME MUST BE PRESENT, ALPHANUMERIC, MINLENGTH 1, MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "O05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI581", "StatusCodeDescriptionPayer": "PAYER/PAYEE INFO RATING WHITELISTED MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 5", "StatusCodeDescriptionPayee": "PAYER/PAYEE INFO RATING WHITELISTED MUST BE PRESENT, MINLENGTH 1, MAXLENGTH 5", "DeclineType": "Technical"}, {"RawStatusCode": "OR1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI582", "StatusCodeDescriptionPayer": "REFUND IS ALLOWED ONLY FOR 2 0", "StatusCodeDescriptionPayee": "REFUND IS ALLOWED ONLY FOR 2 0", "DeclineType": "Business"}, {"RawStatusCode": "OR2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI583", "StatusCodeDescriptionPayer": "TXN ORGTNXID MUST BE PRESENT/ VALID FOR REFUND; ALPHANUMERIC; MINLENGTH 1 , <PERSON><PERSON><PERSON><PERSON><PERSON> 35 GOING SUCCESS", "StatusCodeDescriptionPayee": "TXN ORGTNXID MUST BE PRESENT/ VALID FOR REFUND; ALPHANUMERIC; MINLENGTH 1 , <PERSON><PERSON><PERSON><PERSON><PERSON> 35 GOING SUCCESS", "DeclineType": "Technical"}, {"RawStatusCode": "OR3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI584", "StatusCodeDescriptionPayer": "TXN ORGTNXDATE MUST BE PRESENT/ VALID FOR REFUND", "StatusCodeDescriptionPayee": "TXN ORGTNXDATE MUST BE PRESENT/ VALID FOR REFUND", "DeclineType": "Technical"}, {"RawStatusCode": "OR4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI585", "StatusCodeDescriptionPayer": "TXN ORGRRN MUST BE PRESENT/ VALID FOR REFUND; NUMERIC; LENGTH 12", "StatusCodeDescriptionPayee": "TXN ORGRRN MUST BE PRESENT/ VALID FOR REFUND; NUMERIC; LENGTH 12", "DeclineType": "Technical"}, {"RawStatusCode": "OR5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI586", "StatusCodeDescriptionPayer": "PAYEE GLOBAL ADDRESS MUST BE ACCT NUMBER AND IFSC ONLY FOR REFUND", "StatusCodeDescriptionPayee": "PAYEE GLOBAL ADDRESS MUST BE ACCT NUMBER AND IFSC ONLY FOR REFUND", "DeclineType": "Technical"}, {"RawStatusCode": "OR6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI587", "StatusCodeDescriptionPayer": "PAYEE ACCOUNT <PERSON>TA<PERSON>S MUST BE PRESENT OR VPA MUST BE GLOBAL ADDRESS(A/C NO &AMP; IFSC)", "StatusCodeDescriptionPayee": "PAYEE ACCOUNT <PERSON>TA<PERSON>S MUST BE PRESENT OR VPA MUST BE GLOBAL ADDRESS(A/C NO &AMP; IFSC)", "DeclineType": "Technical"}, {"RawStatusCode": "OR7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI588", "StatusCodeDescriptionPayer": "PAYER MUST BE ENTITY FOR TYPE REFUND", "StatusCodeDescriptionPayee": "PAYER MUST BE ENTITY FOR TYPE REFUND", "DeclineType": "Technical"}, {"RawStatusCode": "OR8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI589", "StatusCodeDescriptionPayer": "PAYEE MUST BE PERSON FOR TYPE REFUND", "StatusCodeDescriptionPayee": "PAYEE MUST BE PERSON FOR TYPE REFUND", "DeclineType": "Technical"}, {"RawStatusCode": "OR9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI590", "StatusCodeDescriptionPayer": "PAYER CRED PREAPPROVED MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER CRED PREAPPROVED MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "P01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI591", "StatusCodeDescriptionPayer": "PAYER NOT PRESENT", "StatusCodeDescriptionPayee": "PAYER NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "P01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI592", "StatusCodeDescriptionPayer": "PAYER NOT PRESENT", "StatusCodeDescriptionPayee": "PAYER NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "P02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI593", "StatusCodeDescriptionPayer": "PAYER ADDR MUST BE VALID VPA MAXLENGTH 255", "StatusCodeDescriptionPayee": "PAYER ADDR MUST BE VALID VPA MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "P02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI594", "StatusCodeDescriptionPayer": "PAYER ADDR MUST BE VALID VPA MAXLENGTH 255", "StatusCodeDescriptionPayee": "PAYER ADDR MUST BE VALID VPA MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "P03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI595", "StatusCodeDescriptionPayer": "PAYER NAME ALPHANUMERIC MINLEGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYER NAME ALPHANUMERIC MINLEGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "P03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI596", "StatusCodeDescriptionPayer": "PAYER NAME ALPHANUMERIC MINLEGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYER NAME ALPHANUMERIC MINLEGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "P04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI597", "StatusCodeDescriptionPayer": "PAYER SEQNUM NUMERIC MINLEGTH 1 MAXLENGTH 3", "StatusCodeDescriptionPayee": "PAYER SEQNUM NUMERIC MINLEGTH 1 MAXLENGTH 3", "DeclineType": "Technical"}, {"RawStatusCode": "P04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI598", "StatusCodeDescriptionPayer": "PAYER SEQNUM NUMERIC MINLEGTH 1 MAXLENGTH 3", "StatusCodeDescriptionPayee": "PAYER SEQNUM NUMERIC MINLEGTH 1 MAXLENGTH 3", "DeclineType": "Technical"}, {"RawStatusCode": "P05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI599", "StatusCodeDescriptionPayer": "PAYER TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "PAYER TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "P05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI600", "StatusCodeDescriptionPayer": "PAYER TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "PAYER TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "P06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI601", "StatusCodeDescriptionPayer": "PAYER CODE NUMERIC OF LENGTH 4", "StatusCodeDescriptionPayee": "PAYER CODE NUMERIC OF LENGTH 4", "DeclineType": "Technical"}, {"RawStatusCode": "P06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI602", "StatusCodeDescriptionPayer": "PAYER CODE NUMERIC OF LENGTH 4", "StatusCodeDescriptionPayee": "PAYER CODE NUMERIC OF LENGTH 4", "DeclineType": "Technical"}, {"RawStatusCode": "P07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI603", "StatusCodeDescriptionPayer": "PAYER BAL MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER BAL MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "P08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI604", "StatusCodeDescriptionPayer": "PAYER BAL DATA MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER BAL DATA MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "P09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI605", "StatusCodeDescriptionPayer": "PAYER AADHAARCONSENT MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER AADHAARCONSENT MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "PA0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI606", "StatusCodeDescriptionPayer": "PAYER/PAYEES AC DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER/PAYEES AC DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PA1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI607", "StatusCodeDescriptionPayer": "PAYER/PAYEES ACCOUNT ADDRTY<PERSON><PERSON> DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER/PAYEES ACCOUNT ADDRTY<PERSON><PERSON> DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PA2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI608", "StatusCodeDescriptionPayer": "PAYER/PAYEES ACCOUNT DE<PERSON>ILS DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER/PAYEES ACCOUNT DE<PERSON>ILS DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PA3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI609", "StatusCodeDescriptionPayer": "PAYER/PAYEES ACCOUNT DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER/PAYEES ACCOUNT DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PI0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI610", "StatusCodeDescriptionPayer": "INSTITUTION TAG SHOULD BE PRESENT IF INITIATIONMODE=12 (FIR)", "StatusCodeDescriptionPayee": "INSTITUTION TAG SHOULD BE PRESENT IF INITIATIONMODE=12 (FIR)", "DeclineType": "Technical"}, {"RawStatusCode": "PI1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI611", "StatusCodeDescriptionPayer": "PAYER INSTITUTION TYPE MUST BE PRESENT AMONG MTO|BANK", "StatusCodeDescriptionPayee": "PAYER INSTITUTION TYPE MUST BE PRESENT AMONG MTO|BANK", "DeclineType": "Technical"}, {"RawStatusCode": "PI2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI612", "StatusCodeDescriptionPayer": "PAYER INSTITUTION ROUTE MUST BE PRESENT AMONG MTSS|RDA", "StatusCodeDescriptionPayee": "PAYER INSTITUTION ROUTE MUST BE PRESENT AMONG MTSS|RDA", "DeclineType": "Technical"}, {"RawStatusCode": "PI3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI613", "StatusCodeDescriptionPayer": "PAYER INSTITUTION NAME MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER INSTITUTION NAME MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "PI4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI614", "StatusCodeDescriptionPayer": "PAYER INSTITUTION NAME VALUE MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 100", "StatusCodeDescriptionPayee": "PAYER INSTITUTION NAME VALUE MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 100", "DeclineType": "Technical"}, {"RawStatusCode": "PI5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI615", "StatusCodeDescriptionPayer": "PAYER INSTITUTION NAME ACNUM MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 50", "StatusCodeDescriptionPayee": "PAYER INSTITUTION NAME ACNUM MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 50", "DeclineType": "Technical"}, {"RawStatusCode": "PI6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI616", "StatusCodeDescriptionPayer": "PAYER INSTITUTION PURPOSE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER INSTITUTION PURPOSE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "PI7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI617", "StatusCodeDescriptionPayer": "PAYER INSTITUTION PURPOSE CODE MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 50", "StatusCodeDescriptionPayee": "PAYER INSTITUTION PURPOSE CODE MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 50", "DeclineType": "Technical"}, {"RawStatusCode": "PI8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI618", "StatusCodeDescriptionPayer": "PAYER INSTITUTION ORIGINATOR MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER INSTITUTION ORIGINATOR MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "PI9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI619", "StatusCodeDescriptionPayer": "PAYER INSTITUTION ORIGINATOR NAME MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 50", "StatusCodeDescriptionPayee": "PAYER INSTITUTION ORIGINATOR NAME MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 50", "DeclineType": "Technical"}, {"RawStatusCode": "PJ0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI620", "StatusCodeDescriptionPayer": "PAYER INSTITUTION PURPOSE NOTE MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 50", "StatusCodeDescriptionPayee": "PAYER INSTITUTION PURPOSE NOTE MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 50", "DeclineType": "Technical"}, {"RawStatusCode": "PJ1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI621", "StatusCodeDescriptionPayer": "PAYER INSTITUTION ORIGINATOR REFNO MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 35", "StatusCodeDescriptionPayee": "PAYER INSTITUTION ORIGINATOR REFNO MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "PJ2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI622", "StatusCodeDescriptionPayer": "PAYER INSTITUTION ORIGINATOR ADDRESS MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER INSTITUTION ORIGINATOR ADDRESS MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "PJ3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI623", "StatusCodeDescriptionPayer": "PAYER INSTITUTION OR<PERSON>INATOR ADDRESS LOCATION MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 40", "StatusCodeDescriptionPayee": "PAYER INSTITUTION OR<PERSON>INATOR ADDRESS LOCATION MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 40", "DeclineType": "Technical"}, {"RawStatusCode": "PJ4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI624", "StatusCodeDescriptionPayer": "PAYER INSTITUTION BENEFICIARY MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER INSTITUTION BENEFICIARY MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "PJ5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI625", "StatusCodeDescriptionPayer": "PAYER INSTITUTION BENEFICIARY NAME MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 50", "StatusCodeDescriptionPayee": "PAYER INSTITUTION BENEFICIARY NAME MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 50", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "PJ6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI626", "StatusCodeDescriptionPayer": "PAYER INSTITUTION ORIGINATOR TYPE MUST BE PRESENT, INDIVIDUAL|COMPANY", "StatusCodeDescriptionPayee": "PAYER INSTITUTION ORIGINATOR TYPE MUST BE PRESENT, INDIVIDUAL|COMPANY", "DeclineType": "Technical"}, {"RawStatusCode": "PJ7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI627", "StatusCodeDescriptionPayer": "PAYER INSTITUTION ORIGINATOR ADDRESS CITYMUST BE PRESENT,MINLENGTH 1 MAXLENGTH 100", "StatusCodeDescriptionPayee": "PAYER INSTITUTION ORIGINATOR ADDRESS CITYMUST BE PRESENT,MINLENGTH 1 MAXLENGTH 100", "DeclineType": "Technical"}, {"RawStatusCode": "PJ8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI628", "StatusCodeDescriptionPayer": "PAYER INSTITUTION OR<PERSON>INATOR ADDRESS COUNTRY MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 100", "StatusCodeDescriptionPayee": "PAYER INSTITUTION OR<PERSON>INATOR ADDRESS COUNTRY MUST BE PRESENT,MINLENGTH 1 MAXLENGTH 100", "DeclineType": "Technical"}, {"RawStatusCode": "PJ9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI629", "StatusCodeDescriptionPayer": "PAYER INSTITUTION ORIGINATOR ADDRESS GEOCODE MUST BE PRESENT, IN nn nnnn,nn nnnn FORMAT", "StatusCodeDescriptionPayee": "PAYER INSTITUTION ORIGINATOR ADDRESS GEOCODE MUST BE PRESENT, IN nn nnnn,nn nnnn FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "PK0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI630", "StatusCodeDescriptionPayer": "INSTITUTION TAG SHOULD NOT BE PRESENT IF INITIATIONMODE OTHER THAN 12", "StatusCodeDescriptionPayee": "INSTITUTION TAG SHOULD NOT BE PRESENT IF INITIATIONMODE OTHER THAN 12", "DeclineType": "Technical"}, {"RawStatusCode": "PM0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI631", "StatusCodeDescriptionPayer": "MERCHA<PERSON> TAG IS MANDATORY IF PAYER/PAYEE IS ENTITY", "StatusCodeDescriptionPayee": "MERCHA<PERSON> TAG IS MANDATORY IF PAYER/PAYEE IS ENTITY", "DeclineType": "Technical"}, {"RawStatusCode": "PM0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI632", "StatusCodeDescriptionPayer": "MERCHA<PERSON> TAG IS MANDATORY IF PAYER/PAYEE IS ENTITY", "StatusCodeDescriptionPayee": "MERCHA<PERSON> TAG IS MANDATORY IF PAYER/PAYEE IS ENTITY", "DeclineType": "Technical"}, {"RawStatusCode": "PM1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI633", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT IDENTIFIER SUBCODE MUST BE NUMERIC AND OF LENGTH 4", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT IDENTIFIER SUBCODE MUST BE NUMERIC AND OF LENGTH 4", "DeclineType": "Technical"}, {"RawStatusCode": "PM1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI634", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT IDENTIFIER SUBCODE MUST BE NUMERIC AND OF LENGTH 4", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT IDENTIFIER SUBCODE MUST BE NUMERIC AND OF LENGTH 4", "DeclineType": "Technical"}, {"RawStatusCode": "PM2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI635", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT IDENTIFIER MID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT IDENTIFIER MID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI636", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT IDENTIFIER MID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT IDENTIFIER MID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI637", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT IDENTIFIER SID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT IDENTIFIER SID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI638", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT IDENTIFIER SID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT IDENTIFIER SID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI639", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT IDENTIFIER TID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT IDENTIFIER TID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI640", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT IDENTIFIER TID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT IDENTIFIER TID MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI641", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT NAME BRAND MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT NAME BRAND MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI642", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT NAME BRAND MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT NAME BRAND MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI643", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT NAME LEGAL MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT NAME LEGAL MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI644", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT NAME LEGAL MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT NAME LEGAL MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI645", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT NAME FRANCHISE MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT NAME FRANCHISE MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI646", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT NAME FRANCHISE MUST BE OF MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT NAME FRANCHISE MUST BE OF MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "PM8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI647", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT OWNERSHIP MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT OWNERSHIP MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "PM8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI648", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT OWNERSHIP MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT OWNERSHIP MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "PM9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI649", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT OWNERSHIP TYPE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT OWNERSHIP TYPE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "PM9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI650", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT OWNERSHIP TYPE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT OWNERSHIP TYPE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "PN0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI651", "StatusCodeDescriptionPayer": "MERCHANT TAG SHOULD NOT BE PRESENT IF PAYER/PAYEE IS PERSON", "StatusCodeDescriptionPayee": "MERCHANT TAG SHOULD NOT BE PRESENT IF PAYER/PAYEE IS PERSON", "DeclineType": "Technical"}, {"RawStatusCode": "PN0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI652", "StatusCodeDescriptionPayer": "MERCHANT TAG SHOULD NOT BE PRESENT IF PAYER/PAYEE IS PERSON", "StatusCodeDescriptionPayee": "MERCHANT TAG SHOULD NOT BE PRESENT IF PAYER/PAYEE IS PERSON", "DeclineType": "Technical"}, {"RawStatusCode": "PN1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI653", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT IDENTIFIER MERCHANTTYPE MUST BE VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT IDENTIFIER MERCHANTTYPE MUST BE VALID", "DeclineType": "Technical"}, {"RawStatusCode": "PN1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI654", "StatusCodeDescriptionPayer": "PAYER/PAYEE MERCHANT IDENTIFIER MERCHANTTYPE MUST BE VALID", "StatusCodeDescriptionPayee": "PAYER/PAYEE MERCHANT IDENTIFIER MERCHANTTYPE MUST BE VALID", "DeclineType": "Technical"}, {"RawStatusCode": "PN2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI655", "StatusCodeDescriptionPayer": "PAYER MERCHANT TYPE MUST BE PRESENT AMONG SMALL|LARGE", "StatusCodeDescriptionPayee": "PAYER MERCHANT TYPE MUST BE PRESENT AMONG SMALL|LARGE", "DeclineType": "Technical"}, {"RawStatusCode": "PR0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI656", "StatusCodeDescriptionPayer": "PAYER DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PR1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI657", "StatusCodeDescriptionPayer": "PAYER ADDR DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER ADDR DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PR2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI658", "StatusCodeDescriptionPayer": "PAYER NAME DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER NAME DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PR3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI659", "StatusCodeDescriptionPayer": "PAYER CODE DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER CODE DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PR4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI660", "StatusCodeDescriptionPayer": "PAYER SEQNUM DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER SEQNUM DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PR5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI661", "StatusCodeDescriptionPayer": "PAYER TYPE DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER TYPE DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PT1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI662", "StatusCodeDescriptionPayer": "PTYPE TAG VALUE SHOULD BE UPIMANDATE", "StatusCodeDescriptionPayee": "PTYPE TAG VALUE SHOULD BE UPIMANDATE", "DeclineType": "Technical"}, {"RawStatusCode": "PY1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI663", "StatusCodeDescriptionPayer": "PAYEES NAME DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYEES NAME DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PY2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI664", "StatusCodeDescriptionPayer": "PAYEES ADDR DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYEES ADDR DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PY3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI665", "StatusCodeDescriptionPayer": "PAYEES SEQNUM DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYEES SEQNUM DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PY4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI666", "StatusCodeDescriptionPayer": "PAYEE CODE DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYEE CODE DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PY5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI667", "StatusCodeDescriptionPayer": "PAYEES TYPE DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYEES TYPE DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "Q01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI668", "StatusCodeDescriptionPayer": "PAYER/PAYEE DEVICE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE DEVICE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "Q02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI669", "StatusCodeDescriptionPayer": "PAYER/PAYEE DEVICE TAGS MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE DEVICE TAGS MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "Q03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI670", "StatusCodeDescriptionPayer": "PAYER/PAYEE TAG DEVICE NAME/VALUE MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE TAG DEVICE NAME/VALUE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "Q04 - Q12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI671", "StatusCodeDescriptionPayer": "SAME VALIDATION MESSAGE BASED ON DEVICE TYPE", "StatusCodeDescriptionPayee": "SAME VALIDATION MESSAGE BASED ON DEVICE TYPE", "DeclineType": "Technical"}, {"RawStatusCode": "R01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI672", "StatusCodeDescriptionPayer": "PAYER NOT PRESENT", "StatusCodeDescriptionPayee": "PAYER NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "R01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI673", "StatusCodeDescriptionPayer": "PAYER NOT PRESENT", "StatusCodeDescriptionPayee": "PAYER NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "R02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI674", "StatusCodeDescriptionPayer": "PAYER ADDR MUST BE VALID VPA MAXLENGTH 255", "StatusCodeDescriptionPayee": "PAYER ADDR MUST BE VALID VPA MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "R02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI675", "StatusCodeDescriptionPayer": "PAYER ADDR MUST BE VALID VPA MAXLENGTH 255", "StatusCodeDescriptionPayee": "PAYER ADDR MUST BE VALID VPA MAXLENGTH 255", "DeclineType": "Technical"}, {"RawStatusCode": "R03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI676", "StatusCodeDescriptionPayer": "PAYER NAME ALPHANUMERIC MINLEGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYER NAME ALPHANUMERIC MINLEGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "R03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI677", "StatusCodeDescriptionPayer": "PAYER NAME ALPHANUMERIC MINLEGTH 1 MAXLENGTH 99", "StatusCodeDescriptionPayee": "PAYER NAME ALPHANUMERIC MINLEGTH 1 MAXLENGTH 99", "DeclineType": "Technical"}, {"RawStatusCode": "R04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI678", "StatusCodeDescriptionPayer": "PAYER SEQNUM NUMERIC MINLEGTH 1 MAXLENGTH 3", "StatusCodeDescriptionPayee": "PAYER SEQNUM NUMERIC MINLEGTH 1 MAXLENGTH 3", "DeclineType": "Technical"}, {"RawStatusCode": "R04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI679", "StatusCodeDescriptionPayer": "PAYER SEQNUM NUMERIC MINLEGTH 1 MAXLENGTH 3", "StatusCodeDescriptionPayee": "PAYER SEQNUM NUMERIC MINLEGTH 1 MAXLENGTH 3", "DeclineType": "Technical"}, {"RawStatusCode": "R05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI680", "StatusCodeDescriptionPayer": "PAYER TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "PAYER TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "R05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI681", "StatusCodeDescriptionPayer": "PAYER TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "PAYER TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "R06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI682", "StatusCodeDescriptionPayer": "PAYER CODE NUMERIC OF LENGTH 4", "StatusCodeDescriptionPayee": "PAYER CODE NUMERIC OF LENGTH 4", "DeclineType": "Technical"}, {"RawStatusCode": "R06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI683", "StatusCodeDescriptionPayer": "PAYER CODE NUMERIC OF LENGTH 4", "StatusCodeDescriptionPayee": "PAYER CODE NUMERIC OF LENGTH 4", "DeclineType": "Technical"}, {"RawStatusCode": "R10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI684", "StatusCodeDescriptionPayer": "Payee redundant or duplicate payment instrument", "StatusCodeDescriptionPayee": "Payee redundant or duplicate payment instrument", "DeclineType": "Technical"}, {"RawStatusCode": "R13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI685", "StatusCodeDescriptionPayer": "TXN ORGTNXID MUST BE PRESENT/ VALID FOR REFUND; ALPHANUMERIC; MINLENGTH 1 , MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN ORGTNXID MUST BE PRESENT/ VALID FOR REFUND; ALPHANUMERIC; MINLENGTH 1 , MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "R14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI686", "StatusCodeDescriptionPayer": "TXN ORGTNXDATE MUST BE PRESENT/ VALID FOR REFUND", "StatusCodeDescriptionPayee": "TXN ORGTNXDATE MUST BE PRESENT/ VALID FOR REFUND", "DeclineType": "Technical"}, {"RawStatusCode": "R15", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI687", "StatusCodeDescriptionPayer": "TXN ORGRRN MUST BE PRESENT/ VALID FOR REFUND; NUMERIC; LENGTH 12", "StatusCodeDescriptionPayee": "TXN ORGRRN MUST BE PRESENT/ VALID FOR REFUND; NUMERIC; LENGTH 12", "DeclineType": "Technical"}, {"RawStatusCode": "R19", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI688", "StatusCodeDescriptionPayer": "INITIATOR PSP MUST SUPPORT VERSION 2.2 FOR REFUND", "StatusCodeDescriptionPayee": "INITIATOR PSP MUST SUPPORT VERSION 2.2 FOR REFUND", "DeclineType": "Technical"}, {"RawStatusCode": "R20", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI689", "StatusCodeDescriptionPayer": "BENEFICIARY BANK MUST SUPPORT VERSION 2.2 FOR REFUND", "StatusCodeDescriptionPayee": "BENEFICIARY BANK MUST SUPPORT VERSION 2.2 FOR REFUND", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "R21", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI690", "StatusCodeDescriptionPayer": "ONLINE REFUND IS DISABLED IN UPI", "StatusCodeDescriptionPayee": "ONLINE REFUND IS DISABLED IN UPI", "DeclineType": "Business"}, {"RawStatusCode": "R23", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI691", "StatusCodeDescriptionPayer": "PAYER AND PAYEE AMOUNT MUST BE SAME", "StatusCodeDescriptionPayee": "PAYER AND PAYEE AMOUNT MUST BE SAME", "DeclineType": "Technical"}, {"RawStatusCode": "R24", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI692", "StatusCodeDescriptionPayer": "PAYER ACCOUNT DETAILS MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER ACCOUNT DETAILS MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "R25", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI693", "StatusCodeDescriptionPayer": "MULT<PERSON><PERSON> PAYEES NOT ALLOWED FOR REFUND", "StatusCodeDescriptionPayee": "MULT<PERSON><PERSON> PAYEES NOT ALLOWED FOR REFUND", "DeclineType": "Technical"}, {"RawStatusCode": "RB", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI694", "StatusCodeDescriptionPayer": "CREDIT REVERSAL TIMEOUT(REVERSAL)", "StatusCodeDescriptionPayee": "CREDIT REVERSAL TIMEOUT(REVERSAL)", "DeclineType": "Technical"}, {"RawStatusCode": "RM1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI695", "StatusCodeDescriptionPayer": "REQMANDATE MUST BE PRESENT, UMN SHOULD BE ALWAYS CREATED BY PAYER (REQAUTHMANDATE)", "StatusCodeDescriptionPayee": "REQMANDATE MUST BE PRESENT, UMN SHOULD BE ALWAYS CREATED BY PAYER (REQAUTHMANDATE)", "DeclineType": "Technical"}, {"RawStatusCode": "RM11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI696", "StatusCodeDescriptionPayer": "B<PERSON><PERSON><PERSON><PERSON><PERSON>D SHOULD BE Y WHEN MANDATE IS CREATE/UPDATE (REQAUTHMANDATE)", "StatusCodeDescriptionPayee": "B<PERSON><PERSON><PERSON><PERSON><PERSON>D SHOULD BE Y WHEN MANDATE IS CREATE/UPDATE (REQAUTHMANDATE)", "DeclineType": "Technical"}, {"RawStatusCode": "RM2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI697", "StatusCodeDescriptionPayer": "UMN HANDLER AND ORG ID SHOULD BE SAME (REQAUTHMANDATE)", "StatusCodeDescriptionPayee": "UMN HANDLER AND ORG ID SHOULD BE SAME (REQAUTHMANDATE)", "DeclineType": "Technical"}, {"RawStatusCode": "RM3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI698", "StatusCodeDescriptionPayer": "TXNID AND MANDATE TXNID SHOULD BE SAME (REQAUTHMANDATE)", "StatusCodeDescriptionPayee": "TXNID AND MANDATE TXNID SHOULD BE SAME (REQAUTHMANDATE)", "DeclineType": "Technical"}, {"RawStatusCode": "RM5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI699", "StatusCodeDescriptionPayer": "PAYEE INITIATED MANDATE SHOULD NOT HAVE SHARETOPAYEE TAG (REQAUTHMANDATE)", "StatusCodeDescriptionPayee": "PAYEE INITIATED MANDATE SHOULD NOT HAVE SHARETOPAYEE TAG (REQAUTHMANDATE)", "DeclineType": "Technical"}, {"RawStatusCode": "RM7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI700", "StatusCodeDescriptionPayer": "VALIDITY START DATE SHOULD BE GREATER THAN OR EQUAL TO THE CURRENT DATE (REQAUTHMANDATE)", "StatusCodeDescriptionPayee": "VALIDITY START DATE SHOULD BE GREATER THAN OR EQUAL TO THE CURRENT DATE (REQAUTHMANDATE)", "DeclineType": "Technical"}, {"RawStatusCode": "RM8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI701", "StatusCodeDescriptionPayer": "VALIDITY END DATE SHOULD BE GREATER THAN OR EQUAL TO THE CURRENT DATE (REQAUTHMANDATE)", "StatusCodeDescriptionPayee": "VALIDITY END DATE SHOULD BE GREATER THAN OR EQUAL TO THE CURRENT DATE (REQAUTHMANDATE)", "DeclineType": "Technical"}, {"RawStatusCode": "RM9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI702", "StatusCodeDescriptionPayer": "SH<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SHOULD BE ALLOWED ONLY FOR PAYER AND RECURRENCE PATTERN SHOULD BE ONETIME FOR BOTH PAYER/PAYEE (REQAUTHMANDATE)", "StatusCodeDescriptionPayee": "SH<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SHOULD BE ALLOWED ONLY FOR PAYER AND RECURRENCE PATTERN SHOULD BE ONETIME FOR BOTH PAYER/PAYEE (REQAUTHMANDATE)", "DeclineType": "Business"}, {"RawStatusCode": "RP", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI703", "StatusCodeDescriptionPayer": "PARTIAL DEBIT REVERSAL TIMEOUT", "StatusCodeDescriptionPayee": "PARTIAL DEBIT REVERSAL TIMEOUT", "DeclineType": "Technical"}, {"RawStatusCode": "RR", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI704", "StatusCodeDescriptionPayer": "DEBIT REVERSAL TIMEOUT(REVERSAL)", "StatusCodeDescriptionPayee": "DEBIT REVERSAL TIMEOUT(REVERSAL)", "DeclineType": "Technical"}, {"RawStatusCode": "S01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI705", "StatusCodeDescriptionPayer": "TXN RISKSCORE PROVIDER MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "TXN RISKSCORE PROVIDER MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "S02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI706", "StatusCodeDescriptionPayer": "TXN RISKSCORE TYPE MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "TXN RISKSCORE TYPE MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "S03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI707", "StatusCodeDescriptionPayer": "TXN RISKSCORE VALUE MUST BE PRESENT NUMERIC; MINLENGTH 1 MAXLENGTH 5", "StatusCodeDescriptionPayee": "TXN RISKSCORE VALUE MUST BE PRESENT NUMERIC; MINLENGTH 1 MAXLENGTH 5", "DeclineType": "Technical"}, {"RawStatusCode": "T01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI708", "StatusCodeDescriptionPayer": "TXN NOT PRESENT", "StatusCodeDescriptionPayee": "TXN NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "T01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI709", "StatusCodeDescriptionPayer": "TXN NOT PRESENT", "StatusCodeDescriptionPayee": "TXN NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "T01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI710", "StatusCodeDescriptionPayer": "TXN NOT PRESENT", "StatusCodeDescriptionPayee": "TXN NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "T02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI711", "StatusCodeDescriptionPayer": "TXN ID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN ID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "T02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI712", "StatusCodeDescriptionPayer": "TXN ID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN ID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "T02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI713", "StatusCodeDescriptionPayer": "TXN ID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN ID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "T03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI714", "StatusCodeDescriptionPayer": "TXN NOTE ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 50", "StatusCodeDescriptionPayee": "TXN NOTE ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 50", "DeclineType": "Technical"}, {"RawStatusCode": "T03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI715", "StatusCodeDescriptionPayer": "TXN NOTE ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 50", "StatusCodeDescriptionPayee": "TXN NOTE ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 50", "DeclineType": "Technical"}, {"RawStatusCode": "T03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI716", "StatusCodeDescriptionPayer": "TXN NOTE ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 50", "StatusCodeDescriptionPayee": "TXN NOTE ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 50", "DeclineType": "Technical"}, {"RawStatusCode": "T04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI717", "StatusCodeDescriptionPayer": "TXN REFID ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN REFID ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "T04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI718", "StatusCodeDescriptionPayer": "TXN REFID ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN REFID ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "T04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI719", "StatusCodeDescriptionPayer": "TXN REFID ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN REFID ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "T05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI720", "StatusCodeDescriptionPayer": "TXN REFURL IS URL; MINLENGTH 1 MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN REFURL IS URL; MINLENGTH 1 MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "T06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI721", "StatusCodeDescriptionPayer": "TXN TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TXN TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "T06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI722", "StatusCodeDescriptionPayer": "TXN TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TXN TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "T06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI723", "StatusCodeDescriptionPayer": "TXN TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TXN TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "T07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI724", "StatusCodeDescriptionPayer": "TXN TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "TXN TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "T07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI725", "StatusCodeDescriptionPayer": "TXN TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "TXN TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "T07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI726", "StatusCodeDescriptionPayer": "TXN TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "TXN TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "T08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI727", "StatusCodeDescriptionPayer": "TXN ORGTNXID MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN ORGTNXID MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "T09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI728", "StatusCodeDescriptionPayer": "TXN ORGTNXID IS ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN ORGTNXID IS ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "T10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI729", "StatusCodeDescriptionPayer": "TXN ORGTNXID IS NOT APPLICABLE FOR THIS TYPE OF TRANSACTION", "StatusCodeDescriptionPayee": "TXN ORGTNXID IS NOT APPLICABLE FOR THIS TYPE OF TRANSACTION", "DeclineType": "Technical"}, {"RawStatusCode": "T11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI730", "StatusCodeDescriptionPayer": "TXN ORGRESPCD MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 3", "StatusCodeDescriptionPayee": "TXN ORGRESPCD MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 3", "DeclineType": "Technical"}, {"RawStatusCode": "T12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI731", "StatusCodeDescriptionPayer": "TXN CUSTREF MUST BE PRESENT; LENGTH 12", "StatusCodeDescriptionPayee": "TXN CUSTREF MUST BE PRESENT; LENGTH 12", "DeclineType": "Technical"}, {"RawStatusCode": "T12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI732", "StatusCodeDescriptionPayer": "TXN CUSTREF MUST BE PRESENT; LENGTH 12", "StatusCodeDescriptionPayee": "TXN CUSTREF MUST BE PRESENT; LENGTH 12", "DeclineType": "Technical"}, {"RawStatusCode": "T13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI733", "StatusCodeDescriptionPayer": "TXN SUBTYPE MUST BE PRESENT", "StatusCodeDescriptionPayee": "TXN SUBTYPE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "T13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI734", "StatusCodeDescriptionPayer": "TXN SUBTYPE MUST BE PRESENT", "StatusCodeDescriptionPayee": "TXN SUBTYPE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "T13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI735", "StatusCodeDescriptionPayer": "TXN SUBTYPE MUST BE PRESENT", "StatusCodeDescriptionPayee": "TXN SUBTYPE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "T14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI736", "StatusCodeDescriptionPayer": "PURPOSE SHOULD BE PRESENT VALID VALUE", "StatusCodeDescriptionPayee": "PURPOSE SHOULD BE PRESENT VALID VALUE", "DeclineType": "Technical"}, {"RawStatusCode": "T14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI737", "StatusCodeDescriptionPayer": "PURPOSE SHOULD BE PRESENT VALID VALUE", "StatusCodeDescriptionPayee": "PURPOSE SHOULD BE PRESENT VALID VALUE", "DeclineType": "Technical"}, {"RawStatusCode": "T14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI738", "StatusCodeDescriptionPayer": "PURPOSE SHOULD BE PRESENT VALID VALUE", "StatusCodeDescriptionPayee": "PURPOSE SHOULD BE PRESENT VALID VALUE", "DeclineType": "Technical"}, {"RawStatusCode": "T15", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI739", "StatusCodeDescriptionPayer": "TXN PURPOSE SHOULD BE 00 WHEN INITIATIONMODE 12", "StatusCodeDescriptionPayee": "TXN PURPOSE SHOULD BE 00 WHEN INITIATIONMODE 12", "DeclineType": "Technical"}, {"RawStatusCode": "T16", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI740", "StatusCodeDescriptionPayer": "IF SUBTYPE = MANDAT<PERSON>, THEN UMN IS MANDATORY", "StatusCodeDescriptionPayee": "IF SUBTYPE = MANDAT<PERSON>, THEN UMN IS MANDATORY", "DeclineType": "Technical"}, {"RawStatusCode": "T20", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI741", "StatusCodeDescriptionPayer": "PAYER INSTITUTION SHOULD NOT BE PRESENT", "StatusCodeDescriptionPayee": "PAYER INSTITUTION SHOULD NOT BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "TR1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI742", "StatusCodeDescriptionPayer": "TXN RISKSCORE PROVIDER MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "TXN RISKSCORE PROVIDER MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "TR1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI743", "StatusCodeDescriptionPayer": "TXN RISKSCORE PROVIDER MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "TXN RISKSCORE PROVIDER MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "TR2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI744", "StatusCodeDescriptionPayer": "TXN RISKSCORE TYPE MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "TXN RISKSCORE TYPE MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "TR2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI745", "StatusCodeDescriptionPayer": "TXN RISKSCORE TYPE MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "StatusCodeDescriptionPayee": "TXN RISKSCORE TYPE MUST BE PRESENT ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 20", "DeclineType": "Technical"}, {"RawStatusCode": "TR3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI746", "StatusCodeDescriptionPayer": "TXN RISKSCORE VALUE MUST BE PRESENT NUMERIC; MINLENGTH 1 MAXLENGTH 5", "StatusCodeDescriptionPayee": "TXN RISKSCORE VALUE MUST BE PRESENT NUMERIC; MINLENGTH 1 MAXLENGTH 5", "DeclineType": "Technical"}, {"RawStatusCode": "TR3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI747", "StatusCodeDescriptionPayer": "TXN RISKSCORE VALUE MUST BE PRESENT NUMERIC; MINLENGTH 1 MAXLENGTH 5", "StatusCodeDescriptionPayee": "TXN RISKSCORE VALUE MUST BE PRESENT NUMERIC; MINLENGTH 1 MAXLENGTH 5", "DeclineType": "Technical"}, {"RawStatusCode": "TT4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI748", "StatusCodeDescriptionPayer": "VERSION 2.0 LIST_PSP_KEYS TAGS NOT SUPPORTED IN 1.0", "StatusCodeDescriptionPayee": "VERSION 2.0 LIST_PSP_KEYS TAGS NOT SUPPORTED IN 1.0", "DeclineType": "Technical"}, {"RawStatusCode": "U01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI749", "StatusCodeDescriptionPayer": "THE REQUEST IS DUPLICATE", "StatusCodeDescriptionPayee": "THE REQUEST IS DUPLICATE", "DeclineType": "Technical"}, {"RawStatusCode": "U02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI750", "StatusCodeDescriptionPayer": "AMOUNT CAP IS EXCEEDED", "StatusCodeDescriptionPayee": "AMOUNT CAP IS EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "U03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI751", "StatusCodeDescriptionPayer": "NET DEBIT CAP IS EXCEEDED", "StatusCodeDescriptionPayee": "NET DEBIT CAP IS EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "U04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI752", "StatusCodeDescriptionPayer": "REQUEST IS NOT FOUND", "StatusCodeDescriptionPayee": "REQUEST IS NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI753", "StatusCodeDescriptionPayer": "FORMATION IS NOT PROPER", "StatusCodeDescriptionPayee": "FORMATION IS NOT PROPER", "DeclineType": "Technical"}, {"RawStatusCode": "U06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI754", "StatusCodeDescriptionPayer": "TRANSACTION ID IS MISMATCHED", "StatusCodeDescriptionPayee": "TRANSACTION ID IS MISMATCHED", "DeclineType": "Technical"}, {"RawStatusCode": "U07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI755", "StatusCodeDescriptionPayer": "VALIDATION ERROR", "StatusCodeDescriptionPayee": "VALIDATION ERROR", "DeclineType": "Technical"}, {"RawStatusCode": "U08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI756", "StatusCodeDescriptionPayer": "SYSTEM EXCEPTION", "StatusCodeDescriptionPayee": "SYSTEM EXCEPTION", "DeclineType": "Technical"}, {"RawStatusCode": "U09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI757", "StatusCodeDescriptionPayer": "REQAUTH TIME OUT FOR PAY", "StatusCodeDescriptionPayee": "REQAUTH TIME OUT FOR PAY", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI758", "StatusCodeDescriptionPayer": "ILLEGAL OPERATION", "StatusCodeDescriptionPayee": "ILLEGAL OPERATION", "DeclineType": "Technical"}, {"RawStatusCode": "U11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI759", "StatusCodeDescriptionPayer": "CREDENTIALS IS NOT PRESENT", "StatusCodeDescriptionPayee": "CREDENTIALS IS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "U12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI760", "StatusCodeDescriptionPayer": "AMOUNT OR CURRENCY MISMATCH", "StatusCodeDescriptionPayee": "AMOUNT OR CURRENCY MISMATCH", "DeclineType": "Technical"}, {"RawStatusCode": "U13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI761", "StatusCodeDescriptionPayer": "EXTERNAL ERROR", "StatusCodeDescriptionPayee": "EXTERNAL ERROR", "DeclineType": "Technical"}, {"RawStatusCode": "U14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI762", "StatusCodeDescriptionPayer": "ENCRYPTION ERROR", "StatusCodeDescriptionPayee": "ENCRYPTION ERROR", "DeclineType": "Technical"}, {"RawStatusCode": "U15", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI763", "StatusCodeDescriptionPayer": "CHECKSUM FAILED", "StatusCodeDescriptionPayee": "CHECKSUM FAILED", "DeclineType": "Technical"}, {"RawStatusCode": "U17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI764", "StatusCodeDescriptionPayer": "PSP IS NOT REGISTERED", "StatusCodeDescriptionPayee": "PSP IS NOT REGISTERED", "DeclineType": "Technical"}, {"RawStatusCode": "U17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI765", "StatusCodeDescriptionPayer": "PSP IS NOT REGISTERED", "StatusCodeDescriptionPayee": "PSP IS NOT REGISTERED", "DeclineType": "Technical"}, {"RawStatusCode": "U17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI766", "StatusCodeDescriptionPayer": "PSP IS NOT REGISTERED", "StatusCodeDescriptionPayee": "PSP IS NOT REGISTERED", "DeclineType": "Technical"}, {"RawStatusCode": "U17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI767", "StatusCodeDescriptionPayer": "PSP IS NOT REGISTERED", "StatusCodeDescriptionPayee": "PSP IS NOT REGISTERED", "DeclineType": "Technical"}, {"RawStatusCode": "U17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI768", "StatusCodeDescriptionPayer": "PSP IS NOT REGISTERED", "StatusCodeDescriptionPayee": "PSP IS NOT REGISTERED", "DeclineType": "Technical"}, {"RawStatusCode": "U17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI769", "StatusCodeDescriptionPayer": "PSP IS NOT REGISTERED", "StatusCodeDescriptionPayee": "PSP IS NOT REGISTERED", "DeclineType": "Technical"}, {"RawStatusCode": "U17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI770", "StatusCodeDescriptionPayer": "PSP IS NOT REGISTERED", "StatusCodeDescriptionPayee": "PSP IS NOT REGISTERED", "DeclineType": "Technical"}, {"RawStatusCode": "U17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI771", "StatusCodeDescriptionPayer": "PSP IS NOT REGISTERED", "StatusCodeDescriptionPayee": "PSP IS NOT REGISTERED", "DeclineType": "Technical"}, {"RawStatusCode": "U18", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI772", "StatusCodeDescriptionPayer": "REQUEST AUTHORISATION ACK<PERSON>OWLEDGEMENT IS NOT RECEIVED", "StatusCodeDescriptionPayee": "REQUEST AUTHORISATION ACK<PERSON>OWLEDGEMENT IS NOT RECEIVED", "DeclineType": "Technical"}, {"RawStatusCode": "U19", "CustomerStatusCode": "ZA", "CustomerReversalCode": "*", "StatusCode": "UPI1207", "StatusCodeDescriptionPayer": "TRANSACTION DECLINED BY CUSTOMER", "StatusCodeDescriptionPayee": "TRANSACTION DECLINED BY CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "U19", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI773", "StatusCodeDescriptionPayer": "REQUEST AUTHORISATION IS DECLINED", "StatusCodeDescriptionPayee": "REQUEST AUTHORISATION IS DECLINED", "DeclineType": "Technical"}, {"RawStatusCode": "U20", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI774", "StatusCodeDescriptionPayer": "REQUEST AUTHORISATION TIMEOUT", "StatusCodeDescriptionPayee": "REQUEST AUTHORISATION TIMEOUT", "DeclineType": "Technical"}, {"RawStatusCode": "U21", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI775", "StatusCodeDescriptionPayer": "REQUEST AUTHORISATION IS NOT FOUND", "StatusCodeDescriptionPayee": "REQUEST AUTHORISATION IS NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U22", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI776", "StatusCodeDescriptionPayer": "CM REQUEST IS DECLINED", "StatusCodeDescriptionPayee": "CM REQUEST IS DECLINED", "DeclineType": "Technical"}, {"RawStatusCode": "U23", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI777", "StatusCodeDescriptionPayer": "CM REQUEST TIMEOUT", "StatusCodeDescriptionPayee": "CM REQUEST TIMEOUT", "DeclineType": "Technical"}, {"RawStatusCode": "U24", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI778", "StatusCodeDescriptionPayer": "CM REQUEST ACK<PERSON>OWLEDGEMENT IS NOT RECEIVED", "StatusCodeDescriptionPayee": "CM REQUEST ACK<PERSON>OWLEDGEMENT IS NOT RECEIVED", "DeclineType": "Technical"}, {"RawStatusCode": "U25", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI779", "StatusCodeDescriptionPayer": "CM URL IS NOT FOUND", "StatusCodeDescriptionPayee": "CM URL IS NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U26", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI780", "StatusCodeDescriptionPayer": "PSP REQUEST CREDIT PAY AC<PERSON><PERSON><PERSON>LEDGEMENT IS NOT RECEIVED", "StatusCodeDescriptionPayee": "PSP REQUEST CREDIT PAY AC<PERSON><PERSON><PERSON>LEDGEMENT IS NOT RECEIVED", "DeclineType": "Technical"}, {"RawStatusCode": "U27", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI781", "StatusCodeDescriptionPayer": "NO RESPONSE FROM PSP", "StatusCodeDescriptionPayee": "NO RESPONSE FROM PSP", "DeclineType": "Technical"}, {"RawStatusCode": "U28", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI782", "StatusCodeDescriptionPayer": "Remitter bank not available", "StatusCodeDescriptionPayee": "Remitter bank not available", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U29", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI783", "StatusCodeDescriptionPayer": "ADDRESS RESOLUTION IS FAILED", "StatusCodeDescriptionPayee": "ADDRESS RESOLUTION IS FAILED", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U31", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI785", "StatusCodeDescriptionPayer": "CREDIT HAS BEEN FAILED", "StatusCodeDescriptionPayee": "CREDIT HAS BEEN FAILED", "DeclineType": "Business"}, {"RawStatusCode": "U32", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI786", "StatusCodeDescriptionPayer": "CREDIT REVERT HAS BEEN FAILED", "StatusCodeDescriptionPayee": "CREDIT REVERT HAS BEEN FAILED", "DeclineType": "Technical"}, {"RawStatusCode": "U33", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI787", "StatusCodeDescriptionPayer": "DEBIT REVERT HAS BEEN FAILED", "StatusCodeDescriptionPayee": "DEBIT REVERT HAS BEEN FAILED", "DeclineType": "Technical"}, {"RawStatusCode": "U34", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI788", "StatusCodeDescriptionPayer": "The debited amount has been reversed", "StatusCodeDescriptionPayee": "The debited amount has been reversed", "DeclineType": "Business"}, {"RawStatusCode": "U35", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI789", "StatusCodeDescriptionPayer": "RESPONSE IS ALREADY BEEN RECEIVED", "StatusCodeDescriptionPayee": "RESPONSE IS ALREADY BEEN RECEIVED", "DeclineType": "Technical"}, {"RawStatusCode": "U36", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI790", "StatusCodeDescriptionPayer": "REQUEST IS ALREADY BEEN SENT", "StatusCodeDescriptionPayee": "REQUEST IS ALREADY BEEN SENT", "DeclineType": "Technical"}, {"RawStatusCode": "U37", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI791", "StatusCodeDescriptionPayer": "REVERSAL HAS BEEN SENT", "StatusCodeDescriptionPayee": "REVERSAL HAS BEEN SENT", "DeclineType": "Technical"}, {"RawStatusCode": "U38", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI792", "StatusCodeDescriptionPayer": "RESPONSE IS ALREADY BEEN SENT", "StatusCodeDescriptionPayee": "RESPONSE IS ALREADY BEEN SENT", "DeclineType": "Technical"}, {"RawStatusCode": "U39", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI793", "StatusCodeDescriptionPayer": "TRANSACTION IS ALREADY BEEN FAILED", "StatusCodeDescriptionPayee": "TRANSACTION IS ALREADY BEEN FAILED", "DeclineType": "Technical"}, {"RawStatusCode": "U40", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI794", "StatusCodeDescriptionPayer": "IMPS PROCESSING FAILED IN UPI", "StatusCodeDescriptionPayee": "IMPS PROCESSING FAILED IN UPI", "DeclineType": "Technical"}, {"RawStatusCode": "U41", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI795", "StatusCodeDescriptionPayer": "IMPS IS SIGNED OFF", "StatusCodeDescriptionPayee": "IMPS IS SIGNED OFF", "DeclineType": "Technical"}, {"RawStatusCode": "U42", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI796", "StatusCodeDescriptionPayer": "IMPS TRANSACTION IS ALREADY BEEN PROCESSED", "StatusCodeDescriptionPayee": "IMPS TRANSACTION IS ALREADY BEEN PROCESSED", "DeclineType": "Technical"}, {"RawStatusCode": "U43", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI797", "StatusCodeDescriptionPayer": "IMPS IS DECLINED", "StatusCodeDescriptionPayee": "IMPS IS DECLINED", "DeclineType": "Technical"}, {"RawStatusCode": "U44", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI798", "StatusCodeDescriptionPayer": "FORM HAS BEEN SIGNED OFF", "StatusCodeDescriptionPayee": "FORM HAS BEEN SIGNED OFF", "DeclineType": "Technical"}, {"RawStatusCode": "U45", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI799", "StatusCodeDescriptionPayer": "FORM PROCESSING HAS BEEN FAILED IN UPI", "StatusCodeDescriptionPayee": "FORM PROCESSING HAS BEEN FAILED IN UPI", "DeclineType": "Technical"}, {"RawStatusCode": "U46", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI800", "StatusCodeDescriptionPayer": "REQUEST CREDIT IS NOT FOUND", "StatusCodeDescriptionPayee": "REQUEST CREDIT IS NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U47", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI801", "StatusCodeDescriptionPayer": "REQUEST DEBIT IS NOT FOUND", "StatusCodeDescriptionPayee": "REQUEST DEBIT IS NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U48", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI802", "StatusCodeDescriptionPayer": "TRANSACTION ID IS NOT PRESENT", "StatusCodeDescriptionPayee": "TRANSACTION ID IS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "U49", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI803", "StatusCodeDescriptionPayer": "REQUEST MESSAGE ID IS NOT PRESENT", "StatusCodeDescriptionPayee": "REQUEST MESSAGE ID IS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "U50", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI804", "StatusCodeDescriptionPayer": "IFSC IS NOT PRESENT", "StatusCodeDescriptionPayee": "IFSC IS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "U51", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI805", "StatusCodeDescriptionPayer": "REQUEST REFUND IS NOT FOUND", "StatusCodeDescriptionPayee": "REQUEST REFUND IS NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U52", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI806", "StatusCodeDescriptionPayer": "PSP ORGID NOT FOUND", "StatusCodeDescriptionPayee": "PSP ORGID NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U52", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI807", "StatusCodeDescriptionPayer": "PSP ORGID NOT FOUND", "StatusCodeDescriptionPayee": "PSP ORGID NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U52", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI808", "StatusCodeDescriptionPayer": "PSP ORGID NOT FOUND", "StatusCodeDescriptionPayee": "PSP ORGID NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U52", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI809", "StatusCodeDescriptionPayer": "PSP ORGID NOT FOUND", "StatusCodeDescriptionPayee": "PSP ORGID NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U52", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI810", "StatusCodeDescriptionPayer": "PSP ORGID NOT FOUND", "StatusCodeDescriptionPayee": "PSP ORGID NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U52", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI811", "StatusCodeDescriptionPayer": "PSP ORGID NOT FOUND", "StatusCodeDescriptionPayee": "PSP ORGID NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U52", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI812", "StatusCodeDescriptionPayer": "PSP ORGID NOT FOUND", "StatusCodeDescriptionPayee": "PSP ORGID NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U52", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI813", "StatusCodeDescriptionPayer": "PSP ORGID NOT FOUND", "StatusCodeDescriptionPayee": "PSP ORGID NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "U53", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI814", "StatusCodeDescriptionPayer": "PSP REQUEST PAY DEBIT AC<PERSON><PERSON><PERSON>LEDGEMENT NOT RECEIVED", "StatusCodeDescriptionPayee": "PSP REQUEST PAY DEBIT AC<PERSON><PERSON><PERSON>LEDGEMENT NOT RECEIVED", "DeclineType": "Technical"}, {"RawStatusCode": "U54", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI815", "StatusCodeDescriptionPayer": "TRANSACTION ID OR AMOUNT IN CREDENTIAL BLOCK DOES NOT MATCH WITH THAT IN REQPAY", "StatusCodeDescriptionPayee": "TRANSACTION ID OR AMOUNT IN CREDENTIAL BLOCK DOES NOT MATCH WITH THAT IN REQPAY", "DeclineType": "Technical"}, {"RawStatusCode": "U55", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI816", "StatusCodeDescriptionPayer": "MESSAGE INTEGRITY FAILED DUE TO ORGID MISMATCH", "StatusCodeDescriptionPayee": "MESSAGE INTEGRITY FAILED DUE TO ORGID MISMATCH", "DeclineType": "Technical"}, {"RawStatusCode": "U56", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI817", "StatusCodeDescriptionPayer": "NUMBER OF PAYEES DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "NUMBER OF PAYEES DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "U57", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI818", "StatusCodeDescriptionPayer": "PAYEE AMOUNT DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYEE AMOUNT DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "U58", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI819", "StatusCodeDescriptionPayer": "PAYER AMOUNT DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER AMOUNT DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "U59", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI820", "StatusCodeDescriptionPayer": "PAYEE ADDRESS DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYEE ADDRESS DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "U60", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI821", "StatusCodeDescriptionPayer": "PAYER ADDRESS DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER ADDRESS DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "U61", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI822", "StatusCodeDescriptionPayer": "PAYEE INFO DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYEE INFO DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "U62", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI823", "StatusCodeDescriptionPayer": "PAYER INFO DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER INFO DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "U63", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI824", "StatusCodeDescriptionPayer": "DEVICE REGISTRATION FAILED IN UPI", "StatusCodeDescriptionPayee": "DEVICE REGISTRATION FAILED IN UPI", "DeclineType": "Technical"}, {"RawStatusCode": "U67", "CustomerStatusCode": "UT", "CustomerReversalCode": "*", "StatusCode": "UPI1204", "StatusCodeDescriptionPayer": "REMITTER/ISSUER UNAVAILABLE (TIMEOUT)", "StatusCodeDescriptionPayee": "REMITTER/ISSUER UNAVAILABLE (TIMEOUT)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U67", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI825", "StatusCodeDescriptionPayer": "DEBIT TIMEOUT", "StatusCodeDescriptionPayee": "DEBIT TIMEOUT", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U68", "CustomerStatusCode": "BT", "CustomerReversalCode": "*", "StatusCode": "UPI1280", "StatusCodeDescriptionPayer": "ACQUIRER/BENEFICIARY UNAVAILABLE(TIMEOUT)", "StatusCodeDescriptionPayee": "ACQUIRER/BENEFICIARY UNAVAILABLE(TIMEOUT)", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "U68", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI826", "StatusCodeDescriptionPayer": "CREDIT TIMEOUT", "StatusCodeDescriptionPayee": "CREDIT TIMEOUT", "DeclineType": "Technical"}, {"RawStatusCode": "U69", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI827", "StatusCodeDescriptionPayer": "COLLECT EXPIRED", "StatusCodeDescriptionPayee": "COLLECT EXPIRED", "DeclineType": "Business"}, {"RawStatusCode": "U70", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI828", "StatusCodeDescriptionPayer": "RECEIVED LATE RESPONSE", "StatusCodeDescriptionPayee": "RECEIVED LATE RESPONSE", "DeclineType": "Technical"}, {"RawStatusCode": "U74", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI829", "StatusCodeDescriptionPayer": "PAYER ACCOUNT MISMATCH", "StatusCodeDescriptionPayee": "PAYER ACCOUNT MISMATCH", "DeclineType": "Technical"}, {"RawStatusCode": "U75", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI830", "StatusCodeDescriptionPayer": "PAYEE ACCOUNT MISMATCH", "StatusCodeDescriptionPayee": "PAYEE ACCOUNT MISMATCH", "DeclineType": "Technical"}, {"RawStatusCode": "U78", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI831", "StatusCodeDescriptionPayer": "Beneficiary bank seems to be offline", "StatusCodeDescriptionPayee": "Beneficiary bank seems to be offline", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "UA2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI832", "StatusCodeDescriptionPayer": "VERSION/TAGS SENT NOT SUPPORTED BY PSP/BANK", "StatusCodeDescriptionPayee": "VERSION/TAGS SENT NOT SUPPORTED BY PSP/BANK", "DeclineType": "Technical"}, {"RawStatusCode": "UB7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI833", "StatusCodeDescriptionPayer": "OTHER BANK/PSP IS NOT SUPPORTED IN 2.0 VERSION", "StatusCodeDescriptionPayee": "OTHER BANK/PSP IS NOT SUPPORTED IN 2.0 VERSION", "DeclineType": "Technical"}, {"RawStatusCode": "UB7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI834", "StatusCodeDescriptionPayer": "Beneficiary Bank/PSP not supported in UPI 2 0", "StatusCodeDescriptionPayee": "Beneficiary Bank/PSP not supported in UPI 2 0", "DeclineType": "Technical"}, {"RawStatusCode": "UH1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI835", "StatusCodeDescriptionPayer": "HEADER VERSION MISMATCHED", "StatusCodeDescriptionPayee": "HEADER VERSION MISMATCHED", "DeclineType": "Technical"}, {"RawStatusCode": "UN5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI836", "StatusCodeDescriptionPayer": "PAYER PSP NOT AVAILABLE", "StatusCodeDescriptionPayee": "PAYER PSP NOT AVAILABLE", "DeclineType": "Technical"}, {"RawStatusCode": "UN6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI837", "StatusCodeDescriptionPayer": "Benfeciary PSP app is not available", "StatusCodeDescriptionPayee": "Benfeciary PSP app is not available", "DeclineType": "Technical"}, {"RawStatusCode": "UN7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI838", "StatusCodeDescriptionPayer": "Remitter bank is not available", "StatusCodeDescriptionPayee": "Remitter bank is not available", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UN8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI839", "StatusCodeDescriptionPayer": "PAYER PSP NOT REGISTERED", "StatusCodeDescriptionPayee": "PAYER PSP NOT REGISTERED", "DeclineType": "Technical"}, {"RawStatusCode": "UN9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI840", "StatusCodeDescriptionPayer": "Benfeciary PSP is not registered", "StatusCodeDescriptionPayee": "Benfeciary PSP is not registered", "DeclineType": "Technical"}, {"RawStatusCode": "UP1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI841", "StatusCodeDescriptionPayer": "NOT A VALID AMOUNT FOR THIS CATEGORY", "StatusCodeDescriptionPayee": "NOT A VALID AMOUNT FOR THIS CATEGORY", "DeclineType": "Technical"}, {"RawStatusCode": "UT", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI842", "StatusCodeDescriptionPayer": "Remitter bank seems to be unavailable", "StatusCodeDescriptionPayee": "Remitter bank seems to be unavailable", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UV2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI843", "StatusCodeDescriptionPayer": "REQRESP VERSION MISMATCHED", "StatusCodeDescriptionPayee": "REQRESP VERSION MISMATCHED", "DeclineType": "Technical"}, {"RawStatusCode": "V01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI844", "StatusCodeDescriptionPayer": "PAYER/PAYEE AMOUNT MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE AMOUNT MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "V01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI845", "StatusCodeDescriptionPayer": "PAYER/PAYEE AMOUNT MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE AMOUNT MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "V02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI846", "StatusCodeDescriptionPayer": "PAYER/PAYEE AMOUNT CUR MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE AMOUNT CUR MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "V02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI847", "StatusCodeDescriptionPayer": "PAYER/PAYEE AMOUNT CUR MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE AMOUNT CUR MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "V03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI848", "StatusCodeDescriptionPayer": "PAYER/PAYEE AMOUNT VALUE MUST BE WITHIN 18 DIGITS INCLUDING 2 DECIMAL", "StatusCodeDescriptionPayee": "PAYER/PAYEE AMOUNT VALUE MUST BE WITHIN 18 DIGITS INCLUDING 2 DECIMAL", "DeclineType": "Technical"}, {"RawStatusCode": "V03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI849", "StatusCodeDescriptionPayer": "PAYER/PAYEE AMOUNT VALUE MUST BE WITHIN 18 DIGITS INCLUDING 2 DECIMAL", "StatusCodeDescriptionPayee": "PAYER/PAYEE AMOUNT VALUE MUST BE WITHIN 18 DIGITS INCLUDING 2 DECIMAL", "DeclineType": "Technical"}, {"RawStatusCode": "V04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI850", "StatusCodeDescriptionPayer": "PAYER/PAYEE AMOUNT SPLIT NAME MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE AMOUNT SPLIT NAME MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "V04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI851", "StatusCodeDescriptionPayer": "PAYER/PAYEE AMOUNT SPLIT NAME MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE AMOUNT SPLIT NAME MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "V05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI852", "StatusCodeDescriptionPayer": "PAYER/PAYEE AMOUNT SPLIT VALUE MUST BE PRESENT MINLENGTH 1, MAXLENGTH 18", "StatusCodeDescriptionPayee": "PAYER/PAYEE AMOUNT SPLIT VALUE MUST BE PRESENT MINLENGTH 1, MAXLENGTH 18", "DeclineType": "Technical"}, {"RawStatusCode": "V05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI853", "StatusCodeDescriptionPayer": "PAYER/PAYEE AMOUNT SPLIT VALUE MUST BE PRESENT MINLENGTH 1, MAXLENGTH 18", "StatusCodeDescriptionPayee": "PAYER/PAYEE AMOUNT SPLIT VALUE MUST BE PRESENT MINLENGTH 1, MAXLENGTH 18", "DeclineType": "Technical"}, {"RawStatusCode": "V06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI854", "StatusCodeDescriptionPayer": "PAYEE AMOUNT CANNOT BE CHANGED", "StatusCodeDescriptionPayee": "PAYEE AMOUNT CANNOT BE CHANGED", "DeclineType": "Technical"}, {"RawStatusCode": "W01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI855", "StatusCodeDescriptionPayer": "PAYER/PAYEE CREDS NOT PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CREDS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "W02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI856", "StatusCodeDescriptionPayer": "PAYER/PAYEE CREDS CRED MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CREDS CRED MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "W03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI857", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED DATA IS WRONG", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED DATA IS WRONG", "DeclineType": "Technical"}, {"RawStatusCode": "W04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI858", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED AADHAR MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED AADHAR MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "W05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI859", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED OTP MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED OTP MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "W06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI860", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED PIN MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED PIN MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "W07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI861", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED CARD MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED CARD MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "W08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI862", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED PREAPPROVED MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED PREAPPROVED MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "W09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI863", "StatusCodeDescriptionPayer": "PAYER/PAYEE CRED DATA MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE CRED DATA MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "W10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI864", "StatusCodeDescriptionPayer": "PAYER/PAYEE  CRED DATA ENCRYPTED AUTHENTICATION MUST BE PRESENT", "StatusCodeDescriptionPayee": "PAYER/PAYEE  CRED DATA ENCRYPTED AUTHENTICATION MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "X01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI865", "StatusCodeDescriptionPayer": "TXN NOT PRESENT", "StatusCodeDescriptionPayee": "TXN NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "X02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI866", "StatusCodeDescriptionPayer": "TXN ID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN ID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "X03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI867", "StatusCodeDescriptionPayer": "TXN NOTE ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 50", "StatusCodeDescriptionPayee": "TXN NOTE ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 50", "DeclineType": "Technical"}, {"RawStatusCode": "X04", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI868", "StatusCodeDescriptionPayer": "TXN REFID ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "StatusCodeDescriptionPayee": "TXN REFID ALPHANUMERIC; MINLENGTH 1 MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "X06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI869", "StatusCodeDescriptionPayer": "TXN TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TXN TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "X07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI870", "StatusCodeDescriptionPayer": "TXN TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "TXN TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "X08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI871", "StatusCodeDescriptionPayer": "TXN ORGTXNDATE MUST BE PRESENT", "StatusCodeDescriptionPayee": "TXN ORGTXNDATE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "X09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI872", "StatusCodeDescriptionPayer": "TXN ORGTXNDATE SHOULD BE WITHIN 90 DAYS", "StatusCodeDescriptionPayee": "TXN ORGTXNDATE SHOULD BE WITHIN 90 DAYS", "DeclineType": "Technical"}, {"RawStatusCode": "Y01", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI873", "StatusCodeDescriptionPayer": "LINK NOT PRESENT", "StatusCodeDescriptionPayee": "LINK NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "Y02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI874", "StatusCodeDescriptionPayer": "LINK TYPE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "LINK TYPE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "Y03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI875", "StatusCodeDescriptionPayer": "LINK VALUE MUST BE PRESENT/VALID", "StatusCodeDescriptionPayee": "LINK VALUE MUST BE PRESENT/VALID", "DeclineType": "Technical"}, {"RawStatusCode": "Z02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI876", "StatusCodeDescriptionPayer": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "StatusCodeDescriptionPayee": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "DeclineType": "Technical"}, {"RawStatusCode": "Z02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI877", "StatusCodeDescriptionPayer": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "StatusCodeDescriptionPayee": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "DeclineType": "Technical"}, {"RawStatusCode": "Z02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI878", "StatusCodeDescriptionPayer": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "StatusCodeDescriptionPayee": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "DeclineType": "Technical"}, {"RawStatusCode": "Z02", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI879", "StatusCodeDescriptionPayer": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "StatusCodeDescriptionPayee": "VER NUMERIC/DECIMAL MIN LENGTH 1 MAX LENGTH 6", "DeclineType": "Technical"}, {"RawStatusCode": "Z03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI880", "StatusCodeDescriptionPayer": "TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "Z03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI881", "StatusCodeDescriptionPayer": "TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "Z03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI882", "StatusCodeDescriptionPayer": "TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "Z03", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI883", "StatusCodeDescriptionPayer": "TS MUST BE ISO_ZONE FORMAT", "StatusCodeDescriptionPayee": "TS MUST BE ISO_ZONE FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "Z06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI884", "StatusCodeDescriptionPayer": "MSGID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "MSGID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "Z06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI885", "StatusCodeDescriptionPayer": "MSGID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "MSGID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "Z06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI886", "StatusCodeDescriptionPayer": "MSGID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "MSGID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "Z06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI887", "StatusCodeDescriptionPayer": "MSGID MUST BE PRESENT MAXLENGTH 35", "StatusCodeDescriptionPayee": "MSGID MUST BE PRESENT MAXLENGTH 35", "DeclineType": "Technical"}, {"RawStatusCode": "1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI888", "StatusCodeDescriptionPayer": "UNABLE TO PROCESS REVERSAL", "StatusCodeDescriptionPayee": "UNABLE TO PROCESS REVERSAL", "DeclineType": "Technical"}, {"RawStatusCode": "4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI889", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI890", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI891", "StatusCodeDescriptionPayer": "HOST (CBS) OFFLINE", "StatusCodeDescriptionPayee": "HOST (CBS) OFFLINE", "DeclineType": "Technical"}, {"RawStatusCode": "8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI892", "StatusCodeDescriptionPayer": "ISSUER NODE OFFLINE", "StatusCodeDescriptionPayee": "ISSUER NODE OFFLINE", "DeclineType": "Technical"}, {"RawStatusCode": "10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI893", "StatusCodeDescriptionPayer": "PIN BLOCK ERROR", "StatusCodeDescriptionPayee": "PIN BLOCK ERROR", "DeclineType": "Technical"}, {"RawStatusCode": "100", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI894", "StatusCodeDescriptionPayer": "PI (BASIC) ATTRIBUTES OF DEMOGRAPHIC DATA DID NOT MATCH", "StatusCodeDescriptionPayee": "PI (BASIC) ATTRIBUTES OF DEMOGRAPHIC DATA DID NOT MATCH", "DeclineType": "Technical"}, {"RawStatusCode": "12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI895", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI896", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI897", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI898", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "20", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI899", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "200", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI900", "StatusCodeDescriptionPayer": "PA (ADDRESS) ATTRIBUTES OF DEMOGRAPHIC DATA DID NOT MATCH", "StatusCodeDescriptionPayee": "PA (ADDRESS) ATTRIBUTES OF DEMOGRAPHIC DATA DID NOT MATCH", "DeclineType": "Technical"}, {"RawStatusCode": "21", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI901", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "22", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI902", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "30", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI903", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "300", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI904", "StatusCodeDescriptionPayer": "BIOMETRIC DATA DID NOT MATCH", "StatusCodeDescriptionPayee": "BIOMETRIC DATA DID NOT MATCH", "DeclineType": "Technical"}, {"RawStatusCode": "310", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI905", "StatusCodeDescriptionPayer": "DUPLICATE FINGERS USED", "StatusCodeDescriptionPayee": "DUPLICATE FINGERS USED", "DeclineType": "Technical"}, {"RawStatusCode": "311", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI906", "StatusCodeDescriptionPayer": "DUPLICATE IRISES USED", "StatusCodeDescriptionPayee": "DUPLICATE IRISES USED", "DeclineType": "Technical"}, {"RawStatusCode": "312", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI907", "StatusCodeDescriptionPayer": "FMR AND FIR CANNOT BE USED IN SAME TRANSACTION", "StatusCodeDescriptionPayee": "FMR AND FIR CANNOT BE USED IN SAME TRANSACTION", "DeclineType": "Technical"}, {"RawStatusCode": "313", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI908", "StatusCodeDescriptionPayer": "SINGLE FIR RECORD CONTAINS MORE THAN ONE FINGER", "StatusCodeDescriptionPayee": "SINGLE FIR RECORD CONTAINS MORE THAN ONE FINGER", "DeclineType": "Technical"}, {"RawStatusCode": "314", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI909", "StatusCodeDescriptionPayer": "NUMBER OF FMR/FIR SHOULD NOT EXCEED 10", "StatusCodeDescriptionPayee": "NUMBER OF FMR/FIR SHOULD NOT EXCEED 10", "DeclineType": "Technical"}, {"RawStatusCode": "315", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI910", "StatusCodeDescriptionPayer": "NUMBER OF IIR SHOULD NOT EXCEED 2", "StatusCodeDescriptionPayee": "NUMBER OF IIR SHOULD NOT EXCEED 2", "DeclineType": "Technical"}, {"RawStatusCode": "36", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI911", "StatusCodeDescriptionPayer": "RESTRICTED CARD", "StatusCodeDescriptionPayee": "RESTRICTED CARD", "DeclineType": "Business"}, {"RawStatusCode": "40", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI912", "StatusCodeDescriptionPayer": "INVALID DEBIT ACCOUNT", "StatusCodeDescriptionPayee": "INVALID DEBIT ACCOUNT", "DeclineType": "Technical"}, {"RawStatusCode": "400", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI913", "StatusCodeDescriptionPayer": "OTP VALIDATION FAILED", "StatusCodeDescriptionPayee": "OTP VALIDATION FAILED", "DeclineType": "Business"}, {"RawStatusCode": "401", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI914", "StatusCodeDescriptionPayer": "TKN VALIDATION FAILED", "StatusCodeDescriptionPayee": "TKN VALIDATION FAILED", "DeclineType": "Technical"}, {"RawStatusCode": "43", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI915", "StatusCodeDescriptionPayer": "LOST OR STOLEN ACCOUNT", "StatusCodeDescriptionPayee": "LOST OR STOLEN ACCOUNT", "DeclineType": "Technical"}, {"RawStatusCode": "500", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI916", "StatusCodeDescriptionPayer": "INVALID SKEYENCRYPTION", "StatusCodeDescriptionPayee": "INVALID SKEYENCRYPTION", "DeclineType": "Technical"}, {"RawStatusCode": "501", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI917", "StatusCodeDescriptionPayer": "INVALID VALUE FOR <PERSON><PERSON> ATTRIBUTE IN SKEY ELEMENT", "StatusCodeDescriptionPayee": "INVALID VALUE FOR <PERSON><PERSON> ATTRIBUTE IN SKEY ELEMENT", "DeclineType": "Technical"}, {"RawStatusCode": "502", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI918", "StatusCodeDescriptionPayer": "INVALID PID ENCRYPTION", "StatusCodeDescriptionPayee": "INVALID PID ENCRYPTION", "DeclineType": "Technical"}, {"RawStatusCode": "503", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI919", "StatusCodeDescriptionPayer": "INVALID HMAC ENCRYPTION", "StatusCodeDescriptionPayee": "INVALID HMAC ENCRYPTION", "DeclineType": "Technical"}, {"RawStatusCode": "504", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI920", "StatusCodeDescriptionPayer": "SESSION KEY REINITIATION REQUIRED DUE TO EXPIRY OR KEY OUT OF SYNC", "StatusCodeDescriptionPayee": "SESSION KEY REINITIATION REQUIRED DUE TO EXPIRY OR KEY OUT OF SYNC", "DeclineType": "Technical"}, {"RawStatusCode": "505", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI921", "StatusCodeDescriptionPayer": "SYNCHRONIZED SKEY USAGE IS NOT ALLOWED", "StatusCodeDescriptionPayee": "SYNCHRONIZED SKEY USAGE IS NOT ALLOWED", "DeclineType": "Technical"}, {"RawStatusCode": "510", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI922", "StatusCodeDescriptionPayer": "INVALID AUTH XML FORMAT", "StatusCodeDescriptionPayee": "INVALID AUTH XML FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "511", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI923", "StatusCodeDescriptionPayer": "INVALID PID XML FORMAT", "StatusCodeDescriptionPayee": "INVALID PID XML FORMAT", "DeclineType": "Technical"}, {"RawStatusCode": "52", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI924", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "520", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI925", "StatusCodeDescriptionPayer": "INVALID DEVICE", "StatusCodeDescriptionPayee": "INVALID DEVICE", "DeclineType": "Technical"}, {"RawStatusCode": "521", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI926", "StatusCodeDescriptionPayer": "INVALID FINGER DEVICE (FDC IN META ELEMENT)", "StatusCodeDescriptionPayee": "INVALID FINGER DEVICE (FDC IN META ELEMENT)", "DeclineType": "Technical"}, {"RawStatusCode": "522", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI927", "StatusCodeDescriptionPayer": "INVALID IRIS DEVICE (IDC IN META ELEMENT)", "StatusCodeDescriptionPayee": "INVALID IRIS DEVICE (IDC IN META ELEMENT)", "DeclineType": "Technical"}, {"RawStatusCode": "530", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI928", "StatusCodeDescriptionPayer": "INVALID AUTHENTICATOR CODE", "StatusCodeDescriptionPayee": "INVALID AUTHENTICATOR CODE", "DeclineType": "Technical"}, {"RawStatusCode": "54", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI929", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "540", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI930", "StatusCodeDescriptionPayer": "INVALID AUTH XML VERSION", "StatusCodeDescriptionPayee": "INVALID AUTH XML VERSION", "DeclineType": "Technical"}, {"RawStatusCode": "541", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI931", "StatusCodeDescriptionPayer": "INVALID PID XML VERSION", "StatusCodeDescriptionPayee": "INVALID PID XML VERSION", "DeclineType": "Technical"}, {"RawStatusCode": "542", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI932", "StatusCodeDescriptionPayer": "AUA NOT AUTHORIZED FOR ASA", "StatusCodeDescriptionPayee": "AUA NOT AUTHORIZED FOR ASA", "DeclineType": "Technical"}, {"RawStatusCode": "543", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI933", "StatusCodeDescriptionPayer": "SUB-AUA NOT ASSOCIATED WITH AUA", "StatusCodeDescriptionPayee": "SUB-AUA NOT ASSOCIATED WITH AUA", "DeclineType": "Technical"}, {"RawStatusCode": "550", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI934", "StatusCodeDescriptionPayer": "INVALID USES ELEMENT ATTRIBUTES", "StatusCodeDescriptionPayee": "INVALID USES ELEMENT ATTRIBUTES", "DeclineType": "Technical"}, {"RawStatusCode": "561", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI935", "StatusCodeDescriptionPayer": "REQUEST EXPIRED (PID->TS VALUE IS OLDER THAN N HOURS WHERE N IS A CONFIGURED THRESHOLD IN AUTHENTICATIONSERVER)", "StatusCodeDescriptionPayee": "REQUEST EXPIRED (PID->TS VALUE IS OLDER THAN N HOURS WHERE N IS A CONFIGURED THRESHOLD IN AUTHENTICATIONSERVER)", "DeclineType": "Technical"}, {"RawStatusCode": "562", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI936", "StatusCodeDescriptionPayer": "TIMESTAMP VALUE IS FUTURE TIME (VALUE SPECIFIED PID->TS IS AHEAD OF AUTHENTICATION SERVER TIME BEYOND ACCEPTABLETHRESHOLD)", "StatusCodeDescriptionPayee": "TIMESTAMP VALUE IS FUTURE TIME (VALUE SPECIFIED PID->TS IS AHEAD OF AUTHENTICATION SERVER TIME BEYOND ACCEPTABLETHRESHOLD)", "DeclineType": "Technical"}, {"RawStatusCode": "563", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI937", "StatusCodeDescriptionPayer": "DUPLICATE REQUEST (THIS ERROR OCCURS WHEN EXACTLY SAME AUTHENTICATIONREQUEST WAS RESENT BY AUA)", "StatusCodeDescriptionPayee": "DUPLICATE REQUEST (THIS ERROR OCCURS WHEN EXACTLY SAME AUTHENTICATIONREQUEST WAS RESENT BY AUA)", "DeclineType": "Technical"}, {"RawStatusCode": "564", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI938", "StatusCodeDescriptionPayer": "HMAC VALIDATION FAILED", "StatusCodeDescriptionPayee": "HMAC VALIDATION FAILED", "DeclineType": "Technical"}, {"RawStatusCode": "565", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI939", "StatusCodeDescriptionPayer": "LICENSE KEY HAS EXPIRED", "StatusCodeDescriptionPayee": "LICENSE KEY HAS EXPIRED", "DeclineType": "Technical"}, {"RawStatusCode": "566", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI940", "StatusCodeDescriptionPayer": "INVALID LICENSE KEY", "StatusCodeDescriptionPayee": "INVALID LICENSE KEY", "DeclineType": "Technical"}, {"RawStatusCode": "567", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI941", "StatusCodeDescriptionPayer": "INVALID INPUT (THIS ERROR OCCURS WHEN SOME UNSUPPORTED CHARACTERS WERE FOUND IN INDIAN LANGUAGE VALUES,LNAME OR LAV)", "StatusCodeDescriptionPayee": "INVALID INPUT (THIS ERROR OCCURS WHEN SOME UNSUPPORTED CHARACTERS WERE FOUND IN INDIAN LANGUAGE VALUES,LNAME OR LAV)", "DeclineType": "Technical"}, {"RawStatusCode": "568", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI942", "StatusCodeDescriptionPayer": "UNSUPPORTED LANGUAGE", "StatusCodeDescriptionPayee": "UNSUPPORTED LANGUAGE", "DeclineType": "Technical"}, {"RawStatusCode": "569", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI943", "StatusCodeDescriptionPayer": "DIGITAL SIGNATURE VERIFICATION FAILED (THIS MEANS THAT AUTHENTICATIONREQUEST XML WAS MODIFIED AFTER IT WAS SIGNED)", "StatusCodeDescriptionPayee": "DIGITAL SIGNATURE VERIFICATION FAILED (THIS MEANS THAT AUTHENTICATIONREQUEST XML WAS MODIFIED AFTER IT WAS SIGNED)", "DeclineType": "Technical"}, {"RawStatusCode": "570", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI944", "StatusCodeDescriptionPayer": "INVALID KEY INFO IN DIGITAL SIGNATURE (THIS MEANS THAT CERTIFICATE USED FOR SIGNING THE AUTHENTICATION REQUEST IS NOT VALID – IT IS EITHER EXPIRED, OR DOES NOT BELONG TO THE AUA OR IS NOT CREATED BY A WELL-KNOWN CERTIFICATION AUTHORITY)", "StatusCodeDescriptionPayee": "INVALID KEY INFO IN DIGITAL SIGNATURE (THIS MEANS THAT CERTIFICATE USED FOR SIGNING THE AUTHENTICATION REQUEST IS NOT VALID – IT IS EITHER EXPIRED, OR DOES NOT BELONG TO THE AUA OR IS NOT CREATED BY A WELL-KNOWN CERTIFICATION AUTHORITY)", "DeclineType": "Technical"}, {"RawStatusCode": "571", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI945", "StatusCodeDescriptionPayer": "PIN REQUIRES RESET (THIS ERROR WILL BE RETURNED IF RESIDENT IS USING THE DEFAULT PIN WHICH NEEDS TO BE RESET BEFOREUSAGE)", "StatusCodeDescriptionPayee": "PIN REQUIRES RESET (THIS ERROR WILL BE RETURNED IF RESIDENT IS USING THE DEFAULT PIN WHICH NEEDS TO BE RESET BEFOREUSAGE)", "DeclineType": "Technical"}, {"RawStatusCode": "572", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI946", "StatusCodeDescriptionPayer": "INVALID BIOMETRIC POSITION (THIS ERROR IS RETURNED IF BIOMETRIC POSITION VALUE POS ATTRIBUTE IN BIO ELEMENT - IS NOT APPLICABLE FOR A GIVEN BIOMETRIC TYPE - TYPE ATTRIBUTE IN BIO ELEMENT )", "StatusCodeDescriptionPayee": "INVALID BIOMETRIC POSITION (THIS ERROR IS RETURNED IF BIOMETRIC POSITION VALUE POS ATTRIBUTE IN BIO ELEMENT - IS NOT APPLICABLE FOR A GIVEN BIOMETRIC TYPE - TYPE ATTRIBUTE IN BIO ELEMENT )", "DeclineType": "Technical"}, {"RawStatusCode": "573", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI947", "StatusCodeDescriptionPayer": "PI USAGE NOT ALLOWED AS PER LICENSE", "StatusCodeDescriptionPayee": "PI USAGE NOT ALLOWED AS PER LICENSE", "DeclineType": "Technical"}, {"RawStatusCode": "574", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI948", "StatusCodeDescriptionPayer": "PA USAGE NOT ALLOWED AS PER LICENSE", "StatusCodeDescriptionPayee": "PA USAGE NOT ALLOWED AS PER LICENSE", "DeclineType": "Technical"}, {"RawStatusCode": "575", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI949", "StatusCodeDescriptionPayer": "PFA USAGE NOT ALLOWED AS PER LICENSE", "StatusCodeDescriptionPayee": "PFA USAGE NOT ALLOWED AS PER LICENSE", "DeclineType": "Technical"}, {"RawStatusCode": "576", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI950", "StatusCodeDescriptionPayer": "FMR USAGE NOT ALLOWED AS PER LICENSE", "StatusCodeDescriptionPayee": "FMR USAGE NOT ALLOWED AS PER LICENSE", "DeclineType": "Technical"}, {"RawStatusCode": "577", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI951", "StatusCodeDescriptionPayer": "FIR USAGE NOT ALLOWED AS PER LICENSE", "StatusCodeDescriptionPayee": "FIR USAGE NOT ALLOWED AS PER LICENSE", "DeclineType": "Technical"}, {"RawStatusCode": "578", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI952", "StatusCodeDescriptionPayer": "IIR USAGE NOT ALLOWED AS PER LICENSE", "StatusCodeDescriptionPayee": "IIR USAGE NOT ALLOWED AS PER LICENSE", "DeclineType": "Technical"}, {"RawStatusCode": "579", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI953", "StatusCodeDescriptionPayer": "OTP USAGE NOT ALLOWED AS PER LICENSE", "StatusCodeDescriptionPayee": "OTP USAGE NOT ALLOWED AS PER LICENSE", "DeclineType": "Technical"}, {"RawStatusCode": "580", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI954", "StatusCodeDescriptionPayer": "PIN USAGE NOT ALLOWED AS PER LICENSE", "StatusCodeDescriptionPayee": "PIN USAGE NOT ALLOWED AS PER LICENSE", "DeclineType": "Technical"}, {"RawStatusCode": "581", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI955", "StatusCodeDescriptionPayer": "FUZZY MATCHING <PERSON>GE NOT ALLOWED AS PER LICENSE", "StatusCodeDescriptionPayee": "FUZZY MATCHING <PERSON>GE NOT ALLOWED AS PER LICENSE", "DeclineType": "Technical"}, {"RawStatusCode": "582", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI956", "StatusCodeDescriptionPayer": "LOCAL LANGUAGE USAGE NOT ALLOWED AS PER LICENSE", "StatusCodeDescriptionPayee": "LOCAL LANGUAGE USAGE NOT ALLOWED AS PER LICENSE", "DeclineType": "Technical"}, {"RawStatusCode": "584", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI957", "StatusCodeDescriptionPayer": "INVALID PIN CODE IN META ELEMENT", "StatusCodeDescriptionPayee": "INVALID PIN CODE IN META ELEMENT", "DeclineType": "Technical"}, {"RawStatusCode": "585", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI958", "StatusCodeDescriptionPayer": "INVALID GEO CODE IN META ELEMENT", "StatusCodeDescriptionPayee": "INVALID GEO CODE IN META ELEMENT", "DeclineType": "Technical"}, {"RawStatusCode": "62", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI959", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "63", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI960", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "65", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI961", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "68", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI962", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "710", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI963", "StatusCodeDescriptionPayer": "MISSING PI DATA AS SPECIFIED IN USES", "StatusCodeDescriptionPayee": "MISSING PI DATA AS SPECIFIED IN USES", "DeclineType": "Technical"}, {"RawStatusCode": "720", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI964", "StatusCodeDescriptionPayer": "MISSING PA DATA AS SPECIFIED IN USES", "StatusCodeDescriptionPayee": "MISSING PA DATA AS SPECIFIED IN USES", "DeclineType": "Technical"}, {"RawStatusCode": "721", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI965", "StatusCodeDescriptionPayer": "MISSING PFA DATA AS SPECIFIED IN USES", "StatusCodeDescriptionPayee": "MISSING PFA DATA AS SPECIFIED IN USES", "DeclineType": "Technical"}, {"RawStatusCode": "730", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI966", "StatusCodeDescriptionPayer": "MISSING PIN DATA AS SPECIFIED IN USES", "StatusCodeDescriptionPayee": "MISSING PIN DATA AS SPECIFIED IN USES", "DeclineType": "Technical"}, {"RawStatusCode": "740", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI967", "StatusCodeDescriptionPayer": "MISSING OTP DATA AS SPECIFIED IN USES", "StatusCodeDescriptionPayee": "MISSING OTP DATA AS SPECIFIED IN USES", "DeclineType": "Technical"}, {"RawStatusCode": "800", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI968", "StatusCodeDescriptionPayer": "INVALID BIOMETRIC DATA", "StatusCodeDescriptionPayee": "INVALID BIOMETRIC DATA", "DeclineType": "Technical"}, {"RawStatusCode": "810", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI969", "StatusCodeDescriptionPayer": "MISSING BIOMETRIC DATA AS SPECIFIED IN USES", "StatusCodeDescriptionPayee": "MISSING BIOMETRIC DATA AS SPECIFIED IN USES", "DeclineType": "Technical"}, {"RawStatusCode": "811", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI970", "StatusCodeDescriptionPayer": "MISSING BIOMETRIC DATA IN CIDR FOR THE GIVEN AADHAAR NUMBER", "StatusCodeDescriptionPayee": "MISSING BIOMETRIC DATA IN CIDR FOR THE GIVEN AADHAAR NUMBER", "DeclineType": "Technical"}, {"RawStatusCode": "812", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI971", "StatusCodeDescriptionPayer": "RESIDENT HAS NOT DONE BEST FINGER DETECTION APPLICATION SHOULD INITIATE BFD APPLICATIONTO HELP RESIDENT IDENTIFY THEIR BEST FINGERS SEE AADHAAR BEST FINGER DETECTIONAPI SPECIFICATION", "StatusCodeDescriptionPayee": "RESIDENT HAS NOT DONE BEST FINGER DETECTION APPLICATION SHOULD INITIATE BFD APPLICATIONTO HELP RESIDENT IDENTIFY THEIR BEST FINGERS SEE AADHAAR BEST FINGER DETECTIONAPI SPECIFICATION", "DeclineType": "Technical"}, {"RawStatusCode": "820", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI972", "StatusCodeDescriptionPayer": "MISSING OR EMPTY VALUE FOR BT ATTRIBUTE IN USES ELEMENT", "StatusCodeDescriptionPayee": "MISSING OR EMPTY VALUE FOR BT ATTRIBUTE IN USES ELEMENT", "DeclineType": "Technical"}, {"RawStatusCode": "821", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI973", "StatusCodeDescriptionPayer": "INVALID VALUE IN THE BT ATTRIBUTE OF USES ELEMENT", "StatusCodeDescriptionPayee": "INVALID VALUE IN THE BT ATTRIBUTE OF USES ELEMENT", "DeclineType": "Technical"}, {"RawStatusCode": "901", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI974", "StatusCodeDescriptionPayer": "NO AUTHENTICATION DATAFOUND IN THE REQUEST (THIS CORRESPONDS TO A SCENARIO WHEREIN NONE OF THE AUTH DATA –DEMO, PV, OR BIOS – IS PRESENT)", "StatusCodeDescriptionPayee": "NO AUTHENTICATION DATAFOUND IN THE REQUEST (THIS CORRESPONDS TO A SCENARIO WHEREIN NONE OF THE AUTH DATA –DEMO, PV, OR BIOS – IS PRESENT)", "DeclineType": "Technical"}, {"RawStatusCode": "902", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI975", "StatusCodeDescriptionPayer": "INVALID DOB VALUE IN THE PI ELEMENT (THIS CORRESPONDS TO A SCENARIOS WHEREIN DOB ATTRIBUTE IS NOT OF THE FORMAT YYYY OR YYYY-MM-DD, OR THE AGE OF RESIDENT IS NOT IN VALID RANGE)", "StatusCodeDescriptionPayee": "INVALID DOB VALUE IN THE PI ELEMENT (THIS CORRESPONDS TO A SCENARIOS WHEREIN DOB ATTRIBUTE IS NOT OF THE FORMAT YYYY OR YYYY-MM-DD, OR THE AGE OF RESIDENT IS NOT IN VALID RANGE)", "DeclineType": "Technical"}, {"RawStatusCode": "91", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI976", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "910", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI977", "StatusCodeDescriptionPayer": "INVALID MV VALUE IN THE PI ELEMENT", "StatusCodeDescriptionPayee": "INVALID MV VALUE IN THE PI ELEMENT", "DeclineType": "Technical"}, {"RawStatusCode": "911", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI978", "StatusCodeDescriptionPayer": "INVALID MV VALUE IN THE PFA ELEMENT", "StatusCodeDescriptionPayee": "INVALID MV VALUE IN THE PFA ELEMENT", "DeclineType": "Technical"}, {"RawStatusCode": "912", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI979", "StatusCodeDescriptionPayer": "INVALID MS VALUE", "StatusCodeDescriptionPayee": "INVALID MS VALUE", "DeclineType": "Technical"}, {"RawStatusCode": "913", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI980", "StatusCodeDescriptionPayer": "BOTH PA AND <PERSON>FA ARE PRESENT IN THE AUTHENTICATIONREQUEST (PA AND PFA ARE MUTUALLY EXCLUSIVE)", "StatusCodeDescriptionPayee": "BOTH PA AND <PERSON>FA ARE PRESENT IN THE AUTHENTICATIONREQUEST (PA AND PFA ARE MUTUALLY EXCLUSIVE)", "DeclineType": "Technical"}, {"RawStatusCode": "92", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI981", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "930-939", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI982", "StatusCodeDescriptionPayer": "TECHNICAL ERROR THAT ARE INTERNAL TO AUTHENTICATIONSERVER", "StatusCodeDescriptionPayee": "TECHNICAL ERROR THAT ARE INTERNAL TO AUTHENTICATIONSERVER", "DeclineType": "Technical"}, {"RawStatusCode": "94", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI983", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "940", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI984", "StatusCodeDescriptionPayer": "UNAUTHORIZED ASA CHANNEL", "StatusCodeDescriptionPayee": "UNAUTHORIZED ASA CHANNEL", "DeclineType": "Technical"}, {"RawStatusCode": "941", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI985", "StatusCodeDescriptionPayer": "UNSPECIFIED ASA CHANNEL", "StatusCodeDescriptionPayee": "UNSPECIFIED ASA CHANNEL", "DeclineType": "Technical"}, {"RawStatusCode": "96", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI986", "StatusCodeDescriptionPayer": "REVERSAL FAILURE", "StatusCodeDescriptionPayee": "REVERSAL FAILURE", "DeclineType": "Technical"}, {"RawStatusCode": "96", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI987", "StatusCodeDescriptionPayer": "REVERSAL FAILURE", "StatusCodeDescriptionPayee": "REVERSAL FAILURE", "DeclineType": "Technical"}, {"RawStatusCode": "96", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI988", "StatusCodeDescriptionPayer": "REVERSAL FAILURE", "StatusCodeDescriptionPayee": "REVERSAL FAILURE", "DeclineType": "Technical"}, {"RawStatusCode": "980", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI989", "StatusCodeDescriptionPayer": "UNSUPPORTED OPTION", "StatusCodeDescriptionPayee": "UNSUPPORTED OPTION", "DeclineType": "Technical"}, {"RawStatusCode": "996", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI990", "StatusCodeDescriptionPayer": "AADHAAR CANCELLED", "StatusCodeDescriptionPayee": "AADHAAR CANCELLED", "DeclineType": "Technical"}, {"RawStatusCode": "996", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI991", "StatusCodeDescriptionPayer": "AADHAAR CANCELLED", "StatusCodeDescriptionPayee": "AADHAAR CANCELLED", "DeclineType": "Technical"}, {"RawStatusCode": "996", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI992", "StatusCodeDescriptionPayer": "AADHAAR CANCELLED", "StatusCodeDescriptionPayee": "AADHAAR CANCELLED", "DeclineType": "Technical"}, {"RawStatusCode": "996", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI993", "StatusCodeDescriptionPayer": "AADHAAR CANCELLED", "StatusCodeDescriptionPayee": "AADHAAR CANCELLED", "DeclineType": "Technical"}, {"RawStatusCode": "996", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI994", "StatusCodeDescriptionPayer": "AADHAAR CANCELLED", "StatusCodeDescriptionPayee": "AADHAAR CANCELLED", "DeclineType": "Technical"}, {"RawStatusCode": "996", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI995", "StatusCodeDescriptionPayer": "AADHAAR CANCELLED", "StatusCodeDescriptionPayee": "AADHAAR CANCELLED", "DeclineType": "Technical"}, {"RawStatusCode": "997", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI996", "StatusCodeDescriptionPayer": "AADHAAR SUSPENDED", "StatusCodeDescriptionPayee": "AADHAAR SUSPENDED", "DeclineType": "Technical"}, {"RawStatusCode": "997", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI997", "StatusCodeDescriptionPayer": "AADHAAR SUSPENDED", "StatusCodeDescriptionPayee": "AADHAAR SUSPENDED", "DeclineType": "Technical"}, {"RawStatusCode": "997", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI998", "StatusCodeDescriptionPayer": "AADHAAR SUSPENDED", "StatusCodeDescriptionPayee": "AADHAAR SUSPENDED", "DeclineType": "Technical"}, {"RawStatusCode": "997", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI999", "StatusCodeDescriptionPayer": "AADHAAR SUSPENDED", "StatusCodeDescriptionPayee": "AADHAAR SUSPENDED", "DeclineType": "Technical"}, {"RawStatusCode": "997", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1000", "StatusCodeDescriptionPayer": "AADHAAR SUSPENDED", "StatusCodeDescriptionPayee": "AADHAAR SUSPENDED", "DeclineType": "Technical"}, {"RawStatusCode": "997", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1001", "StatusCodeDescriptionPayer": "AADHAAR SUSPENDED", "StatusCodeDescriptionPayee": "AADHAAR SUSPENDED", "DeclineType": "Technical"}, {"RawStatusCode": "997", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1002", "StatusCodeDescriptionPayer": "AADHAAR SUSPENDED", "StatusCodeDescriptionPayee": "AADHAAR SUSPENDED", "DeclineType": "Technical"}, {"RawStatusCode": "997", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1003", "StatusCodeDescriptionPayer": "AADHAAR SUSPENDED", "StatusCodeDescriptionPayee": "AADHAAR SUSPENDED", "DeclineType": "Technical"}, {"RawStatusCode": "997", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1004", "StatusCodeDescriptionPayer": "AADHAAR SUSPENDED", "StatusCodeDescriptionPayee": "AADHAAR SUSPENDED", "DeclineType": "Technical"}, {"RawStatusCode": "997", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1005", "StatusCodeDescriptionPayer": "AADHAAR SUSPENDED", "StatusCodeDescriptionPayee": "AADHAAR SUSPENDED", "DeclineType": "Technical"}, {"RawStatusCode": "998", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1006", "StatusCodeDescriptionPayer": "INVALID AADHAAR NUMBER OR NON AVAILABILITY OF AADHAAR DATA", "StatusCodeDescriptionPayee": "INVALID AADHAAR NUMBER OR NON AVAILABILITY OF AADHAAR DATA", "DeclineType": "Technical"}, {"RawStatusCode": "998", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1007", "StatusCodeDescriptionPayer": "INVALID AADHAAR NUMBER OR NON AVAILABILITY OF AADHAAR DATA", "StatusCodeDescriptionPayee": "INVALID AADHAAR NUMBER OR NON AVAILABILITY OF AADHAAR DATA", "DeclineType": "Technical"}, {"RawStatusCode": "998", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1008", "StatusCodeDescriptionPayer": "INVALID AADHAAR NUMBER OR NON AVAILABILITY OF AADHAAR DATA", "StatusCodeDescriptionPayee": "INVALID AADHAAR NUMBER OR NON AVAILABILITY OF AADHAAR DATA", "DeclineType": "Technical"}, {"RawStatusCode": "999", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1009", "StatusCodeDescriptionPayer": "UNKNOWN ERROR", "StatusCodeDescriptionPayee": "UNKNOWN ERROR", "DeclineType": "Technical"}, {"RawStatusCode": "L06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1010", "StatusCodeDescriptionPayer": "KEY CODE HAS NOT BEEN PROVIDED IN INPUT", "StatusCodeDescriptionPayee": "KEY CODE HAS NOT BEEN PROVIDED IN INPUT", "DeclineType": "Technical"}, {"RawStatusCode": "L07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1011", "StatusCodeDescriptionPayer": "ERROR WHILE PARSING KEY CODE FROM INPUT", "StatusCodeDescriptionPayee": "ERROR WHILE PARSING KEY CODE FROM INPUT", "DeclineType": "Technical"}, {"RawStatusCode": "L08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1012", "StatusCodeDescriptionPayer": "XML PAYLOAD HAS NOT BEEN PROVIDED IN INPUT", "StatusCodeDescriptionPayee": "XML PAYLOAD HAS NOT BEEN PROVIDED IN INPUT", "DeclineType": "Technical"}, {"RawStatusCode": "L09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1013", "StatusCodeDescriptionPayer": "ERROR WHILE PARSING XML PAYLOAD FROM INPUT", "StatusCodeDescriptionPayee": "ERROR WHILE PARSING XML PAYLOAD FROM INPUT", "DeclineType": "Technical"}, {"RawStatusCode": "L10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1014", "StatusCodeDescriptionPayer": "ERROR WHILE PARSING CONTROLS FROM INPUT", "StatusCodeDescriptionPayee": "ERROR WHILE PARSING CONTROLS FROM INPUT", "DeclineType": "Technical"}, {"RawStatusCode": "L11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1015", "StatusCodeDescriptionPayer": "ERROR WHILE PARSING CONFIGURATION FROM INPUT", "StatusCodeDescriptionPayee": "ERROR WHILE PARSING CONFIGURATION FROM INPUT", "DeclineType": "Technical"}, {"RawStatusCode": "L12", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1016", "StatusCodeDescriptionPayer": "SALT HAS NOT BEEN PROVIDED IN INPUT", "StatusCodeDescriptionPayee": "SALT HAS NOT BEEN PROVIDED IN INPUT", "DeclineType": "Technical"}, {"RawStatusCode": "L13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1017", "StatusCodeDescriptionPayer": "ERROR WHILE PARSING SALT FROM INPUT", "StatusCodeDescriptionPayee": "ERROR WHILE PARSING SALT FROM INPUT", "DeclineType": "Technical"}, {"RawStatusCode": "L14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1018", "StatusCodeDescriptionPayer": "ERROR WHILE PARSING PAY INFO FROM INPUT", "StatusCodeDescriptionPayee": "ERROR WHILE PARSING PAY INFO FROM INPUT", "DeclineType": "Technical"}, {"RawStatusCode": "L15", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1019", "StatusCodeDescriptionPayer": "ERROR WHILE PARSING LOCALE FROM INPUT", "StatusCodeDescriptionPayee": "ERROR WHILE PARSING LOCALE FROM INPUT", "DeclineType": "Technical"}, {"RawStatusCode": "L16", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1020", "StatusCodeDescriptionPayer": "UNKNOWN ERROR OCCURRED", "StatusCodeDescriptionPayee": "UNKNOWN ERROR OCCURRED", "DeclineType": "Technical"}, {"RawStatusCode": "L17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1021", "StatusCodeDescriptionPayer": "TRUST HAS NOT BEEN PROVIDED", "StatusCodeDescriptionPayee": "TRUST HAS NOT BEEN PROVIDED", "DeclineType": "Technical"}, {"RawStatusCode": "L18", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1022", "StatusCodeDescriptionPayer": "MANDATORY SALT VALUES HAVE NOT BEEN PROVIDED", "StatusCodeDescriptionPayee": "MANDATORY SALT VALUES HAVE NOT BEEN PROVIDED", "DeclineType": "Technical"}, {"RawStatusCode": "L19", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1023", "StatusCodeDescriptionPayer": "ERROR WHILE PARSING MA<PERSON><PERSON>ORY SALT VALUES", "StatusCodeDescriptionPayee": "ERROR WHILE PARSING MA<PERSON><PERSON>ORY SALT VALUES", "DeclineType": "Technical"}, {"RawStatusCode": "L20", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1024", "StatusCodeDescriptionPayer": "TRUST IS NOT VALID", "StatusCodeDescriptionPayee": "TRUST IS NOT VALID", "DeclineType": "Technical"}, {"RawStatusCode": "M0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1025", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "M1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1026", "StatusCodeDescriptionPayer": "INVALID BENEFICIARY DETAILS", "StatusCodeDescriptionPayee": "INVALID BENEFICIARY DETAILS", "DeclineType": "Business"}, {"RawStatusCode": "M3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1027", "StatusCodeDescriptionPayer": "ACCOUNT BLOCKED/FROZEN", "StatusCodeDescriptionPayee": "ACCOUNT BLOCKED/FROZEN", "DeclineType": "Business"}, {"RawStatusCode": "M4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1028", "StatusCodeDescriptionPayer": "NRE ACCOUNT", "StatusCodeDescriptionPayee": "NRE ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "M5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1029", "StatusCodeDescriptionPayer": "ACCOUNT CLOSED", "StatusCodeDescriptionPayee": "ACCOUNT CLOSED", "DeclineType": "Business"}, {"RawStatusCode": "M6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1030", "StatusCodeDescriptionPayer": "LIMIT EXCEEDED FOR MEMBER BANK", "StatusCodeDescriptionPayee": "LIMIT EXCEEDED FOR MEMBER BANK", "DeclineType": "Technical"}, {"RawStatusCode": "M8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1031", "StatusCodeDescriptionPayer": "INVALID OTP", "StatusCodeDescriptionPayee": "INVALID OTP", "DeclineType": "Business"}, {"RawStatusCode": "M9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1032", "StatusCodeDescriptionPayer": "INVALID / INCORRECT OTP", "StatusCodeDescriptionPayee": "INVALID / INCORRECT OTP", "DeclineType": "Business"}, {"RawStatusCode": "MA", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1033", "StatusCodeDescriptionPayer": "MERCHANT ERROR", "StatusCodeDescriptionPayee": "MERCHANT ERROR", "DeclineType": "Business"}, {"RawStatusCode": "MC", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1034", "StatusCodeDescriptionPayer": "FUNCTIONALITY NOT YET AVAILABLE FOR MERCHANT THROUGH THE PAYEE BANK", "StatusCodeDescriptionPayee": "FUNCTIONALITY NOT YET AVAILABLE FOR MERCHANT THROUGH THE PAYEE BANK", "DeclineType": "Business"}, {"RawStatusCode": "MF", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1035", "StatusCodeDescriptionPayer": "MERCHANT SYSTEM NOT AVAILABLE, PLEASE TRY AGAIN", "StatusCodeDescriptionPayee": "MERCHANT SYSTEM NOT AVAILABLE, PLEASE TRY AGAIN", "DeclineType": "Technical"}, {"RawStatusCode": "MG", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1036", "StatusCodeDescriptionPayer": "FUNCTIONALITY NOT YET AVAILABLE FOR CUSTOMER THROUGH THE ISSUING BANK", "StatusCodeDescriptionPayee": "FUNCTIONALITY NOT YET AVAILABLE FOR CUSTOMER THROUGH THE ISSUING BANK", "DeclineType": "Business"}, {"RawStatusCode": "MH", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1037", "StatusCodeDescriptionPayer": "OTP TRANSACTION LIMIT EXCEEDED", "StatusCodeDescriptionPayee": "OTP TRANSACTION LIMIT EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "MK", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1038", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "ML", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1039", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "MP", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1040", "StatusCodeDescriptionPayer": "Beneficiary bank not live on particular transaction type", "StatusCodeDescriptionPayee": "Beneficiary bank not live on particular transaction type", "DeclineType": "Business"}, {"RawStatusCode": "MU", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1041", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "MV", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1042", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Business"}, {"RawStatusCode": "MZ", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1043", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Business"}, {"RawStatusCode": "U64", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1044", "StatusCodeDescriptionPayer": "DATA TAG SHOULD CONTAIN 4 PARTS DURING DEVICE REGISTRATION", "StatusCodeDescriptionPayee": "DATA TAG SHOULD CONTAIN 4 PARTS DURING DEVICE REGISTRATION", "DeclineType": "Technical"}, {"RawStatusCode": "U65", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1045", "StatusCodeDescriptionPayer": "CREDS BLOCK SHOULD CONTAIN CORRECT ELEMENTS DURING DEVICE REGISTRATION", "StatusCodeDescriptionPayee": "CREDS BLOCK SHOULD CONTAIN CORRECT ELEMENTS DURING DEVICE REGISTRATION", "DeclineType": "Technical"}, {"RawStatusCode": "U66", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1046", "StatusCodeDescriptionPayer": "DEVICE FINGERPRINT MISMATCH", "StatusCodeDescriptionPayee": "DEVICE FINGERPRINT MISMATCH", "DeclineType": "Business"}, {"RawStatusCode": "U71", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1047", "StatusCodeDescriptionPayer": "MERCHANT CREDIT NOT SUPPORTED IN IMPS", "StatusCodeDescriptionPayee": "MERCHANT CREDIT NOT SUPPORTED IN IMPS", "DeclineType": "Technical"}, {"RawStatusCode": "U72", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1048", "StatusCodeDescriptionPayer": "VAE FAILED", "StatusCodeDescriptionPayee": "VAE FAILED", "DeclineType": "Technical"}, {"RawStatusCode": "U76", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1049", "StatusCodeDescriptionPayer": "MOBILE BANKING REGISTRATION FORMAT NOT SUPPORTED BY THE ISSUER BANK", "StatusCodeDescriptionPayee": "MOBILE BANKING REGISTRATION FORMAT NOT SUPPORTED BY THE ISSUER BANK", "DeclineType": "Technical"}, {"RawStatusCode": "U77", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1050", "StatusCodeDescriptionPayer": "MERCHANT BLOCKED", "StatusCodeDescriptionPayee": "MERCHANT BLOCKED", "DeclineType": "Technical"}, {"RawStatusCode": "U79", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1051", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "U80", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1052", "StatusCodeDescriptionPayer": "Payer PSP throttling decline", "StatusCodeDescriptionPayee": "Payer PSP throttling decline", "DeclineType": "Technical"}, {"RawStatusCode": "U81", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1053", "StatusCodeDescriptionPayer": "REMITTER BANK DEEMED CHECK DECLINE", "StatusCodeDescriptionPayee": "REMITTER BANK DEEMED CHECK DECLINE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U82", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1054", "StatusCodeDescriptionPayer": "READ TIMEOUT IN REQPAY CREDIT", "StatusCodeDescriptionPayee": "READ TIMEOUT IN REQPAY CREDIT", "DeclineType": "Technical"}, {"RawStatusCode": "U83", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1055", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "U85", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1056", "StatusCodeDescriptionPayer": "CONNECTION TIMEOUT IN REQPAY DEBIT", "StatusCodeDescriptionPayee": "CONNECTION TIMEOUT IN REQPAY DEBIT", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U87", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1057", "StatusCodeDescriptionPayer": "READ TIMEOUT IN REQPAY DEBIT", "StatusCodeDescriptionPayee": "READ TIMEOUT IN REQPAY DEBIT", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U88", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1058", "StatusCodeDescriptionPayer": "CONNECTION TIMEOUT IN REQPAY CREDIT", "StatusCodeDescriptionPayee": "CONNECTION TIMEOUT IN REQPAY CREDIT", "DeclineType": "Technical"}, {"RawStatusCode": "U95", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1059", "StatusCodeDescriptionPayer": "PAYEE VPA AADHAAR OR IIN VPA IS DISABLED", "StatusCodeDescriptionPayee": "PAYEE VPA AADHAAR OR IIN VPA IS DISABLED", "DeclineType": "Business"}, {"RawStatusCode": "U96", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1060", "StatusCodeDescriptionPayer": "PAYER AND PAYEE IFSC/ACNUM CAN'T BE SAME", "StatusCodeDescriptionPayee": "PAYER AND PAYEE IFSC/ACNUM CAN'T BE SAME", "DeclineType": "Business"}, {"RawStatusCode": "U97", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1061", "StatusCodeDescriptionPayer": "PSP REQUEST META ACKNOWLEDGEMENT NOT RECEIVED", "StatusCodeDescriptionPayee": "PSP REQUEST META ACKNOWLEDGEMENT NOT RECEIVED", "DeclineType": "Technical"}, {"RawStatusCode": "U98", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1062", "StatusCodeDescriptionPayer": "NULL ACK RECEIVED BY UPI FOR META TRANSACTION", "StatusCodeDescriptionPayee": "NULL ACK RECEIVED BY UPI FOR META TRANSACTION", "DeclineType": "Technical"}, {"RawStatusCode": "U99", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1063", "StatusCodeDescriptionPayer": "NEGATIVE ACK RECEIVED BY UPI FOR META TRANSACTION", "StatusCodeDescriptionPayee": "NEGATIVE ACK RECEIVED BY UPI FOR META TRANSACTION", "DeclineType": "Technical"}, {"RawStatusCode": "UA1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1064", "StatusCodeDescriptionPayer": "HEADER & URL VERSION IS MISMATCHED", "StatusCodeDescriptionPayee": "HEADER & URL VERSION IS MISMATCHED", "DeclineType": "Technical"}, {"RawStatusCode": "UA2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1065", "StatusCodeDescriptionPayer": "VERSION/TAGS SENT NOT SUPPORTED BY PSP/BANK", "StatusCodeDescriptionPayee": "VERSION/TAGS SENT NOT SUPPORTED BY PSP/BANK", "DeclineType": "Technical"}, {"RawStatusCode": "UA3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1066", "StatusCodeDescriptionPayer": "PAYER/PAYEE PSP,HEADER OR URL VERSION MISMATCHED", "StatusCodeDescriptionPayee": "PAYER/PAYEE PSP,HEADER OR URL VERSION MISMATCHED", "DeclineType": "Technical"}, {"RawStatusCode": "UA4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1067", "StatusCodeDescriptionPayer": "PAYER/PAYEE PSP,REQUEST & RESPONSE HEADER VERSION MISMATCH", "StatusCodeDescriptionPayee": "PAYER/PAYEE PSP,REQUEST & RESPONSE HEADER VERSION MISMATCH", "DeclineType": "Technical"}, {"RawStatusCode": "UA5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1068", "StatusCodeDescriptionPayer": "PAYER/PAYEE PSP,VERSION/TAGS NOT SUPPORTED BY PSP/BANK", "StatusCodeDescriptionPayee": "PAYER/PAYEE PSP,VERSION/TAGS NOT SUPPORTED BY PSP/BANK", "DeclineType": "Technical"}, {"RawStatusCode": "UA7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1069", "StatusCodeDescriptionPayer": "REMITTER BANK,REQUEST & RESPONSE HEADER VERSION MISMATCH", "StatusCodeDescriptionPayee": "REMITTER BANK,REQUEST & RESPONSE HEADER VERSION MISMATCH", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UA8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1070", "StatusCodeDescriptionPayer": "REMITTER BANK,HEADER OR URL VERSION MISMATCHED", "StatusCodeDescriptionPayee": "REMITTER BANK,HEADER OR URL VERSION MISMATCHED", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UA9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1071", "StatusCodeDescriptionPayer": "REMITTER BANK,VERSION/TAGS SENT NOT SUPPORTED BY BANK", "StatusCodeDescriptionPayee": "REMITTER BANK,VERSION/TAGS SENT NOT SUPPORTED BY BANK", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UB1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1072", "StatusCodeDescriptionPayer": "BENIFICIARY BANK,REQUEST & RESPONSE HEADER VERSION MISMATCH", "StatusCodeDescriptionPayee": "BENIFICIARY BANK,REQUEST & RESPONSE HEADER VERSION MISMATCH", "DeclineType": "Technical"}, {"RawStatusCode": "UB2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1073", "StatusCodeDescriptionPayer": "BENIFICIARY BANK,HEADER OR URL VERSION MISMATCHED", "StatusCodeDescriptionPayee": "BENIFICIARY BANK,HEADER OR URL VERSION MISMATCHED", "DeclineType": "Technical"}, {"RawStatusCode": "UB3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1074", "StatusCodeDescriptionPayer": "BENIFICIARY BANK,VERSION/TAGS SENT NOT SUPPORTED BY BANK", "StatusCodeDescriptionPayee": "BENIFICIARY BANK,VERSION/TAGS SENT NOT SUPPORTED BY BANK", "DeclineType": "Technical"}, {"RawStatusCode": "UB9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1075", "StatusCodeDescriptionPayer": "REMITTER <PERSON><PERSON><PERSON> DOES NOT SUPPORT VERSION", "StatusCodeDescriptionPayee": "REMITTER <PERSON><PERSON><PERSON> DOES NOT SUPPORT VERSION", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UC2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1076", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "UM0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1077", "StatusCodeDescriptionPayer": "REQAUTHMAND<PERSON><PERSON> ACK NOT RECEIVED", "StatusCodeDescriptionPayee": "REQAUTHMAND<PERSON><PERSON> ACK NOT RECEIVED", "DeclineType": "Technical"}, {"RawStatusCode": "UM1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1078", "StatusCodeDescriptionPayer": "RESPAUTHMANDATE DECLINED BY PSP", "StatusCodeDescriptionPayee": "RESPAUTHMANDATE DECLINED BY PSP", "DeclineType": "Technical"}, {"RawStatusCode": "UM2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1079", "StatusCodeDescriptionPayer": "RESPAUTHMANDATE TIMEOUT", "StatusCodeDescriptionPayee": "RESPAUTHMANDATE TIMEOUT", "DeclineType": "Technical"}, {"RawStatusCode": "UM3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1080", "StatusCodeDescriptionPayer": "RESPAUTHMANDATE EXPIRED", "StatusCodeDescriptionPayee": "RESPAUTHMANDATE EXPIRED", "DeclineType": "Business"}, {"RawStatusCode": "UM4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1081", "StatusCodeDescriptionPayer": "REQAUTHMANDATE NEGATIVE ACK RECEIVED FROM PSP", "StatusCodeDescriptionPayee": "REQAUTHMANDATE NEGATIVE ACK RECEIVED FROM PSP", "DeclineType": "Technical"}, {"RawStatusCode": "UM5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1082", "StatusCodeDescriptionPayer": "RESPAUTHMANDATE NEGATIVE ACK SENT FROM UPI TO PSP", "StatusCodeDescriptionPayee": "RESPAUTHMANDATE NEGATIVE ACK SENT FROM UPI TO PSP", "DeclineType": "Technical"}, {"RawStatusCode": "UM6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1083", "StatusCodeDescriptionPayer": "ORIGINAL REQAUTHMANDATE NOT FOUND", "StatusCodeDescriptionPayee": "ORIGINAL REQAUTHMANDATE NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "UM7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1084", "StatusCodeDescriptionPayer": "REQMANDATE ACK NOT RECEIVED FROM REMITTER BANK", "StatusCodeDescriptionPayee": "REQMANDATE ACK NOT RECEIVED FROM REMITTER BANK", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UM8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1085", "StatusCodeDescriptionPayer": "RESPMANDATE DECLINED BY REMITTER BANK", "StatusCodeDescriptionPayee": "RESPMANDATE DECLINED BY REMITTER BANK", "DeclineType": "Technical"}, {"RawStatusCode": "UM9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1086", "StatusCodeDescriptionPayer": "RESPMANDATE TIMEOUT AT REMITTER END", "StatusCodeDescriptionPayee": "RESPMANDATE TIMEOUT AT REMITTER END", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UN0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1087", "StatusCodeDescriptionPayer": "REQMANDATE NEGATIVE ACK RECEIVED FROM REMITTER BANK", "StatusCodeDescriptionPayee": "REQMANDATE NEGATIVE ACK RECEIVED FROM REMITTER BANK", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UN1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1088", "StatusCodeDescriptionPayer": "RESPMANDATE NEGATIVE ACK SENT FROM UPI TO REMITTER BANK", "StatusCodeDescriptionPayee": "RESPMANDATE NEGATIVE ACK SENT FROM UPI TO REMITTER BANK", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UN2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1089", "StatusCodeDescriptionPayer": "ORIGINAL REQMANDATE NOT FOUND", "StatusCodeDescriptionPayee": "ORIGINAL REQMANDATE NOT FOUND", "DeclineType": "Technical"}, {"RawStatusCode": "UN3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1090", "StatusCodeDescriptionPayer": "RESPMANDATE ACK NOT RECEIVED FROM PAYER", "StatusCodeDescriptionPayee": "RESPMANDATE ACK NOT RECEIVED FROM PAYER", "DeclineType": "Technical"}, {"RawStatusCode": "UN4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1091", "StatusCodeDescriptionPayer": "REQMANDATECONFIRMA<PERSON>ON<PERSON>K NOT RECEIVED FROM PAYER", "StatusCodeDescriptionPayee": "REQMANDATECONFIRMA<PERSON>ON<PERSON>K NOT RECEIVED FROM PAYER", "DeclineType": "Technical"}, {"RawStatusCode": "UO0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1092", "StatusCodeDescriptionPayer": "REMITTER BANK NOT REGISTERED (MANDATE)", "StatusCodeDescriptionPayee": "REMITTER BANK NOT REGISTERED (MANDATE)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "UO1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1093", "StatusCodeDescriptionPayer": "BENEFICIARY BANK NOT REGISTERED (MANDATE)", "StatusCodeDescriptionPayee": "BENEFICIARY BANK NOT REGISTERED (MANDATE)", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "UP2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1094", "StatusCodeDescriptionPayer": "MANDATE AMOUNT CAP IS EXCEEDED", "StatusCodeDescriptionPayee": "MANDATE AMOUNT CAP IS EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "FL", "CustomerReversalCode": "*", "StatusCode": "UPI1095", "StatusCodeDescriptionPayer": "First Transaction Limit exceeded The user has crossed the Rs 5000 transaction limit for the first 24hrs post onboarding", "StatusCodeDescriptionPayee": "First Transaction Limit exceeded The user has crossed the Rs 5000 transaction limit for the first 24hrs post onboarding", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "FP", "CustomerReversalCode": "*", "StatusCode": "UPI1096", "StatusCodeDescriptionPayer": "Freeze Period for first time user Post 1st transaction approval, a 12 hr freeze period is initiated by the bank", "StatusCodeDescriptionPayee": "Freeze Period for first time user Post 1st transaction approval, a 12 hr freeze period is initiated by the bank", "DeclineType": "Business"}, {"RawStatusCode": "MP6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1097", "StatusCodeDescriptionPayer": "Remitter PSP app does not support Mandates Version 2.1", "StatusCodeDescriptionPayee": "Remitter PSP app does not support Mandates Version 2.1", "DeclineType": "Technical"}, {"RawStatusCode": "MP7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1098", "StatusCodeDescriptionPayer": "Beneficiary PSP app does not support Mandates Version 2.1", "StatusCodeDescriptionPayee": "Beneficiary PSP app does not support Mandates Version 2.1", "DeclineType": "Technical"}, {"RawStatusCode": "MP8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1099", "StatusCodeDescriptionPayer": "Remitter Bank does not support Mandates Version 2.1", "StatusCodeDescriptionPayee": "Remitter Bank does not support Mandates Version 2.1", "DeclineType": "Technical"}, {"RawStatusCode": "MP9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1100", "StatusCodeDescriptionPayer": "Benificiary Bank does not support Mandates Version 2.1", "StatusCodeDescriptionPayee": "Benificiary Bank does not support Mandates Version 2.1", "DeclineType": "Technical"}, {"RawStatusCode": "UO0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1101", "StatusCodeDescriptionPayer": "Remitter Bank not registered for Mandate", "StatusCodeDescriptionPayee": "Remitter Bank not registered for Mandate", "DeclineType": "Technical"}, {"RawStatusCode": "UO1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1102", "StatusCodeDescriptionPayer": "Beneficiary Bank not registered for Mandate", "StatusCodeDescriptionPayee": "Beneficiary Bank not registered for Mandate", "DeclineType": "Technical"}, {"RawStatusCode": "UO3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1103", "StatusCodeDescriptionPayer": "PSP does not support Mandates Version 2.1", "StatusCodeDescriptionPayee": "PSP does not support Mandates Version 2.1", "DeclineType": "Technical"}, {"RawStatusCode": "UO4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1104", "StatusCodeDescriptionPayer": "Did not receive confirmation from Payee", "StatusCodeDescriptionPayee": "Did not receive confirmation from Payee", "DeclineType": "Technical"}, {"RawStatusCode": "UO5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1105", "StatusCodeDescriptionPayer": "Did not receive confirmation from Payer", "StatusCodeDescriptionPayee": "Did not receive confirmation from Payer", "DeclineType": "Technical"}, {"RawStatusCode": "UO7", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1106", "StatusCodeDescriptionPayer": "Mandate OrgId or UMN address not matched with Payer VPA Address", "StatusCodeDescriptionPayee": "Mandate OrgId or UMN address not matched with Payer VPA Address", "DeclineType": "Technical"}, {"RawStatusCode": "UO8", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1107", "StatusCodeDescriptionPayer": "Mandate UMN address either not active or does not exist", "StatusCodeDescriptionPayee": "Mandate UMN address either not active or does not exist", "DeclineType": "Technical"}, {"RawStatusCode": "UO9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1108", "StatusCodeDescriptionPayer": "Mandate UMN address exists already", "StatusCodeDescriptionPayee": "Mandate UMN address exists already", "DeclineType": "Technical"}, {"RawStatusCode": "MX0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1109", "StatusCodeDescriptionPayer": "Mandate not supported by bank", "StatusCodeDescriptionPayee": "Mandate not supported by bank", "DeclineType": "Technical"}, {"RawStatusCode": "MX1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1110", "StatusCodeDescriptionPayer": "Mandate not supported by bank", "StatusCodeDescriptionPayee": "Mandate not supported by bank", "DeclineType": "Technical"}, {"RawStatusCode": "MX2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1111", "StatusCodeDescriptionPayer": "Mandate not supported by bank", "StatusCodeDescriptionPayee": "Mandate not supported by bank", "DeclineType": "Technical"}, {"RawStatusCode": "MX3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1112", "StatusCodeDescriptionPayer": "Mandate not supported by bank", "StatusCodeDescriptionPayee": "Mandate not supported by bank", "DeclineType": "Technical"}, {"RawStatusCode": "MX4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1113", "StatusCodeDescriptionPayer": "Mandate not supported by bank", "StatusCodeDescriptionPayee": "Mandate not supported by bank", "DeclineType": "Technical"}, {"RawStatusCode": "MX5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1114", "StatusCodeDescriptionPayer": "Mandate not supported by bank", "StatusCodeDescriptionPayee": "Mandate not supported by bank", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "PA", "CustomerReversalCode": "*", "StatusCode": "UPI1115", "StatusCodeDescriptionPayer": "Invalid PAN details", "StatusCodeDescriptionPayee": "Invalid PAN details", "DeclineType": "Technical"}, {"RawStatusCode": "UC1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1116", "StatusCodeDescriptionPayer": "No response for ReqAuthValCust", "StatusCodeDescriptionPayee": "No response for ReqAuthValCust", "DeclineType": "Technical"}, {"RawStatusCode": "A15", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1117", "StatusCodeDescriptionPayer": "No Ack for ReqValCust", "StatusCodeDescriptionPayee": "No Ack for ReqValCust", "DeclineType": "Technical"}, {"RawStatusCode": "UC3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1118", "StatusCodeDescriptionPayer": "<PERSON><PERSON> for ReqValCust", "StatusCodeDescriptionPayee": "<PERSON><PERSON> for ReqValCust", "DeclineType": "Technical"}, {"RawStatusCode": "UC4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1119", "StatusCodeDescriptionPayer": "<PERSON><PERSON> for ReqValCust", "StatusCodeDescriptionPayee": "<PERSON><PERSON> for ReqValCust", "DeclineType": "Technical"}, {"RawStatusCode": "UC5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1120", "StatusCodeDescriptionPayer": "No response for ReqValCust", "StatusCodeDescriptionPayee": "No response for ReqValCust", "DeclineType": "Technical"}, {"RawStatusCode": "U84", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1121", "StatusCodeDescriptionPayer": "BENEFICIARY BAN<PERSON> DEEMED CHECK DECLINE", "StatusCodeDescriptionPayee": "BENEFICIARY BAN<PERSON> DEEMED CHECK DECLINE", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "U86", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1122", "StatusCodeDescriptionPayer": "REMITTER BANK THROTTLING DECLINE", "StatusCodeDescriptionPayee": "REMITTER BANK THROTTLING DECLINE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U89", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1123", "StatusCodeDescriptionPayer": "BENEFIC<PERSON>RY BA<PERSON>K THROTTLING DECLINE", "StatusCodeDescriptionPayee": "BENEFIC<PERSON>RY BA<PERSON>K THROTTLING DECLINE", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "*", "CustomerStatusCode": "V3", "CustomerReversalCode": "*", "StatusCode": "UPI1124", "StatusCodeDescriptionPayer": "PIN <PERSON> is missing (txns > 2000)", "StatusCodeDescriptionPayee": "PIN <PERSON> is missing (txns > 2000)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "V4", "CustomerReversalCode": "*", "StatusCode": "UPI1125", "StatusCodeDescriptionPayer": "PIN Cred Block is missing (txns < 2000 and Seq No = 1)", "StatusCodeDescriptionPayee": "PIN Cred Block is missing (txns < 2000 and Seq No = 1)", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "NU", "CustomerReversalCode": "*", "StatusCode": "UPI1126", "StatusCodeDescriptionPayer": "Unable to Notify the Customer", "StatusCodeDescriptionPayee": "Unable to Notify the Customer", "DeclineType": "Business"}, {"RawStatusCode": "UG1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1127", "StatusCodeDescriptionPayer": "Response Activation TimeOut", "StatusCodeDescriptionPayee": "Response Activation TimeOut", "DeclineType": "Technical"}, {"RawStatusCode": "UG3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1128", "StatusCodeDescriptionPayer": "Response ValQR TimeOut", "StatusCodeDescriptionPayee": "Response ValQR TimeOut", "DeclineType": "Technical"}, {"RawStatusCode": "UG5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1129", "StatusCodeDescriptionPayer": "FOREX Error in ValQR", "StatusCodeDescriptionPayee": "FOREX Error in ValQR", "DeclineType": "Technical"}, {"RawStatusCode": "UG6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1130", "StatusCodeDescriptionPayer": "QR Payload not found", "StatusCodeDescriptionPayee": "QR Payload not found", "DeclineType": "Technical"}, {"RawStatusCode": "X10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1131", "StatusCodeDescriptionPayer": "TXN ORGTXNID/ORGRRN/UMNEXECTS ALLOWED ONLY IF TXN TYPE IS MANDATENOTIFICATION", "StatusCodeDescriptionPayee": "TXN ORGTXNID/ORGRRN/UMNEXECTS ALLOWED ONLY IF TXN TYPE IS MANDATENOTIFICATION", "DeclineType": "Technical"}, {"RawStatusCode": "R26", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1132", "StatusCodeDescriptionPayer": "TXN ORGTXNID/ORGRRN/UMNEXECTS ALLOWED ONLY IF TXN TYPE IS MANDATENOTIFICATION", "StatusCodeDescriptionPayee": "TXN ORGTXNID/ORGRRN/UMNEXECTS ALLOWED ONLY IF TXN TYPE IS MANDATENOTIFICATION", "DeclineType": "Technical"}, {"RawStatusCode": "Q13", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1133", "StatusCodeDescriptionPayer": "PAYEE AMOUNT IS NOT ALLOWED ONLY IF TXNTYPE IS NOT MANDATE NOTIFICATION", "StatusCodeDescriptionPayee": "PAYEE AMOUNT IS NOT ALLOWED ONLY IF TXNTYPE IS NOT MANDATE NOTIFICATION", "DeclineType": "Technical"}, {"RawStatusCode": "Q14", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1134", "StatusCodeDescriptionPayer": "PAYER AMOUNT IS NOT ALLOWED ONLY IF TXNTYPE IS VALCUST/MANDATENOTIFICATION", "StatusCodeDescriptionPayee": "PAYER AMOUNT IS NOT ALLOWED ONLY IF TXNTYPE IS VALCUST/MANDATENOTIFICATION", "DeclineType": "Technical"}, {"RawStatusCode": "T17", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1135", "StatusCodeDescriptionPayer": "TXN TYPE DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "TXN TYPE DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "VZ4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1136", "StatusCodeDescriptionPayer": "MANDATE RECURRENCE RULE TAG SHOULD NOT BE PRESENT - PAYEE PSP NOT CERTIFIED FOR 2 7", "StatusCodeDescriptionPayee": "MANDATE RECURRENCE RULE TAG SHOULD NOT BE PRESENT - PAYEE PSP NOT CERTIFIED FOR 2 7", "DeclineType": "Technical"}, {"RawStatusCode": "U90", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1137", "StatusCodeDescriptionPayer": "REMITTER BANK DEEMED HIGH RESPONSE TIME CHECK DECLINE", "StatusCodeDescriptionPayee": "REMITTER BANK DEEMED HIGH RESPONSE TIME CHECK DECLINE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U91", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1138", "StatusCodeDescriptionPayer": "BENEFICIARY BA<PERSON><PERSON> DEEMED HIGH RESPONSE TIME CHECK DECLINE", "StatusCodeDescriptionPayee": "BENEFICIARY BA<PERSON><PERSON> DEEMED HIGH RESPONSE TIME CHECK DECLINE", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "PM16", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1139", "StatusCodeDescriptionPayer": "ALREADY PROCESSED TRANSACTION", "StatusCodeDescriptionPayee": "ALREADY PROCESSED TRANSACTION", "DeclineType": "Technical"}, {"RawStatusCode": "Q3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1140", "StatusCodeDescriptionPayer": "Purpose code=14, Block Fund =Y ( Block fund must be N always for SI/ recurring mandate) ( PAYER)", "StatusCodeDescriptionPayee": "Purpose code=14, Block Fund =Y ( Block fund must be N always for SI/ recurring mandate) ( PAYER)", "DeclineType": "Technical"}, {"RawStatusCode": "Q4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1141", "StatusCodeDescriptionPayer": "Purpose Code=14, Revocable= N ( Revokable tag must always be Y for for SI/ recurring mandate) ( PAYER)", "StatusCodeDescriptionPayee": "Purpose Code=14, Revocable= N ( Revokable tag must always be Y for for SI/ recurring mandate) ( PAYER)", "DeclineType": "Technical"}, {"RawStatusCode": "V5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1142", "StatusCodeDescriptionPayer": "Purpose code=14, Block Fund =Y ( Block fund must be N always for SI/ recurring mandate) (Remitter)", "StatusCodeDescriptionPayee": "Purpose code=14, Block Fund =Y ( Block fund must be N always for SI/ recurring mandate) (Remitter)", "DeclineType": "Technical"}, {"RawStatusCode": "V6", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1143", "StatusCodeDescriptionPayer": "Purpose Code=14, Revocable= N ( Revokable tag must always be Y for for SI/ recurring mandate) ( Remitter)", "StatusCodeDescriptionPayee": "Purpose Code=14, Revocable= N ( Revokable tag must always be Y for for SI/ recurring mandate) ( Remitter)", "DeclineType": "Technical"}, {"RawStatusCode": "MB9", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1144", "StatusCodeDescriptionPayer": "MANDATE RECURRENCE PATTERN REVOKABLE=Y, ONLY Y IS ALLOWED IF THE PURPOSE CODE=14", "StatusCodeDescriptionPayee": "MANDATE RECURRENCE PATTERN REVOKABLE=Y, ONLY Y IS ALLOWED IF THE PURPOSE CODE=14", "DeclineType": "Technical"}, {"RawStatusCode": "G82", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1145", "StatusCodeDescriptionPayer": "PAYER REQ ID DETAILS NOT PRESENT", "StatusCodeDescriptionPayee": "PAYER REQ ID DETAILS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G83", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1146", "StatusCodeDescriptionPayer": "PAYER REQ ID DETAILS IDS NOT PRESENT", "StatusCodeDescriptionPayee": "PAYER REQ ID DETAILS IDS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G84", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1147", "StatusCodeDescriptionPayer": "REQ ID DETAILS ID NAME VALUE MUST BE PRESENT", "StatusCodeDescriptionPayee": "REQ ID DETAILS ID NAME VALUE MUST BE PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G85", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1148", "StatusCodeDescriptionPayer": "REQ ID DETAILS ID NAME VALUE MUST BE PRESENT MINLENGTH M,MAXLENGTH N", "StatusCodeDescriptionPayee": "REQ ID DETAILS ID NAME VALUE MUST BE PRESENT MINLENGTH M,MAXLENGTH N", "DeclineType": "Technical"}, {"RawStatusCode": "G86", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1149", "StatusCodeDescriptionPayer": "RESPIDDETATILS NOT PRESENT", "StatusCodeDescriptionPayee": "RESPIDDETATILS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G87", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1150", "StatusCodeDescriptionPayer": "RESP ID DETAILS ID NOT PRESENT", "StatusCodeDescriptionPayee": "RESP ID DETAILS ID NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G88", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1151", "StatusCodeDescriptionPayer": "RESP ID DETAILS ID DETAILS NOT PRESENT", "StatusCodeDescriptionPayee": "RESP ID DETAILS ID DETAILS NOT PRESENT", "DeclineType": "Technical"}, {"RawStatusCode": "G89", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1152", "StatusCodeDescriptionPayer": "PAYER RESP ID DETAILS ID NAME/VALUE MUST BE PRESENT AND VALID", "StatusCodeDescriptionPayee": "PAYER RESP ID DETAILS ID NAME/VALUE MUST BE PRESENT AND VALID", "DeclineType": "Technical"}, {"RawStatusCode": "G90", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1153", "StatusCodeDescriptionPayer": "ID DETAILS VALUE MUST BE PRESENT FOR DETAIL NAME", "StatusCodeDescriptionPayee": "ID DETAILS VALUE MUST BE PRESENT FOR DETAIL NAME", "DeclineType": "Technical"}, {"RawStatusCode": "G91", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1154", "StatusCodeDescriptionPayer": "ID DETAIL VALUE INCORRECT FORMAT FOR DETAIL NAME", "StatusCodeDescriptionPayee": "ID DETAIL VALUE INCORRECT FORMAT FOR DETAIL NAME", "DeclineType": "Technical"}, {"RawStatusCode": "G94", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1157", "StatusCodeDescriptionPayer": "PAYER PAN DIFFERS FROM ORGIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER PAN DIFFERS FROM ORGIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "G95", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1158", "StatusCodeDescriptionPayer": "PAYER UMN DIFFERS FROM ORIGINAL REQUEST", "StatusCodeDescriptionPayee": "PAYER UMN DIFFERS FROM ORIGINAL REQUEST", "DeclineType": "Technical"}, {"RawStatusCode": "PV1", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1159", "StatusCodeDescriptionPayer": "PSP DOES NOT SUPPORT VERSION VALCUST 2.4", "StatusCodeDescriptionPayee": "PSP DOES NOT SUPPORT VERSION VALCUST 2.4", "DeclineType": "Technical"}, {"RawStatusCode": "PV2", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1160", "StatusCodeDescriptionPayer": "PAYER PSP DOES NOT SUPPORT VERSION VALCUST 2.4", "StatusCodeDescriptionPayee": "PAYER PSP DOES NOT SUPPORT VERSION VALCUST 2.4", "DeclineType": "Technical"}, {"RawStatusCode": "PV3", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1161", "StatusCodeDescriptionPayer": "PAYEE PSP DOES NOT SUPPORT VERSION VALCUST 2.4", "StatusCodeDescriptionPayee": "PAYEE PSP DOES NOT SUPPORT VERSION VALCUST 2.5", "DeclineType": "Technical"}, {"RawStatusCode": "PV4", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1162", "StatusCodeDescriptionPayer": "REMIT PSP DOES NOT SUPPORT VERSION VALCUST 2.4", "StatusCodeDescriptionPayee": "REMIT PSP DOES NOT SUPPORT VERSION VALCUST 2.4", "DeclineType": "Technical"}, {"RawStatusCode": "PV5", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1163", "StatusCodeDescriptionPayer": "BENEFICIARY PSP DOES NOT SUPPORT VERSION VALCUST 2.4", "StatusCodeDescriptionPayee": "BENEFICIARY PSP DOES NOT SUPPORT VERSION VALCUST 2.4", "DeclineType": "Technical", "BankServerDown": "BENEFICIARY"}, {"RawStatusCode": "*", "CustomerStatusCode": "SD", "CustomerReversalCode": "*", "StatusCode": "UPI1164", "StatusCodeDescriptionPayer": "Service disable on UPI/ Customer is not active", "StatusCodeDescriptionPayee": "Service disable on UPI/ Customer is not active", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "IV", "CustomerReversalCode": "*", "StatusCode": "UPI1165", "StatusCodeDescriptionPayer": "Invalid verification token", "StatusCodeDescriptionPayee": "Invalid verification token", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "PE", "CustomerReversalCode": "*", "StatusCode": "UPI1166", "StatusCodeDescriptionPayer": "Payment validity expired", "StatusCodeDescriptionPayee": "Payment validity expired", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "RD", "CustomerReversalCode": "*", "StatusCode": "UPI1167", "StatusCodeDescriptionPayer": "Request Decline by the bank", "StatusCodeDescriptionPayee": "Request Decline by the bank", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "IN", "CustomerReversalCode": "*", "StatusCode": "UPI1168", "StatusCodeDescriptionPayer": "International Service not activated/disabled", "StatusCodeDescriptionPayee": "International Service not activated/disabled", "DeclineType": "Technical"}, {"RawStatusCode": "*", "CustomerStatusCode": "CN", "CustomerReversalCode": "*", "StatusCode": "UPI1169", "StatusCodeDescriptionPayer": "Country/ Currency not supported", "StatusCodeDescriptionPayee": "Country/ Currency not supported", "DeclineType": "Technical"}, {"RawStatusCode": "U92", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1170", "StatusCodeDescriptionPayer": "Payer PSP not available", "StatusCodeDescriptionPayee": "Payer PSP not available", "DeclineType": "Technical"}, {"RawStatusCode": "U93", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1171", "StatusCodeDescriptionPayer": "Payee PSP not available", "StatusCodeDescriptionPayee": "Payee PSP not available", "DeclineType": "Technical"}, {"RawStatusCode": "U94", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1172", "StatusCodeDescriptionPayer": "Payee PSP throttling Decline", "StatusCodeDescriptionPayee": "Payee PSP throttling Decline", "DeclineType": "Technical"}, {"RawStatusCode": "D05", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1173", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "D06", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1174", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "D07", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1175", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "D08", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1176", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "D09", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1177", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "D10", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1178", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "D11", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1179", "StatusCodeDescriptionPayer": "Technical Error", "StatusCodeDescriptionPayee": "Technical Error", "DeclineType": "Technical"}, {"RawStatusCode": "U30", "CustomerStatusCode": "AM", "CustomerReversalCode": "*", "StatusCode": "UPI1184", "StatusCodeDescriptionPayer": "MPIN NOT SET BY CUSTOMER", "StatusCodeDescriptionPayee": "MPIN NOT SET BY CUSTOMER", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "XN", "CustomerReversalCode": "*", "StatusCode": "UPI1185", "StatusCodeDescriptionPayer": "NO CARD RECORD (REMITTER)", "StatusCodeDescriptionPayee": "NO CARD RECORD (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "Z9", "CustomerReversalCode": "*", "StatusCode": "UPI1186", "StatusCodeDescriptionPayer": "INSUFFICIENT FUNDS IN CUSTOMER (REMITTER) ACCOUNT", "StatusCodeDescriptionPayee": "INSUFFICIENT FUNDS IN CUSTOMER (REMITTER) ACCOUNT", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "Z6", "CustomerReversalCode": "*", "StatusCode": "UPI1187", "StatusCodeDescriptionPayer": "NUMBER OF PIN TRIES EXCEEDED", "StatusCodeDescriptionPayee": "NUMBER OF PIN TRIES EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "ZM", "CustomerReversalCode": "*", "StatusCode": "UPI1188", "StatusCodeDescriptionPayer": "INVALID / INCORRECT MPIN", "StatusCodeDescriptionPayee": "INVALID / INCORRECT MPIN", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "XB", "CustomerReversalCode": "*", "StatusCode": "UPI1189", "StatusCodeDescriptionPayer": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (REMITTER)", "StatusCodeDescriptionPayee": "INVALID TRANSACTION OR IF MEMBER IS NOT ABLE TO FIND ANY APPROPRIATE RESPONSE CODE (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "YE", "CustomerReversalCode": "*", "StatusCode": "UPI1196", "StatusCodeDescriptionPayer": "REMITTING ACCOUNT BLOCKED/FROZEN", "StatusCodeDescriptionPayee": "REMITTING ACCOUNT BLOCKED/FROZEN", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "FP", "CustomerReversalCode": "*", "StatusCode": "UPI1197", "StatusCodeDescriptionPayer": "FREEZE PERIOD FOR FIRST TIME USER", "StatusCodeDescriptionPayee": "FREEZE PERIOD FOR FIRST TIME USER", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "UT", "CustomerReversalCode": "*", "StatusCode": "UPI205", "StatusCodeDescriptionPayer": "REMITTER/ISSUER UNAVAILABLE (TIMEOUT)", "StatusCodeDescriptionPayee": "REMITTER/ISSUER UNAVAILABLE (TIMEOUT)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U30", "CustomerStatusCode": "FL", "CustomerReversalCode": "*", "StatusCode": "UPI1226", "StatusCodeDescriptionPayer": "FIRST TRANSACTION LIMIT EXCEEDED", "StatusCodeDescriptionPayee": "FIRST TRANSACTION LIMIT EXCEEDED", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "IE", "CustomerReversalCode": "*", "StatusCode": "UPI1227", "StatusCodeDescriptionPayer": "ADEQUAT<PERSON> FUNDS NOT A<PERSON><PERSON><PERSON><PERSON> IN THE ACCOUNT BECAUSE FUNDS HAVE BEEN BLOCKED FOR MANDATE", "StatusCodeDescriptionPayee": "ADEQUAT<PERSON> FUNDS NOT A<PERSON><PERSON><PERSON><PERSON> IN THE ACCOUNT BECAUSE FUNDS HAVE BEEN BLOCKED FOR MANDATE", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "Z7", "CustomerReversalCode": "*", "StatusCode": "UPI1228", "StatusCodeDescriptionPayer": "TRANSACTION FREQUENCY LIMIT EXCEEDED AS SET BY REMITTING MEMBER", "StatusCodeDescriptionPayee": "TRANSACTION FREQUENCY LIMIT EXCEEDED AS SET BY REMITTING MEMBER", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "Z8", "CustomerReversalCode": "*", "StatusCode": "UPI1229", "StatusCodeDescriptionPayer": "PER TRANSACTION LIMIT EXCEEDED AS SET BY REMITTING MEMBER ", "StatusCodeDescriptionPayee": "PER TRANSACTION LIMIT EXCEEDED AS SET BY REMITTING MEMBER ", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "ZB", "CustomerReversalCode": "*", "StatusCode": "UPI1230", "StatusCodeDescriptionPayer": "INVALID MERCHANT (PAYEE PSP)", "StatusCodeDescriptionPayee": "INVALID MERCHANT (PAYEE PSP)", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "ZX", "CustomerReversalCode": "*", "StatusCode": "UPI1231", "StatusCodeDescriptionPayer": "INACTIVE OR DORMANT ACCOUNT (REMITTER)", "StatusCodeDescriptionPayee": "INACTIVE OR DORMANT ACCOUNT (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "K1", "CustomerReversalCode": "*", "StatusCode": "UPI1232", "StatusCodeDescriptionPayer": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY REMITTER", "StatusCodeDescriptionPayee": "SUSPECTED FRAUD, DECLINE / TRANSACTIONS DECLINED BASED ON RISK SCORE BY REMITTER", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "ZU", "CustomerReversalCode": "*", "StatusCode": "UPI1233", "StatusCodeDescriptionPayer": "LIMIT EXCEEDED FOR REMITTING BANK/ISSUING BANK", "StatusCodeDescriptionPayee": "LIMIT EXCEEDED FOR REMITTING BANK/ISSUING BANK", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "XT", "CustomerReversalCode": "*", "StatusCode": "UPI1234", "StatusCodeDescriptionPayer": "CUT-OFF IS IN PROCESS (REMITTER)", "StatusCodeDescriptionPayee": "CUT-OFF IS IN PROCESS (REMITTER)", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U30", "CustomerStatusCode": "XH", "CustomerReversalCode": "*", "StatusCode": "UPI1235", "StatusCodeDescriptionPayer": "ACCOUNT DOES NOT EXIST (REMITTER) ", "StatusCodeDescriptionPayee": "ACCOUNT DOES NOT EXIST (REMITTER) ", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "XY", "CustomerReversalCode": "*", "StatusCode": "UPI1236", "StatusCodeDescriptionPayer": "REMITTER CBS OFFLINE", "StatusCodeDescriptionPayee": "REMITTER CBS OFFLINE", "DeclineType": "Technical", "BankServerDown": "REMITTER"}, {"RawStatusCode": "U30", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI784", "StatusCodeDescriptionPayer": "DEBIT HAS BEEN FAILED", "StatusCodeDescriptionPayee": "DEBIT HAS BEEN FAILED", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "XV", "CustomerReversalCode": "*", "StatusCode": "UPI1271", "StatusCodeDescriptionPayer": "TRANSACTION CANNOT BE COMPLETED. COMPLIANCE VIOLATION (REMITTER)", "StatusCodeDescriptionPayee": "TRANSACTION CANNOT BE COMPLETED. COMPLIANCE VIOLATION (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "XP", "CustomerReversalCode": "*", "StatusCode": "UPI1272", "StatusCodeDescriptionPayer": "TRANSACTION NOT PERMITTED TO CARDHOLDER (REMITTER)", "StatusCodeDescriptionPayee": "TRANSACTION NOT PERMITTED TO CARDHOLDER (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "U30", "CustomerStatusCode": "XV", "CustomerReversalCode": "*", "StatusCode": "UPI1273", "StatusCodeDescriptionPayer": "TRANSACTION CANNOT BE COMPLETED. COMPLIANCE VIOLATION (REMITTER)", "StatusCodeDescriptionPayee": "TRANSACTION CANNOT BE COMPLETED. COMPLIANCE VIOLATION (REMITTER)", "DeclineType": "Business"}, {"RawStatusCode": "S93", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1282", "StatusCodeDescriptionPayer": "PAYEE_PSP_THROTTLE_DECLINE_OUTGOING_COUNT", "StatusCodeDescriptionPayee": "PAYEE_PSP_THROTTLE_DECLINE_OUTGOING_COUNT", "DeclineType": "Technical"}, {"RawStatusCode": "S94", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI1281", "StatusCodeDescriptionPayer": "PAYEE_PSP_THROTTLE_DECLINE_RESPONSE_TIME", "StatusCodeDescriptionPayee": "PAYEE_PSP_THROTTLE_DECLINE_RESPONSE_TIME", "DeclineType": "Technical"}, {"RawStatusCode": "0", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI_SUCCESS", "StatusCodeDescriptionPayer": "Success", "StatusCodeDescriptionPayee": "Success", "DeclineType": "Business"}, {"RawStatusCode": "00", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI_SUCCESS", "StatusCodeDescriptionPayer": "Success", "StatusCodeDescriptionPayee": "Success", "DeclineType": "Business"}, {"RawStatusCode": "*", "CustomerStatusCode": "*", "CustomerReversalCode": "*", "StatusCode": "UPI007", "StatusCodeDescriptionPayer": "Unknown", "StatusCodeDescriptionPayee": "Unknown", "DeclineType": "Technical", "BankServerDown": "REMITTER"}]}