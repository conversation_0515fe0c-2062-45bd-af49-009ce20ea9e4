package servergenhook

import (
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/grpc"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"

	"github.com/epifi/gamma/api/vendors/depositresponsestatusmapping"
	vendorfedral "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/gamma/api/vendors/paystatusmapping"
	cardResponseMapping "github.com/epifi/gamma/api/vendors/responsemapping/card"
	"github.com/epifi/gamma/vendornotification/config"
)

func InitVendorNotificationServer(s *grpc.Server) (func(), error) {
	cleanupFn := func() {}

	conf, err := config.Load()
	if err != nil {
		return cleanupFn, err
	}

	err = paystatusmapping.LoadPayStatusCodes(conf.PayFundTransferStatusCodeJson, conf.PayUpiStatusCodeJson, conf.EnachTransactionStatusCodeJson)
	if err != nil {
		return cleanupFn, fmt.Errorf("error loading pay status code. %w", err)
	}

	err = depositresponsestatusmapping.LoadDepositResponseStatusCodes(conf.DepositResponseStatusCodeFilePath)
	if err != nil {
		return cleanupFn, fmt.Errorf("error loading deposit response status code. %w", err)
	}

	err = vendorfedral.InitNotificationParserRules()
	if err != nil {
		return cleanupFn, fmt.Errorf("failed to load notification parser facts. %w", err)
	}

	err = vendorfedral.InitLoanNotificationParserRules()
	if err != nil {
		logger.Panic("failed to load loan notification parser facts", zap.Error(err))
	}

	err = cardResponseMapping.LoadCardResponseStatusCodes(conf.CardResponseStatusCodeFilePath)
	if err != nil {
		logger.Panic("error loading card response status code", zap.Error(err))
	}

	servergen_wire.PopulateAuthDescriptors(s, &epifigrpc.AuthDescriptorLoadOptions{
		SkipAuthEnforcement: true,
	})

	return cleanupFn, nil
}
