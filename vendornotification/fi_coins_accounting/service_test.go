package fi_coins_accounting

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/logger"

	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	"github.com/epifi/gamma/api/casper/external_vendor_redemption/mocks"
	"github.com/epifi/gamma/api/user"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	mockVendorMapping "github.com/epifi/gamma/api/vendormapping/mocks"
	fiCoinsAccVendorPb "github.com/epifi/gamma/api/vendors/fi_coins_accounting"

	"github.com/epifi/gamma/vendornotification/config/genconf"
)

const (
	validAuthToken   = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMTVhYWExMWEtZDg0ZC00NjhmLThhMDAtYWEyZjg5OWI3MTY5In0.6wmeFfeGj6lLqziw8Q5pHPVucoaNrlAPOlRzp3SDYWs"
	invalidAuthToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiIn0.PkVElUzJ1dIV1X0usrK56fur9U0LiZ5fcV2eBLWKZKg"
	validAuthToken2  = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************.RUwHYtx5J_AqQ2E8StgeKeLkuia8BLFO1Od_HO6bDj0"
)

// nolint: govet
func TestService_GetUserFiCoinsBalance(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	a := require.New(t)
	gconf, err := genconf.Load()
	a.NoError(err)
	ctx := appendAuthTokenInContext(validAuthToken)
	ctx2 := appendAuthTokenInContext(invalidAuthToken)
	type args struct {
		ctx     context.Context
		request *fiCoinsAccVendorPb.GetUserFiCoinsBalanceRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient)
		want           *fiCoinsAccVendorPb.GetUserFiCoinsBalanceResponse
		wantErr        bool
	}{
		{
			name: "success: Get fi coins balance",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserFiCoinsBalanceRequest{
					UserId: "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetInputIdByVendor(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetInputIdByVendorResponse{
					Status:  rpc.StatusOk(),
					InputId: "ACnyH99au1SruMfst3S65fwQ230608==",
				}, nil)
				externalVendorRedemptionClient.EXPECT().GetUserFiCoinsBalance(gomock.Any(), gomock.Any()).Return(&evrPb.GetUserFiCoinsBalanceResponse{
					Status:  rpc.StatusOk(),
					Balance: 1000,
				}, nil)
			},
			want: &fiCoinsAccVendorPb.GetUserFiCoinsBalanceResponse{
				UserId:         "15aaa11a-d84d-468f-8a00-aa2f899b7169",
				FiCoinsBalance: 1000,
			},
		},
		{
			name: "failure: no user id in request",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserFiCoinsBalanceRequest{
					UserId: "",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: no vendor in request",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserFiCoinsBalanceRequest{
					UserId: "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor: "",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: invalid user id in request",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserFiCoinsBalanceRequest{
					UserId: "123-invalid",
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: invalid vendor in request",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserFiCoinsBalanceRequest{
					UserId: "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor: "DPANDA-v2",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: no auth token",
			args: args{
				ctx: context.Background(),
				request: &fiCoinsAccVendorPb.GetUserFiCoinsBalanceRequest{
					UserId: "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: invalid auth token",
			args: args{
				ctx: ctx2,
				request: &fiCoinsAccVendorPb.GetUserFiCoinsBalanceRequest{
					UserId: "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor: "DPANDA",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockVendorMappingClient := mockVendorMapping.NewMockVendorMappingServiceClient(ctr)
			mockExternalVendorRedemptionClient := mocks.NewMockExternalVendorRedemptionServiceClient(ctr)
			s := &Service{
				conf:                           gconf,
				vendorMappingClient:            mockVendorMappingClient,
				externalVendorRedemptionClient: mockExternalVendorRedemptionClient,
			}
			tt.setupMockCalls(mockExternalVendorRedemptionClient, mockVendorMappingClient)
			got, err := s.GetUserFiCoinsBalance(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserFiCoinsBalance error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("GetUserFiCoinsBalance() diff = %s", diff)
			}
		})
	}
}

// nolint: govet,goimports
func TestService_TransactFiCoins(t *testing.T) {
	logger.Init("test")
	a := require.New(t)
	gconf, err := genconf.Load()
	a.NoError(err)
	ctx := appendAuthTokenInContext(validAuthToken)
	ctx2 := appendAuthTokenInContext(invalidAuthToken)
	type args struct {
		ctx     context.Context
		request *fiCoinsAccVendorPb.TransactFiCoinsRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient)
		want           *fiCoinsAccVendorPb.TransactFiCoinsResponse
		wantErr        bool
	}{
		{
			name: "failure: empty user id",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "",
					Vendor:       "DPANDA",
					TxnType:      "DEBIT",
					TxnCategory:  "PURCHASE",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: invalid vendor",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "DPANDA-FI",
					TxnType:      "DEBIT",
					TxnCategory:  "PURCHASE",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: invalid category",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "POSHVINE",
					TxnType:      "DEBIT",
					TxnCategory:  "PURCHASE",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category:     "",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: invalid txn type",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "DPANDA",
					TxnType:      "DEBITING",
					TxnCategory:  "PURCHASE",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: empty ref id",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "DPANDA",
					TxnType:      "DEBIT",
					TxnCategory:  "PURCHASE",
					FiCoinsUnits: 1000,
					VendorRefId:  "",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: empty vendor order id for vendor Poshvine",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "POSHVINE",
					TxnType:      "DEBIT",
					TxnCategory:  "PURCHASE",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category:     "GIFT_CARDS",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetInputIdByVendor(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetInputIdByVendorResponse{
					Status:  rpc.StatusOk(),
					InputId: "ACnyH99au1SruMfst3S65fwQ230608==",
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "success: debit txn, first time call, txn completed",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "DPANDA",
					TxnType:      "DEBIT",
					TxnCategory:  "PURCHASE",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetInputIdByVendor(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetInputIdByVendorResponse{
					Status:  rpc.StatusOk(),
					InputId: "ACnyH99au1SruMfst3S65fwQ230608==",
				}, nil)
				externalVendorRedemptionClient.EXPECT().TransactFiCoins(gomock.Any(), gomock.Any()).Return(&evrPb.TransactFiCoinsResponse{
					Status:                rpc.StatusOk(),
					ProcessingRefId:       "2aec491c-85f9-4ef7-b280-17bf725a01d2",
					TxnStatus:             "COMPLETED",
					UpdatedFiCoinsBalance: 1000,
					SubStatus:             "",
				}, nil)
			},
			want: &fiCoinsAccVendorPb.TransactFiCoinsResponse{
				UserId:                "15aaa11a-d84d-468f-8a00-aa2f899b7169",
				TxnId:                 "2aec491c-85f9-4ef7-b280-17bf725a01d2",
				UpdatedFiCoinsBalance: 1000,
				Status:                "COMPLETED",
				SubStatus:             "",
			},
		},
		{
			name: "success: debit txn, first time call, txn pending",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "DPANDA",
					TxnType:      "DEBIT",
					TxnCategory:  "PURCHASE",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetInputIdByVendor(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetInputIdByVendorResponse{
					Status:  rpc.StatusOk(),
					InputId: "ACnyH99au1SruMfst3S65fwQ230608==",
				}, nil)
				externalVendorRedemptionClient.EXPECT().TransactFiCoins(gomock.Any(), gomock.Any()).Return(&evrPb.TransactFiCoinsResponse{
					Status:                rpc.StatusOk(),
					ProcessingRefId:       "",
					TxnStatus:             "PENDING",
					UpdatedFiCoinsBalance: 1000,
					SubStatus:             "",
				}, nil)
			},
			want: &fiCoinsAccVendorPb.TransactFiCoinsResponse{
				UserId:                "15aaa11a-d84d-468f-8a00-aa2f899b7169",
				TxnId:                 "",
				UpdatedFiCoinsBalance: 1000,
				Status:                "PENDING",
				SubStatus:             "",
			},
		},
		{
			name: "success: debit txn, txn completed, but balance rpc throws error",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "DPANDA",
					TxnType:      "DEBIT",
					TxnCategory:  "PURCHASE",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetInputIdByVendor(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetInputIdByVendorResponse{
					Status:  rpc.StatusOk(),
					InputId: "ACnyH99au1SruMfst3S65fwQ230608==",
				}, nil)
				externalVendorRedemptionClient.EXPECT().TransactFiCoins(gomock.Any(), gomock.Any()).Return(&evrPb.TransactFiCoinsResponse{
					Status:                rpc.StatusOk(),
					ProcessingRefId:       "2aec491c-85f9-4ef7-b280-17bf725a01d2",
					TxnStatus:             "COMPLETED",
					UpdatedFiCoinsBalance: 0,
					SubStatus:             "error while fetching available balance",
				}, nil)
			},
			want: &fiCoinsAccVendorPb.TransactFiCoinsResponse{
				UserId:                "15aaa11a-d84d-468f-8a00-aa2f899b7169",
				TxnId:                 "2aec491c-85f9-4ef7-b280-17bf725a01d2",
				UpdatedFiCoinsBalance: 0,
				Status:                "COMPLETED",
				SubStatus:             "error while fetching available balance",
			},
		},
		{
			name: "success: failed debit txn, txn not performed",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "DPANDA",
					TxnType:      "DEBIT",
					TxnCategory:  "PURCHASE",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetInputIdByVendor(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetInputIdByVendorResponse{
					Status:  rpc.StatusOk(),
					InputId: "ACnyH99au1SruMfst3S65fwQ230608==",
				}, nil)
				externalVendorRedemptionClient.EXPECT().TransactFiCoins(gomock.Any(), gomock.Any()).Return(&evrPb.TransactFiCoinsResponse{
					Status:                rpc.StatusInternalWithDebugMsg("txn call not successful"),
					TxnStatus:             "FAILED",
					UpdatedFiCoinsBalance: 0,
					SubStatus:             "txn call not successful",
				}, nil)
			},
			want: &fiCoinsAccVendorPb.TransactFiCoinsResponse{
				UserId:    "15aaa11a-d84d-468f-8a00-aa2f899b7169",
				Status:    "FAILED",
				SubStatus: "txn call not successful",
			},
		},
		{
			name: "success: credit txn, first time call, txn completed",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "DPANDA",
					TxnType:      "CREDIT",
					TxnCategory:  "REFUND",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockVendorMappingClient.EXPECT().GetInputIdByVendor(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetInputIdByVendorResponse{
					Status:  rpc.StatusOk(),
					InputId: "ACnyH99au1SruMfst3S65fwQ230608==",
				}, nil)
				externalVendorRedemptionClient.EXPECT().TransactFiCoins(gomock.Any(), gomock.Any()).Return(&evrPb.TransactFiCoinsResponse{
					Status:                rpc.StatusOk(),
					ProcessingRefId:       "2aec491c-85f9-4ef7-b280-17bf725a01d2",
					TxnStatus:             "COMPLETED",
					UpdatedFiCoinsBalance: 2000,
					SubStatus:             "",
				}, nil)
			},
			want: &fiCoinsAccVendorPb.TransactFiCoinsResponse{
				UserId:                "15aaa11a-d84d-468f-8a00-aa2f899b7169",
				TxnId:                 "2aec491c-85f9-4ef7-b280-17bf725a01d2",
				UpdatedFiCoinsBalance: 2000,
				Status:                "COMPLETED",
				SubStatus:             "",
			},
		},
		{
			name: "failure: no auth token",
			args: args{
				ctx: context.Background(),
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "DPANDA",
					TxnType:      "CREDIT",
					TxnCategory:  "REFUND",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: no payload in token",
			args: args{
				ctx: ctx2,
				request: &fiCoinsAccVendorPb.TransactFiCoinsRequest{
					UserId:       "15aaa11a-d84d-468f-8a00-aa2f899b7169",
					Vendor:       "DPANDA",
					TxnType:      "CREDIT",
					TxnCategory:  "REFUND",
					FiCoinsUnits: 1000,
					VendorRefId:  "f82b918d-991d-4934-a48b-d33db56830ba",
					Category: "ECOM",
				},
			},
			setupMockCalls: func(externalVendorRedemptionClient *mocks.MockExternalVendorRedemptionServiceClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockVendorMappingClient := mockVendorMapping.NewMockVendorMappingServiceClient(ctr)
			mockExternalVendorRedemptionClient := mocks.NewMockExternalVendorRedemptionServiceClient(ctr)
			s := &Service{
				conf:                           gconf,
				vendorMappingClient:            mockVendorMappingClient,
				externalVendorRedemptionClient: mockExternalVendorRedemptionClient,
			}
			tt.setupMockCalls(mockExternalVendorRedemptionClient, mockVendorMappingClient)
			got, err := s.TransactFiCoins(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("TransactFiCoins error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("TransactFiCoins() diff = %s", diff)
			}
		})
	}
}

// nolint: govet
func TestService_GetUserIdFromPhoneNumber(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	a := require.New(t)
	gconf, err := genconf.Load()
	a.NoError(err)
	ctx := appendAuthTokenInContext(validAuthToken2)
	ctx2 := appendAuthTokenInContext(invalidAuthToken)
	type args struct {
		ctx     context.Context
		request *fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient)
		want           *fiCoinsAccVendorPb.GetUserIdFromPhoneNumberResponse
		wantErr        bool
	}{
		{
			name: "success: Get user id from phone number",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest{
					PhoneNumber: "1234567890",
					Vendor:      "POSHVINE",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&user.GetUserResponse{
					User: &user.User{
						ActorId: "actor-id",
					},
					Status:     rpc.StatusOk(),
					UserStatus: 0,
				}, nil)
				mockVendorMappingClient.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetBEMappingByIdResponse{
					Status:     rpc.StatusOk(),
					DpandaId:   "dpanda-id-1",
					PoshvineId: "poshvine-id-2",
				}, nil)
			},
			want: &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberResponse{
				UserId: "poshvine-id-2",
			},
		},
		{
			name: "failure: no phone number in request",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest{
					PhoneNumber: "",
					Vendor:      "POSHVINE",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: no vendor in request",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest{
					PhoneNumber: "1234567890",
					Vendor:      "v-poshvine",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: invalid phone number - insufficient digits",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest{
					PhoneNumber: "12345678900000",
					Vendor:      "POSHVINE",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: phone number record not found",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest{
					PhoneNumber: "1234567890",
					Vendor:      "POSHVINE",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&user.GetUserResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "failure: user client error",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest{
					PhoneNumber: "1234567890",
					Vendor:      "POSHVINE",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&user.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "failure: GetBEMappingById error",
			args: args{
				ctx: ctx,
				request: &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest{
					PhoneNumber: "1234567890",
					Vendor:      "POSHVINE",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
				mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&user.GetUserResponse{
					User: &user.User{
						ActorId: "actor-id",
					},
					Status:     rpc.StatusOk(),
					UserStatus: 0,
				}, nil)
				mockVendorMappingClient.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormappingPb.GetBEMappingByIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "failure: no auth token",
			args: args{
				ctx: context.Background(),
				request: &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest{
					PhoneNumber: "1234567890",
					Vendor:      "POSHVINE",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
		{
			name: "failure: invalid auth token",
			args: args{
				ctx: ctx2,
				request: &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest{
					PhoneNumber: "1234567890",
					Vendor:      "POSHVINE",
				},
			},
			setupMockCalls: func(mockUserClient *mockUser.MockUsersClient, mockVendorMappingClient *mockVendorMapping.MockVendorMappingServiceClient) {
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockVendorMappingClient := mockVendorMapping.NewMockVendorMappingServiceClient(ctr)
			mockUserClient := mockUser.NewMockUsersClient(ctr)
			s := &Service{
				conf:                gconf,
				vendorMappingClient: mockVendorMappingClient,
				userClient:          mockUserClient,
			}
			tt.setupMockCalls(mockUserClient, mockVendorMappingClient)
			got, err := s.GetUserIdFromPhoneNumber(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserIdFromPhoneNumber error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("GetUserIdFromPhoneNumber() diff = %s", diff)
			}
		})
	}
}

func appendAuthTokenInContext(token string) context.Context {
	md := make(map[string][]string)
	md["auth_token"] = []string{token}
	return metadata.NewIncomingContext(context.Background(), md)
}
