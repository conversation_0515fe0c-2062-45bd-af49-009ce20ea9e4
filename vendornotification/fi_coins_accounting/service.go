package fi_coins_accounting

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/gogo/status"
	"github.com/golang-jwt/jwt/v4"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	fireflyPb "github.com/epifi/gamma/api/firefly"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	fiCoinsAccVnPb "github.com/epifi/gamma/api/vendornotification/fi_coins_accounting"
	fiCoinsAccVendorPb "github.com/epifi/gamma/api/vendors/fi_coins_accounting"

	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/security"
)

// nolint:gosec
const (
	DpandaAuthApiKey   = "DpandaAuthApiKey"
	PoshvineAuthApiKey = "PoshvineAuthApiKey"
	RazorpayAuthApiKey = "RazorpayAuthApiKey"
)

type Service struct {
	conf                           *genconf.Config
	vendorMappingClient            vendormappingPb.VendorMappingServiceClient
	externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient
	userClient                     user.UsersClient
	fireflyClient                  fireflyPb.FireflyClient
}

func NewService(conf *genconf.Config, vendorMappingClient vendormappingPb.VendorMappingServiceClient, externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient, userClient user.UsersClient, fireflyClient fireflyPb.FireflyClient) *Service {
	return &Service{
		conf:                           conf,
		vendorMappingClient:            vendorMappingClient,
		externalVendorRedemptionClient: externalVendorRedemptionClient,
		userClient:                     userClient,
		fireflyClient:                  fireflyClient,
	}
}

var _ fiCoinsAccVnPb.FiCoinsAccountingServer = &Service{}

// GetUserFiCoinsBalance fetches user fi coins balance
// nolint: dupl
func (s *Service) GetUserFiCoinsBalance(ctx context.Context, request *fiCoinsAccVendorPb.GetUserFiCoinsBalanceRequest) (*fiCoinsAccVendorPb.GetUserFiCoinsBalanceResponse, error) {
	err1 := security.CheckWhiteList(ctx, s.conf.DPandaWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix())
	err2 := security.CheckWhiteList(ctx, s.conf.PoshVineWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix())
	if err1 != nil && err2 != nil {
		return nil, status.Error(codes.PermissionDenied, "Access forbidden")
	}

	// check if fi coins to fi points migration is active
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return nil, status.Error(codes.Unavailable, accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error())
	}

	if request.GetUserId() == "" {
		logger.Error(ctx, "user id missing in request", zap.String("userId", request.GetUserId()))
		return nil, status.Errorf(codes.InvalidArgument, "user id missing in request")
	}
	vendor, err := s.validateVendor(ctx, request.GetVendor())
	if err != nil {
		logger.Error(ctx, "vendor missing in request", zap.String(logger.VENDOR, request.GetVendor()))
		return nil, status.Errorf(codes.InvalidArgument, "vendor missing in request")
	}

	err = s.verifyJwtToken(ctx, vendor, request.GetUserId())
	if err != nil {
		logger.Error(ctx, "invalid jwt token", zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, "invalid jwt token, err: %v", err)
	}
	actorId, err := s.getActorIdFromVendorId(ctx, vendor, request.GetUserId())
	if err != nil {
		logger.Error(ctx, "error while fetching actor id from vendor's id", zap.String(logger.VENDOR, request.GetVendor()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, status.Errorf(codes.NotFound, "user not found")
		}
		return nil, status.Errorf(codes.Internal, "error while fetching user details")
	}

	// validate the card presence if the flag is enabled.
	if request.GetShouldValidateCardPresence() {
		if len(request.GetBin()) != 6 && len(request.GetBin()) != 8 {
			logger.Error(ctx, "unsupported bin number, bin should be 6 or 8 digits length", zap.String("bin", request.GetBin()), zap.String(logger.VENDOR, request.GetVendor()), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, status.Errorf(codes.InvalidArgument, "unsupported bin number, bin should be 6 or 8 digits length")
		}
		cardType := getCardTypeFromBinNumber(request.GetBin())
		if cardType == types.CardType_CARD_CATEGORY_UNSPECIFIED {
			logger.Error(ctx, "unsupported card bin number received", zap.String("bin", request.GetBin()), zap.String(logger.VENDOR, request.GetVendor()), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, status.Errorf(codes.InvalidArgument, "unsupported card bin number received")
		}

		cardPresenceStatus, rpcErr := s.validateCardPresence(ctx, actorId, request.GetLastFourDigits(), cardType)
		if rpcErr != nil {
			logger.Error(ctx, "error while validating the card presence", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.VENDOR, request.GetVendor()), zap.String("cardType", cardType.String()), zap.Error(rpcErr))
			return nil, status.Errorf(codes.Internal, "error while validating card presence")
		}

		if !cardPresenceStatus {
			logger.Error(ctx, "card presence status is false", zap.String(logger.VENDOR, request.GetVendor()), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, status.Errorf(codes.FailedPrecondition, "card presence status: false")
		}
	}

	balance, err := s.fetchAccountBalance(ctx, actorId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "error while fetching account balance")
	}
	return &fiCoinsAccVendorPb.GetUserFiCoinsBalanceResponse{
		UserId:         request.GetUserId(),
		FiCoinsBalance: int32(balance),
	}, nil
}

func (s *Service) fetchAccountBalance(ctx context.Context, actorId string) (int64, error) {
	resp, err := s.externalVendorRedemptionClient.GetUserFiCoinsBalance(ctx, &evrPb.GetUserFiCoinsBalanceRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching fi coins account balance", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return 0, fmt.Errorf("error while fetching fi coins account balance, err: %w", rpcErr)
	}
	return resp.GetBalance(), nil
}

// TransactFiCoins performs fi coins transaction
// nolint: funlen
func (s *Service) TransactFiCoins(ctx context.Context, request *fiCoinsAccVendorPb.TransactFiCoinsRequest) (*fiCoinsAccVendorPb.TransactFiCoinsResponse, error) {
	err1 := security.CheckWhiteList(ctx, s.conf.DPandaWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix())
	err2 := security.CheckWhiteList(ctx, s.conf.PoshVineWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix())
	if err1 != nil && err2 != nil {
		return nil, status.Error(codes.PermissionDenied, "Access forbidden")
	}

	// check if fi coins to fi points migration is active
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return nil, status.Error(codes.Unavailable, accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error())
	}

	if request.GetVendorRefId() == "" {
		logger.Error(ctx, "vendor ref id missing in request", zap.String(logger.VENDOR, request.GetVendor()), zap.String("vendorRefId", request.GetVendorRefId()))
		return nil, status.Error(codes.InvalidArgument, "vendor ref id missing in request")
	}
	vendor, err := s.validateVendor(ctx, request.GetVendor())
	if err != nil {
		logger.Error(ctx, "unsupported vendor provided", zap.String(logger.VENDOR, request.GetVendor()), zap.String("vendorRefId", request.GetVendorRefId()))
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	category, err := s.validateCategory(ctx, request.GetCategory())
	if err != nil {
		logger.Error(ctx, "unsupported category provided", zap.String("category", request.GetCategory()), zap.String(logger.VENDOR, request.GetVendor()), zap.String("vendorRefId", request.GetVendorRefId()))
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	txnType, err := s.validateTxnType(ctx, request.GetTxnType())
	if err != nil {
		logger.Error(ctx, "unsupported txn type provided", zap.String("txnType", request.GetTxnType()), zap.String(logger.VENDOR, request.GetVendor()), zap.String("vendorRefId", request.GetVendorRefId()))
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	txnCategory, err := s.validateTxnCategory(ctx, request.GetTxnCategory())
	if err != nil {
		logger.Error(ctx, "unsupported txn category provided", zap.String("txnCategory", request.GetTxnCategory()), zap.String(logger.VENDOR, request.GetVendor()), zap.String("vendorRefId", request.GetVendorRefId()))
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}

	// Only for purchase type we must need user_id validation.
	// For other cases like refunds, it is not necessary to get user id. We can fetch from vendor_ref_id.
	if txnCategory == evrPb.TxnCategory_PURCHASE {
		if request.GetUserId() == "" {
			logger.Error(ctx, "user id missing in request", zap.String("userId", request.GetUserId()), zap.String(logger.VENDOR, request.GetVendor()), zap.String("vendorRefId", request.GetVendorRefId()))
			return nil, status.Errorf(codes.InvalidArgument, "user id missing in request")
		}
	}

	err = s.verifyJwtToken(ctx, vendor, request.GetUserId())
	if err != nil {
		logger.Error(ctx, "invalid jwt token", zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, "invalid jwt token, err: %v", err)
	}

	actorId, err := s.getActorIdFromVendorId(ctx, vendor, request.GetUserId())
	if err != nil {
		logger.Error(ctx, "error while fetching actor id from vendor's id", zap.Error(err), zap.String(logger.VENDOR, request.GetVendor()), zap.String("vendorRefId", request.GetVendorRefId()))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, status.Errorf(codes.NotFound, "user not found")
		}
		return nil, status.Errorf(codes.Internal, "error while fetching user details")
	}

	// validate the card presence if the flag is enabled.
	if request.GetShouldValidateCardPresence() {
		if len(request.GetBin()) != 6 && len(request.GetBin()) != 8 {
			logger.Error(ctx, "unsupported bin number, bin should be 6 or 8 digits length", zap.String("bin", request.GetBin()), zap.String(logger.VENDOR, request.GetVendor()), zap.String(logger.ACTOR_ID_V2, actorId), zap.String("vendorRefId", request.GetVendorRefId()))
			return nil, status.Errorf(codes.InvalidArgument, "unsupported bin number, bin should be 6 or 8 digits length")
		}
		cardType := getCardTypeFromBinNumber(request.GetBin())
		if cardType == types.CardType_CARD_CATEGORY_UNSPECIFIED {
			logger.Error(ctx, "unsupported card bin number received", zap.String("bin", request.GetBin()), zap.String(logger.VENDOR, request.GetVendor()), zap.String(logger.ACTOR_ID_V2, actorId), zap.String("vendorRefId", request.GetVendorRefId()))
			return nil, status.Errorf(codes.InvalidArgument, "unsupported card bin number received")
		}

		cardPresenceStatus, rpcErr := s.validateCardPresence(ctx, actorId, request.GetLastFourDigits(), cardType)
		if rpcErr != nil {
			logger.Error(ctx, "error while validating the card presence", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.VENDOR, request.GetVendor()), zap.String("cardType", cardType.String()), zap.String("vendorRefId", request.GetVendorRefId()), zap.Error(rpcErr))
			return nil, status.Errorf(codes.Internal, "error while validating card presence")
		}

		if !cardPresenceStatus {
			logger.Error(ctx, "card presence status is false", zap.String(logger.VENDOR, request.GetVendor()), zap.String(logger.ACTOR_ID_V2, actorId), zap.String("vendorRefId", request.GetVendorRefId()))
			return nil, status.Errorf(codes.FailedPrecondition, "card presence status: false")
		}
	}

	vendorOrderId := request.GetVendorOrderId()
	// If vendor is POSHVINE, vendor order id is mandatory.
	if vendorOrderId == "" {
		if vendor == evrPb.Vendor_POSHVINE {
			// this is the vendor ref id in fiStoreRedemptions and is required to map the fi coins transaction with the redemption.
			logger.Error(ctx, "vendor order id missing in request", zap.String(logger.VENDOR, request.GetVendor()), zap.String(logger.VENDOR_ORDER_ID, vendorOrderId), zap.String("vendorRefId", request.GetVendorRefId()), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, status.Error(codes.InvalidArgument, "vendor order id missing in request")
		} else {
			// If we are not getting vendor order id, assume vendor ref id as vendor order id.
			vendorOrderId = request.GetVendorRefId()
		}
	}

	txnId, updatedBalance, txnStatus, subStatus, err := s.performTransaction(ctx, &evrPb.TransactFiCoinsRequest{
		ActorId:       actorId,
		VendorRefId:   request.GetVendorRefId(),
		VendorOrderId: vendorOrderId,
		Amount:        request.GetFiCoinsUnits(),
		TxnType:       txnType,
		TxnCategory:   txnCategory,
		Vendor:        vendor,
		Category:      category,
	})
	if err != nil {
		logger.Error(ctx, "failed to perform txn", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("status", txnStatus), zap.String("subStatus", subStatus), zap.String(logger.VENDOR, request.GetVendor()), zap.String(logger.VENDOR_ORDER_ID, vendorOrderId), zap.String("vendorRefId", request.GetVendorRefId()), zap.Error(err))
		return &fiCoinsAccVendorPb.TransactFiCoinsResponse{
			UserId:                request.GetUserId(),
			TxnId:                 txnId,
			UpdatedFiCoinsBalance: updatedBalance,
			Status:                txnStatus,
			SubStatus:             subStatus,
		}, nil
	}
	logger.Info(ctx, "successfully performed txn", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("status", txnStatus), zap.String("subStatus", subStatus), zap.String(logger.VENDOR, request.GetVendor()), zap.String(logger.VENDOR_ORDER_ID, vendorOrderId))
	return &fiCoinsAccVendorPb.TransactFiCoinsResponse{
		UserId:                request.GetUserId(),
		TxnId:                 txnId,
		UpdatedFiCoinsBalance: updatedBalance,
		Status:                txnStatus,
		SubStatus:             subStatus,
	}, nil
}

func (s *Service) performTransaction(ctx context.Context, req *evrPb.TransactFiCoinsRequest) (txnId string, updatedBalance int32, status string, subStatus string, err error) {
	resp, err := s.externalVendorRedemptionClient.TransactFiCoins(ctx, &evrPb.TransactFiCoinsRequest{
		ActorId:       req.GetActorId(),
		VendorRefId:   req.GetVendorRefId(),
		VendorOrderId: req.GetVendorOrderId(),
		Amount:        req.GetAmount(),
		TxnType:       req.GetTxnType(),
		TxnCategory:   req.GetTxnCategory(),
		Vendor:        req.GetVendor(),
		Category:      req.GetCategory(),
	})
	txnId = resp.GetProcessingRefId()
	updatedBalance = resp.GetUpdatedFiCoinsBalance()
	status = resp.GetTxnStatus()
	subStatus = resp.GetSubStatus()
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error while transacting fi coins", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(rpcErr))
		return txnId, updatedBalance, status, subStatus, errors.New("error while transacting fi coins")
	}
	logger.Debug(ctx, "TransactFiCoins RPC successful", zap.String("resp", resp.String()))
	return
}

// verifyJwtToken verifies the token & return error if token is expired or invalid
func (s *Service) verifyJwtToken(ctx context.Context, vendor evrPb.Vendor, userId string) error {
	md, isMetaDataAppendedInContext := metadata.FromIncomingContext(ctx)
	if !isMetaDataAppendedInContext {
		return errors.New("no metadata found in context")
	}
	authToken, found := md["auth_token"]
	if !found || len(authToken) == 0 {
		return errors.New("auth token not found")
	}
	tokenString := authToken[0]
	secretKey := s.getVendorAuthSecretKey(vendor)
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method")
		}
		return []byte(secretKey), nil
	})
	if err != nil || !token.Valid {
		return err
	}
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return fmt.Errorf("invalid token claims")
	}
	payload, ok := claims["user_id"].(string)
	if !ok {
		return fmt.Errorf("invalid token data")
	}
	if payload != userId {
		return fmt.Errorf("invalid payload data")
	}
	return nil
}

// getVendorAuthSecretKey returns vendor's auth secret key
func (s *Service) getVendorAuthSecretKey(vendor evrPb.Vendor) string {
	switch vendor {
	case evrPb.Vendor_DPANDA:
		return s.conf.Secrets().Ids[DpandaAuthApiKey]
	case evrPb.Vendor_POSHVINE:
		return s.conf.Secrets().Ids[PoshvineAuthApiKey]
	case evrPb.Vendor_RAZORPAY:
		return s.conf.Secrets().Ids[RazorpayAuthApiKey]
	default:
		return ""
	}
}

// validateVendor validate vendor provider
func (s *Service) validateVendor(ctx context.Context, vendor string) (evrPb.Vendor, error) {
	switch strings.ToUpper(vendor) {
	case evrPb.Vendor_DPANDA.String():
		return evrPb.Vendor_DPANDA, nil
	case evrPb.Vendor_POSHVINE.String():
		return evrPb.Vendor_POSHVINE, nil
	case evrPb.Vendor_RAZORPAY.String():
		return evrPb.Vendor_RAZORPAY, nil
	default:
		return evrPb.Vendor_VENDOR_UNSPECIFIED, fmt.Errorf("unsupported vendor provided, vendor: %s", vendor)
	}
}

// validateCategory validates category from string.
func (s *Service) validateCategory(ctx context.Context, category string) (evrPb.Category, error) {
	switch "CATEGORY_" + strings.ToUpper(category) {
	case evrPb.Category_CATEGORY_ECOM.String():
		return evrPb.Category_CATEGORY_ECOM, nil
	case evrPb.Category_CATEGORY_GIFT_CARDS.String():
		return evrPb.Category_CATEGORY_GIFT_CARDS, nil
	case evrPb.Category_CATEGORY_FLIGHTS.String():
		return evrPb.Category_CATEGORY_FLIGHTS, nil
	case evrPb.Category_CATEGORY_HOTELS.String():
		return evrPb.Category_CATEGORY_HOTELS, nil
	case evrPb.Category_CATEGORY_MILES_EXCHANGE.String():
		return evrPb.Category_CATEGORY_MILES_EXCHANGE, nil
	default:
		return evrPb.Category_CATEGORY_UNSPECIFIED, fmt.Errorf("unsupported category provided, category: %s", category)
	}
}

// validateTxnType validate txn type
func (s *Service) validateTxnType(ctx context.Context, txnType string) (evrPb.TxnType, error) {
	switch strings.ToUpper(txnType) {
	case evrPb.TxnType_DEBIT.String():
		return evrPb.TxnType_DEBIT, nil
	case evrPb.TxnType_CREDIT.String():
		return evrPb.TxnType_CREDIT, nil
	default:
		return evrPb.TxnType_TXN_TYPE_UNSPECIFIED, fmt.Errorf("unsupported txnType provided, txnType: %s", txnType)
	}
}

// validateTxnCategory validate txn type
func (s *Service) validateTxnCategory(ctx context.Context, txnCategory string) (evrPb.TxnCategory, error) {
	switch strings.ToUpper(txnCategory) {
	case evrPb.TxnCategory_PURCHASE.String():
		return evrPb.TxnCategory_PURCHASE, nil
	case evrPb.TxnCategory_REFUND.String():
		return evrPb.TxnCategory_REFUND, nil
	case evrPb.TxnCategory_PARTIAL_REFUND.String():
		return evrPb.TxnCategory_PARTIAL_REFUND, nil
	default:
		return evrPb.TxnCategory_TXN_CATEGORY_UNSPECIFIED, fmt.Errorf("unsupported txnCategory provided, txnCategory: %s", txnCategory)
	}
}

// getVendorGatewayMappingEnum returns vendor gateway vendor
func (s *Service) getVendorGatewayMappingEnum(ctx context.Context, vendor evrPb.Vendor) commonvgpb.Vendor {
	switch vendor {
	case evrPb.Vendor_DPANDA:
		return commonvgpb.Vendor_DPANDA
	case evrPb.Vendor_POSHVINE:
		return commonvgpb.Vendor_POSHVINE
	case evrPb.Vendor_RAZORPAY:
		return commonvgpb.Vendor_RAZORPAY
	default:
		logger.Error(ctx, "unsupported vendor", zap.String(logger.VENDOR, vendor.String()))
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED
	}
}

// getActorIdFromVendorId get actor id from given vendor's id
func (s *Service) getActorIdFromVendorId(ctx context.Context, vendor evrPb.Vendor, userId string) (string, error) {
	// if user id is empty return empty id, validation should be taken care by caller.
	if userId == "" {
		return "", nil
	}
	vgVendor := s.getVendorGatewayMappingEnum(ctx, vendor)
	actorIdResp, err := s.vendorMappingClient.GetInputIdByVendor(ctx, &vendormappingPb.GetInputIdByVendorRequest{
		Id:     userId,
		Vendor: vgVendor,
	})
	if rpcErr := epifigrpc.RPCError(actorIdResp, err); rpcErr != nil {
		if actorIdResp.GetStatus().IsRecordNotFound() {
			return "", epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error while fetching ActorId mapped to vendor user ID", zap.String(logger.USER_ID, userId), zap.Error(rpcErr))
		return "", fmt.Errorf("error while fetching ActorId mapped to vendor user ID, err: %w", rpcErr)
	}
	return actorIdResp.GetInputId(), nil
}

// GetUserIdFromPhoneNumber returns vendor user id for given phone number.
func (s *Service) GetUserIdFromPhoneNumber(ctx context.Context, request *fiCoinsAccVendorPb.GetUserIdFromPhoneNumberRequest) (*fiCoinsAccVendorPb.GetUserIdFromPhoneNumberResponse, error) {
	err1 := security.CheckWhiteList(ctx, s.conf.DPandaWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix())
	err2 := security.CheckWhiteList(ctx, s.conf.PoshVineWhitelist(), s.conf.NumberOfHopsThatAddXForwardedFor(), s.conf.VpcCidrIPPrefix())
	if err1 != nil && err2 != nil {
		return nil, status.Error(codes.PermissionDenied, "Access forbidden")
	}

	if request.GetPhoneNumber() == "" {
		logger.Error(ctx, "phone number missing in request", zap.String("phoneNumber", request.GetPhoneNumber()))
		return nil, fmt.Errorf("phone number missing in request")
	}
	vendor, err := s.validateVendor(ctx, request.GetVendor())
	if err != nil {
		logger.Error(ctx, "vendor missing in request", zap.String(logger.VENDOR, request.GetVendor()))
		return nil, fmt.Errorf("vendor missing in request")
	}
	// unique identifier should be passed in jwt token body, so here it is phone number.
	err = s.verifyJwtToken(ctx, vendor, request.GetPhoneNumber())
	if err != nil {
		logger.Error(ctx, "invalid jwt token", zap.Error(err))
		return nil, fmt.Errorf("invalid jwt token, err: %v", err)
	}
	phoneNumber, err := validatePhoneNumber(request.GetPhoneNumber())
	if err != nil {
		logger.Error(ctx, "invalid phone number", zap.String("phoneNumber", request.GetPhoneNumber()), zap.Error(err))
		return nil, err
	}
	actorId, err := s.getActorIdFromPhoneNumber(ctx, phoneNumber)
	if err != nil {
		logger.Error(ctx, "error while fetching actor id from phone number", zap.Any("phoneNumber", phoneNumber), zap.Error(err))
		return nil, err
	}
	vendorId, err := s.getVendorIdFromActorId(ctx, vendor, actorId)
	if err != nil {
		logger.Error(ctx, "error while fetching vendor id from actor id", zap.Error(err))
		return nil, fmt.Errorf("error while fetching user id")
	}
	return &fiCoinsAccVendorPb.GetUserIdFromPhoneNumberResponse{UserId: vendorId}, nil
}

// validatePhoneNumber validates the phone number.
func validatePhoneNumber(phoneNumber string) (uint64, error) {
	num, err := strconv.ParseUint(phoneNumber, 0, 64)
	switch {
	case err != nil:
		return 0, fmt.Errorf("error while converting phone number string to number")
	case len(phoneNumber) != 10:
		return 0, fmt.Errorf("invalid phone number. Allowed only 10-digit phone number without the country code")
	default:
		return num, nil
	}
}

// getActorIdFromPhoneNumber returns actorID from phone number.
func (s *Service) getActorIdFromPhoneNumber(ctx context.Context, phoneNumber uint64) (string, error) {
	userResp, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_PhoneNumber{PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: phoneNumber}},
	})
	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		if userResp.GetStatus().IsRecordNotFound() {
			logger.Debug(ctx, "phone number not found", zap.Error(rpcErr))
			return "", fmt.Errorf("phone number not found")
		}
		logger.Error(ctx, "error while fetching user from phone number", zap.Error(rpcErr))
		return "", fmt.Errorf("error while fetching user from phone number")
	}
	return userResp.GetUser().GetActorId(), nil
}

// getVendorIdFromActorId get vendor id from actor id.
func (s *Service) getVendorIdFromActorId(ctx context.Context, vendor evrPb.Vendor, actorId string) (string, error) {
	vgVendor := s.getVendorGatewayMappingEnum(ctx, vendor)
	vendorIdResp, err := s.vendorMappingClient.GetBEMappingById(ctx, &vendormappingPb.GetBEMappingByIdRequest{
		Id: actorId,
	})
	if rpcErr := epifigrpc.RPCError(vendorIdResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching vendor id from actor id", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return "", fmt.Errorf("error while fetching vendor id from actor id, err: %w", rpcErr)
	}
	switch vgVendor {
	case commonvgpb.Vendor_DPANDA:
		return vendorIdResp.GetDpandaId(), nil
	case commonvgpb.Vendor_POSHVINE:
		return vendorIdResp.GetPoshvineId(), nil
	case commonvgpb.Vendor_RAZORPAY:
		return vendorIdResp.GetRazorpayId(), nil
	default:
		return "", fmt.Errorf("unkown vendor")
	}
}

// getCardTypeFromBinNumber returns card type (DEBIT, CREDIT) from card bin number.
// nolint: gocritic
func getCardTypeFromBinNumber(bin string) types.CardType {
	switch {
	case strings.HasPrefix("418730XX", bin):
		return types.CardType_DEBIT
	case strings.HasPrefix("46673000", bin), strings.HasPrefix("40339900", bin), strings.HasPrefix("46673002", bin):
		return types.CardType_CREDIT
	default:
		return types.CardType_CARD_CATEGORY_UNSPECIFIED
	}
}

// validateCardPresence validates the card of the user with last_four_digits of the card.
func (s *Service) validateCardPresence(ctx context.Context, actorId, lastFourDigitsOfCard string, cardType types.CardType) (bool, error) {
	resp, err := s.fireflyClient.ValidateCardPresence(ctx, &fireflyPb.ValidateCardPresenceRequest{
		ActorId:        actorId,
		CardType:       cardType,
		LastFourDigits: lastFourDigitsOfCard,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return false, rpcErr
	}

	return resp.GetPresenceStatus().ToBool(), nil
}
