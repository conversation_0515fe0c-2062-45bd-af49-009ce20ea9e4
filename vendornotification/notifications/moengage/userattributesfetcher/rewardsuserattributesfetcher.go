package userattributesfetcher

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
)

type UserRewardAttributesFetcher struct {
	rewardsServiceClient rewardsPb.RewardsGeneratorClient
}

func NewUserRewardAttributesFetcher(rewardsServiceClient rewardsPb.RewardsGeneratorClient) *UserRewardAttributesFetcher {
	return &UserRewardAttributesFetcher{rewardsServiceClient: rewardsServiceClient}
}

// nolint dupl
func (u *UserRewardAttributesFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	fieldToUserAttributesMap := map[string]*moengageVendorPb.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_Summary:
			rewardsSummary, err := u.getRewardsSummary(ctx, req.GetActorId(), req.GetRequestMetadata())
			if err != nil {
				return nil, fmt.Errorf("error while getting rewards summary, err: %w", err)
			}
			fieldToUserAttributesMap[string(UserAttributesReqField_Summary)] = &moengageVendorPb.UserAttribute{
				ValueTypes: &moengageVendorPb.UserAttribute_RewardsSummary{
					RewardsSummary: rewardsSummary,
				},
			}
		default:
			logger.Info(ctx, "unsupported parameter field encountered in rewards attributes fetcher", zap.String("field", string(field)))
		}
	}
	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func (u *UserRewardAttributesFetcher) getRewardsSummary(ctx context.Context, actorId string, reqMetadata map[RequestMetadataKey]string) (*moengageVendorPb.RewardsSummary, error) {
	if reqMetadata == nil {
		return nil, fmt.Errorf("request metadata cannot be empty for fetching reward summary")
	}

	var (
		fromTime *timestampPb.Timestamp
		toTime   *timestampPb.Timestamp
	)
	durationString, containsDuration := reqMetadata[RequestMetadataKey_Duration]
	fromDateString, containsFromDate := reqMetadata[RequestMetaadataKey_FromDate]
	toDateString, containsToDate := reqMetadata[RequestMetaadataKey_ToDate]
	switch {
	case containsDuration:
		{
			duration, err := time.ParseDuration(durationString)
			if err != nil {
				return nil, fmt.Errorf("error while parsing duration, err: %w", err)
			}
			fromTime = timestampPb.New(time.Now().Add(-duration))
			toTime = timestampPb.Now()
		}
	case containsFromDate:
		{
			var parseDateErr error

			fromTime, parseDateErr = parseDate(fromDateString)
			if parseDateErr != nil {
				return nil, fmt.Errorf("error in parsing %s: %w", RequestMetaadataKey_FromDate, parseDateErr)
			}
			if containsToDate {
				toTime, parseDateErr = parseDate(toDateString)
				if parseDateErr != nil {
					return nil, fmt.Errorf("error in parsing %s: %w", RequestMetaadataKey_ToDate, parseDateErr)
				}
			} else {
				toTime = timestampPb.Now()
			}
		}
	default:
		return nil, errors.New("no recognised attributes in request metadata")
	}

	if (fromTime != nil || toTime != nil) && toTime.AsTime().Sub(fromTime.AsTime()) > time.Hour*datetime.HoursInDay*31 {
		return nil, errors.New("time duration between fromTime and toTime should not be more than 31 days")
	}

	summaryResponse, err := u.rewardsServiceClient.GetRewardSummary(ctx, &rewardsPb.GetRewardSummaryRequest{
		ActorId: actorId,
		Filter: &rewardsPb.GetRewardSummaryRequest_Filter{
			FromTime: fromTime,
			UptoTime: toTime,
		},
	})
	if rpcErr := epifigrpc.RPCError(summaryResponse, err); rpcErr != nil {
		return nil, fmt.Errorf("error in calling rewardsServiceClient.GetRewardSummary, err: %w", rpcErr)
	}

	return &moengageVendorPb.RewardsSummary{
		FiCoinsRewardsSummary: fmt.Sprintf("%v", summaryResponse.GetTotalFiCoinsEarned()),
		CashRewardsSummary:    strconv.FormatInt(summaryResponse.GetTotalCashRewardEarned().GetUnits(), 10),
	}, nil
}

func parseDate(dateStr string) (*timestampPb.Timestamp, error) {
	date, err := time.ParseInLocation("02-01-2006", dateStr, datetime.IST)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date from string: %w", err)
	}
	return timestampPb.New(date.In(datetime.IST)), nil
}
