package userattributesfetcher

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/pkg/networth"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
	stocksVendorPb "github.com/epifi/gamma/api/vendors/moengage/stocks"
	"github.com/epifi/gamma/vendornotification/config/genconf"
)

var (
	WirePortfolioTrackerAttributeFetcherSet = wire.NewSet(NewPortfolioTrackerAttributeFetcher, wire.Bind(new(IUserAttributesFetcher), new(*PortfolioTrackerAttributeFetcher)))
)

type PortfolioTrackerAttributeFetcher struct {
	genConf                 *genconf.Config
	netWorthClient          networthPb.NetWorthClient
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient
	catalogManagerClient    catalogPb.CatalogManagerClient
}

func NewPortfolioTrackerAttributeFetcher(
	genConf *genconf.Config,
	netWorthClient networthPb.NetWorthClient,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
	catalogManagerClient catalogPb.CatalogManagerClient) *PortfolioTrackerAttributeFetcher {
	return &PortfolioTrackerAttributeFetcher{
		genConf:                 genConf,
		netWorthClient:          netWorthClient,
		variableGeneratorClient: variableGeneratorClient,
		catalogManagerClient:    catalogManagerClient,
	}
}

var (
	analyserVariables = []analyserVariablePb.AnalysisVariableName{
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS,
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY,
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO,
	}
	analyserVariablesWithIndianStocks = []analyserVariablePb.AnalysisVariableName{
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS,
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY,
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO,
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION,
	}
)

func (p *PortfolioTrackerAttributeFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	actorId := req.GetActorId()
	fieldToUserAttributesMap := map[string]*moengageVendorPb.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_Summary:
			portfolioTrackerSummary, err := p.getSummary(ctx, actorId)
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					return nil, err
				}
				logger.Error(ctx, "failed to get portfolio tracker summary for content api", zap.Error(err))
				return nil, errors.Wrapf(err, "failed to get portfolio tracker summary for content api")
			}
			fieldToUserAttributesMap[string(UserAttributesReqField_Summary)] = &moengageVendorPb.UserAttribute{
				ValueTypes: &moengageVendorPb.UserAttribute_PortfolioChangeDetails{
					PortfolioChangeDetails: portfolioTrackerSummary,
				},
			}
		default:
			logger.Error(ctx, "unsupported parameter field encountered in loan attributes fetcher", zap.String("field", string(field)))
			return nil, fmt.Errorf("unsupported parameter field encountered in loan attributes fetcher %v", string(field))
		}
	}
	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func (p *PortfolioTrackerAttributeFetcher) getSummary(ctx context.Context, actorId string) (*moengageVendorPb.PortfolioChangeDetails, error) {
	previousDayTimestamp := timestampPb.New(time.Now().AddDate(0, 0, -1))

	analyserVariableMap, err := p.getAnalysisVariableMap(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get analysis variable map: %w", err)
	}

	var topGainerName, topGainerPercentage string
	topGainers, _, err := networth.GetTopGainerAndTopLoser(ctx, &networth.GetTopGainerAndTopLoserRequest{
		AnalyserVariableMap: analyserVariableMap,
		AnalysisVariables:   p.getAnalyserVariables(),
		TopGainers:          1,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get top gainers and losers: %w", err)
	}
	if len(topGainers) == 1 {
		topGainerName = topGainers[0].Name
		topGainerPercentage = fmt.Sprintf("%.2f", topGainers[0].ReturnPercentage)
	}

	dailyNavChangePercentage, err := getNifty50Returns(analyserVariableMap)
	if err != nil {
		return nil, fmt.Errorf("failed to get nifty50 daily change percentage: %w", err)
	}

	dailyChangeAmount, dailyChangePercentage, err := getPortfolioDailyChange(ctx, analyserVariableMap)
	if err != nil {
		return nil, fmt.Errorf("failed to get portfolio daily change percentage: %w", err)
	}

	return &moengageVendorPb.PortfolioChangeDetails{
		PortfolioDailyChangeAmount: dailyChangeAmount,
		PortfolioDailyChangePercentage: &stocksVendorPb.NumericMetric{
			DisplayValue: fmt.Sprintf("%.2f", dailyChangePercentage),
			Value:        dailyChangePercentage,
		},
		PortfolioSummaryDate:           previousDayTimestamp.AsTime().Format("02 January 2006"),
		DailyTopGainerName:             topGainerName,
		DailyTopGainerChangePercentage: topGainerPercentage,
		DailyBenchmarkChangePercentage: &stocksVendorPb.NumericMetric{
			DisplayValue: fmt.Sprintf("%.2f", dailyNavChangePercentage),
			Value:        dailyNavChangePercentage,
		},
	}, nil
}

func (p *PortfolioTrackerAttributeFetcher) getAnalysisVariableMap(ctx context.Context, actorId string) (map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable, error) {
	getAnalysisVariableResp, err := p.variableGeneratorClient.GetAnalysisVariables(ctx, &analyserVariablePb.GetAnalysisVariablesRequest{
		ActorId:               actorId,
		AnalysisVariableNames: p.getAnalyserVariables(),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get analysis variable read only map for portfolio tracker %w", err)
	}
	return getAnalysisVariableResp.GetVariableEnumMap(), nil
}

func getNifty50Returns(analyserVariableMap map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable) (float64, error) {
	nifty50ReturnsResp, ok := analyserVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO]
	if !ok {
		return 0, fmt.Errorf("no analysis variable found for analysis variable name %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO)
	}
	return nifty50ReturnsResp.GetMfPortfolioPerformanceDetails().GetNifty50PercentageReturns(), nil
}

func getPortfolioDailyChange(ctx context.Context, analyserVariableMap map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable) (string, float64, error) {
	var dailyChangeAmount, prevDayValue float64
	for analyserVariable := range analyserVariableMap {
		switch analyserVariable {
		case analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS:
			prevValue, dailyChange, err := getMfDailyChange(ctx, analyserVariableMap)
			if err != nil {
				return "", 0, err
			}
			prevDayValue += prevValue
			dailyChangeAmount += dailyChange
		case analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION:
			prevValue, dailyChange, err := getIndianStocksChange(analyserVariableMap)
			if err != nil {
				return "", 0, err
			}
			prevDayValue += prevValue
			dailyChangeAmount += dailyChange
		}
	}

	if prevDayValue == 0 {
		return "", 0, epifierrors.ErrRecordNotFound
	}
	dailyChangePercentage := dailyChangeAmount / prevDayValue * 100
	dailyChangeAmountString := fmt.Sprintf("%.2f", dailyChangeAmount)
	return dailyChangeAmountString, dailyChangePercentage, nil
}

// getMfDailyChange returns the previous day's value and the daily change for Mutual Funds
func getMfDailyChange(ctx context.Context, analyserVariableMap map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable) (float64, float64, error) {
	mfAnalyserVariable, ok := analyserVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !ok {
		return 0, 0, fmt.Errorf("no analysis variable found for analysis variable name %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS)
	}
	_, prevValue, dailyChangeAmount, _ := networth.GetMfAggregatedValues(ctx, mfAnalyserVariable.GetMfSecretsSchemeAnalytics())
	return prevValue, dailyChangeAmount, nil
}

// getIndianStocksChange returns the previous day's value and the daily change for Indian Stocks
func getIndianStocksChange(analyserVariableMap map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable) (float64, float64, error) {
	indianStocksVariable, ok := analyserVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION]
	if !ok {
		return 0, 0, fmt.Errorf("no analyser variable found for analysis variable name %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION)
	}
	if indianStocksVariable.GetAnalysisVariableState() != analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE {
		return 0, 0, nil
	}
	return money.ToFloat(indianStocksVariable.GetIndianStocksDistribution().GetDayChangeResponse().GetInitialDateTotalValue()),
		indianStocksVariable.GetIndianStocksDistribution().GetDayChangeResponse().GetTotalChange(), nil
}

func (p *PortfolioTrackerAttributeFetcher) getAnalyserVariables() []analyserVariablePb.AnalysisVariableName {
	if p.genConf.EnableIndianStocksContentApi() {
		return analyserVariablesWithIndianStocks
	}
	return analyserVariables
}
