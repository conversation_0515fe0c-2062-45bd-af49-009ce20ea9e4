package userattributesfetcher

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/base64"
	"fmt"

	"github.com/epifi/be-common/api/rpc"
	beCardPb "github.com/epifi/gamma/api/card"
	"github.com/epifi/gamma/api/card/provisioning"
	types "github.com/epifi/gamma/api/typesv2"
	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	"github.com/epifi/gamma/api/frontend/deeplink"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
)

type UserFiStoreAttributesFetcher struct {
	vendorMappingClient vendormappingPb.VendorMappingServiceClient
	cardClient          provisioning.CardProvisioningClient
}

func NewUserFiStoreAttributesFetcher(vendorMappingClient vendormappingPb.VendorMappingServiceClient, cardClient provisioning.CardProvisioningClient) *UserFiStoreAttributesFetcher {
	return &UserFiStoreAttributesFetcher{
		vendorMappingClient: vendorMappingClient,
		cardClient:          cardClient,
	}
}

// nolint: dupl
func (u *UserFiStoreAttributesFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	fieldToUserAttributesMap := map[string]*moengageVendorPb.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_Deeplink:
			base64EncodedDeeplink, err := u.getBase64EncodedDeeplink(ctx, req.GetActorId(), req.GetRequestMetadata())
			if err != nil {
				return nil, fmt.Errorf("error while getting base64EncodedDeeplink deeplink, err: %w", err)
			}
			fieldToUserAttributesMap[string(UserAttributesReqField_Deeplink)] = &moengageVendorPb.UserAttribute{
				ValueTypes: &moengageVendorPb.UserAttribute_StringValue{
					StringValue: base64EncodedDeeplink,
				},
			}
		default:
			logger.Info(ctx, "unsupported parameter field encountered in fi store attributes fetcher", zap.String("field", string(field)))
		}
	}
	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func (u *UserFiStoreAttributesFetcher) getBase64EncodedDeeplink(ctx context.Context, actorId string, reqMetadata map[RequestMetadataKey]string) (string, error) {
	if reqMetadata == nil || reqMetadata[RequestMetadataKey_Vendor] == "" {
		return "", fmt.Errorf("vendor is required for fetching fi store deeplink")
	}
	vendor := reqMetadata[RequestMetadataKey_Vendor]

	switch vendor {
	case evrPb.Vendor_DPANDA.String():
		if reqMetadata[RequestMetadataKey_WebPageUrl] == "" {
			return "", errors.Wrap(epifierrors.ErrInvalidArgument, "web page url is required for fetching fi store deeplink for dpanda")
		}
		dPandaDeeplink, err := u.getWebPageDeeplinkWithCardDetails(ctx, actorId, reqMetadata[RequestMetadataKey_WebPageUrl], vendor)
		if err != nil {
			logger.Error(ctx, "error while fetching dPanda deeplink", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return "", err
		}
		marshalledProto, err := proto.Marshal(dPandaDeeplink)
		if err != nil {
			return "", err
		}
		return base64.StdEncoding.EncodeToString(marshalledProto), nil
	case evrPb.Vendor_POSHVINE.String():
		if reqMetadata[RequestMetadataKey_WebPageUrl] == "" {
			return "", errors.Wrap(epifierrors.ErrInvalidArgument, "web page url is required for fetching fi store deeplink for poshvine")
		}
		poshVineDeeplink, err := u.getWebPageDeeplinkWithCardDetails(ctx, actorId, reqMetadata[RequestMetadataKey_WebPageUrl], vendor)
		if err != nil {
			logger.Error(ctx, "error while fetching poshvine deeplink", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return "", err
		}
		marshalledProto, err := proto.Marshal(poshVineDeeplink)
		if err != nil {
			return "", err
		}
		return base64.StdEncoding.EncodeToString(marshalledProto), nil
	default:
		return "", errors.Wrap(epifierrors.ErrInvalidArgument, "unsupported vendor for fi store")
	}
}

func (u *UserFiStoreAttributesFetcher) getWebPageDeeplinkWithCardDetails(ctx context.Context, actorId, targetUrl string, vendor string) (*deeplink.Deeplink, error) {
	var (
		debitCardId          string
		showDebitCardDetails bool
	)
	// fetch debit card id to pass in deeplink to show debit card details in webpage.
	cards, cardFetchErr := u.cardClient.FetchCards(ctx, &provisioning.FetchCardsRequest{
		Actor:            &types.Actor{Id: actorId},
		IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
		CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
		CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
		CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
		CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
		SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
	})
	switch rpcErr := epifigrpc.RPCError(cards, cardFetchErr); {
	case rpc.StatusFromError(rpcErr).IsRecordNotFound():
		showDebitCardDetails = false
	case rpcErr != nil:
		logger.Error(ctx, "error while fetching debit card details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return nil, rpcErr
	case len(cards.GetCards()) == 0:
		showDebitCardDetails = false
	default:
		showDebitCardDetails = true
		debitCardId = cards.GetCards()[0].GetId()
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_WEB_PAGE_WITH_CARD_DETAILS_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&pkgScreenOptionsPb.WebPageWithCardDetailsScreenOptions{
			WebpageTitle:             "Fi Store",
			WebpageUrl:               "",
			DisableHardwareBackPress: true,
			DisableDropDownAnimation: true,
			ShowUpiDetails:           true,
			ShowCreditCardDetails:    true,
			ShowDebitCardDetails:     showDebitCardDetails,
			DebitCardId:              debitCardId,
			RequestMetadata:          fmt.Sprintf("vendor:%s,target_url:%s", vendor, targetUrl),
		}),
	}, nil
}
