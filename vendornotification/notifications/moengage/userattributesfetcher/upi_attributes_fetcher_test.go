package userattributesfetcher

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/gamma/api/accounts"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	onboardingEnums "github.com/epifi/gamma/api/upi/onboarding/enums"
)

// Mock clients
type mockUpiOnboardingClient struct {
	mock.Mock
	upiOnbPb.UpiOnboardingClient
}

type mockAccountPIClient struct {
	mock.Mock
	accountPiPb.AccountPIRelationClient
}

type mockSavingsClient struct {
	mock.Mock
	savingsPb.SavingsClient
}

func (m *mockUpiOnboardingClient) GetAccounts(ctx context.Context, in *upiOnbPb.GetAccountsRequest, opts ...grpc.CallOption) (*upiOnbPb.GetAccountsResponse, error) {
	args := m.Called(ctx, in)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*upiOnbPb.GetAccountsResponse), args.Error(1)
}

func (m *mockAccountPIClient) GetPiByAccountId(ctx context.Context, in *accountPiPb.GetPiByAccountIdRequest, opts ...grpc.CallOption) (*accountPiPb.GetPiByAccountIdResponse, error) {
	args := m.Called(ctx, in)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*accountPiPb.GetPiByAccountIdResponse), args.Error(1)
}

func (m *mockSavingsClient) GetSavingsAccountEssentials(ctx context.Context, in *savingsPb.GetSavingsAccountEssentialsRequest, opts ...grpc.CallOption) (*savingsPb.GetSavingsAccountEssentialsResponse, error) {
	args := m.Called(ctx, in)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*savingsPb.GetSavingsAccountEssentialsResponse), args.Error(1)
}

func TestUpiAttributesFetcher_GetAttributes(t *testing.T) {
	type testUser struct {
		actorID     string
		internalUPI string
		primaryUPI  string
	}
	testUsers := []testUser{
		{
			actorID:    "actor1",
			primaryUPI: "user1@primary",
		},
		{
			actorID:     "actor2",
			internalUPI: "user2@federal",
			primaryUPI:  "user2@federal",
		},
		{
			actorID:     "actor3",
			internalUPI: "user3@federal",
			primaryUPI:  "user3@primary",
		},
	}
	tests := []struct {
		name          string
		user          testUser
		setupMocks    func(*mockUpiOnboardingClient, *mockAccountPIClient, *mockSavingsClient)
		expectedUPIID string
		expectError   bool
	}{
		{
			name: "When no federal account exists, the primary non-federal account should be used",
			user: testUsers[0],
			setupMocks: func(upiMock *mockUpiOnboardingClient, accPiMock *mockAccountPIClient, savingsMock *mockSavingsClient) {
				savingsMock.On("GetSavingsAccountEssentials", mock.Anything, &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				upiMock.On("GetAccounts", mock.Anything, &upiOnbPb.GetAccountsRequest{
					ActorId: "actor1",
					AccountStatus: []onboardingEnums.UpiAccountStatus{
						onboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						onboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				}).Return(&upiOnbPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnbPb.UpiAccount{
						{
							Id:                "primary123",
							AccountPreference: onboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY,
						},
					},
				}, nil)
				accPiMock.On("GetPiByAccountId", mock.Anything, mock.MatchedBy(func(req *accountPiPb.GetPiByAccountIdRequest) bool {
					return req.GetAccountId() == "primary123" &&
						(req.GetAccountType() == accounts.Type_SAVINGS || req.GetAccountType() == 0) &&
						len(req.GetPiTypes()) == 1 && req.GetPiTypes()[0] == piPb.PaymentInstrumentType_UPI &&
						len(req.GetPiStates()) == 3 &&
						req.GetPiStates()[0] == piPb.PaymentInstrumentState_CREATED &&
						req.GetPiStates()[1] == piPb.PaymentInstrumentState_VERIFIED &&
						req.GetPiStates()[2] == piPb.PaymentInstrumentState_SUSPENDED
				})).Return(&accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type:  piPb.PaymentInstrumentType_UPI,
							State: piPb.PaymentInstrumentState_VERIFIED,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:     "user1@primary",
									VpaType: piPb.Upi_VPA_TYPE_UNSPECIFIED,
								},
							},
							Capabilities: map[string]bool{
								piPb.Capability_OUTBOUND_TXN.String(): true,
								piPb.Capability_INBOUND_TXN.String():  true,
							},
						},
					},
				}, nil)
			},
			expectedUPIID: "user1@primary",
			expectError:   false,
		},
		{
			name: "When federal account exists, it should be used",
			user: testUsers[1],
			setupMocks: func(upiMock *mockUpiOnboardingClient, accPiMock *mockAccountPIClient, savingsMock *mockSavingsClient) {
				savingsMock.On("GetSavingsAccountEssentials", mock.Anything, &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor2",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id: "same123",
					},
				}, nil)
				accPiMock.On("GetPiByAccountId", mock.Anything, mock.MatchedBy(func(req *accountPiPb.GetPiByAccountIdRequest) bool {
					return req.GetAccountId() == "same123" &&
						(req.GetAccountType() == accounts.Type_SAVINGS || req.GetAccountType() == 0) &&
						len(req.GetPiTypes()) == 1 && req.GetPiTypes()[0] == piPb.PaymentInstrumentType_UPI &&
						len(req.GetPiStates()) == 3 &&
						req.GetPiStates()[0] == piPb.PaymentInstrumentState_CREATED &&
						req.GetPiStates()[1] == piPb.PaymentInstrumentState_VERIFIED &&
						req.GetPiStates()[2] == piPb.PaymentInstrumentState_SUSPENDED
				})).Return(&accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type:  piPb.PaymentInstrumentType_UPI,
							State: piPb.PaymentInstrumentState_VERIFIED,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:     "user2@federal",
									VpaType: piPb.Upi_VPA_TYPE_UNSPECIFIED,
								},
							},
							Capabilities: map[string]bool{
								piPb.Capability_OUTBOUND_TXN.String(): true,
								piPb.Capability_INBOUND_TXN.String():  true,
							},
						},
					},
				}, nil)
			},
			expectedUPIID: "user2@federal",
			expectError:   false,
		},
		{
			name: "When federal account exists, it should be used though there is another non-federal account",
			user: testUsers[2],
			setupMocks: func(upiMock *mockUpiOnboardingClient, accPiMock *mockAccountPIClient, savingsMock *mockSavingsClient) {
				// Federal account setup
				savingsMock.On("GetSavingsAccountEssentials", mock.Anything, &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor3",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						Id: "federal123",
					},
				}, nil)

				// Handle both account types in GetPiByAccountId
				accPiMock.On("GetPiByAccountId", mock.Anything, mock.MatchedBy(func(req *accountPiPb.GetPiByAccountIdRequest) bool {
					return req.GetAccountId() == "federal123" &&
						(req.GetAccountType() == accounts.Type_SAVINGS || req.GetAccountType() == 0) &&
						len(req.GetPiTypes()) == 1 && req.GetPiTypes()[0] == piPb.PaymentInstrumentType_UPI &&
						len(req.GetPiStates()) == 3 &&
						req.GetPiStates()[0] == piPb.PaymentInstrumentState_CREATED &&
						req.GetPiStates()[1] == piPb.PaymentInstrumentState_VERIFIED &&
						req.GetPiStates()[2] == piPb.PaymentInstrumentState_SUSPENDED
				})).Return(&accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type:  piPb.PaymentInstrumentType_UPI,
							State: piPb.PaymentInstrumentState_VERIFIED,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:     "user3@federal",
									VpaType: piPb.Upi_VPA_TYPE_UNSPECIFIED,
								},
							},
							Capabilities: map[string]bool{
								piPb.Capability_OUTBOUND_TXN.String(): true,
								piPb.Capability_INBOUND_TXN.String():  true,
							},
						},
					},
				}, nil)

				// Primary account setup (should not be called since federal account exists)
				accPiMock.On("GetPiByAccountId", mock.Anything, &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "primary123",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
					PiStates: []piPb.PaymentInstrumentState{
						piPb.PaymentInstrumentState_CREATED,
						piPb.PaymentInstrumentState_VERIFIED,
						piPb.PaymentInstrumentState_SUSPENDED,
					},
				}).Return(nil, fmt.Errorf("failed to get VPA")).Maybe()
			},
			expectedUPIID: "user3@federal",
			expectError:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Initialize mocks
			upiMock := &mockUpiOnboardingClient{}
			accPiMock := &mockAccountPIClient{}
			savingsMock := &mockSavingsClient{}

			// Setup mocks
			tt.setupMocks(upiMock, accPiMock, savingsMock)

			// Create fetcher
			fetcher := NewUpiAttributesFetcher(upiMock, accPiMock, savingsMock)

			// Create request
			req := &GetAttributesRequest{
				ActorId:   tt.user.actorID,
				FieldMask: []UserAttributesReqField{UserAttributesReqField_UpiId},
			}

			// Execute test
			resp, err := fetcher.GetAttributes(context.Background(), req)

			// Verify results
			if tt.expectError {
				require.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, resp)

			upiAttr, exists := resp.FieldNameToAttributesMap[string(UserAttributesReqField_UpiId)]
			assert.True(t, exists)
			assert.Equal(t, tt.expectedUPIID, upiAttr.GetStringValue())

			// Verify all mock expectations were met
			upiMock.AssertExpectations(t)
			accPiMock.AssertExpectations(t)
			savingsMock.AssertExpectations(t)
		})
	}
}
