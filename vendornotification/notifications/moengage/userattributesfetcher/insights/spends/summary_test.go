package spends

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	txnaggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	mockCategorizerPb "github.com/epifi/gamma/api/categorizer/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	typesPb "github.com/epifi/gamma/api/typesv2"
	moengageInsightsPb "github.com/epifi/gamma/api/vendors/moengage/insights"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/pkg/dmf/datafetcher"
	mock_datafetcher "github.com/epifi/gamma/pkg/dmf/datafetcher/mocks"
	"github.com/epifi/gamma/pkg/dmf/txnaggregates"
	mock_txnaggregates "github.com/epifi/gamma/pkg/dmf/txnaggregates/mocks"
	"github.com/epifi/be-common/pkg/logger"
	userAttributeFetcher "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
)

type mockFields struct {
	mockActorAccount        *mock_datafetcher.MockActorAccounts
	mockTxnAggregatesHelper *mock_txnaggregates.MockTxnAggregates
	mockCategorizerClient   *mockCategorizerPb.MockTxnCategorizerClient
}

func initMocks(ctl *gomock.Controller) *mockFields {
	return &mockFields{
		mockActorAccount:        mock_datafetcher.NewMockActorAccounts(ctl),
		mockTxnAggregatesHelper: mock_txnaggregates.NewMockTxnAggregates(ctl),
		mockCategorizerClient:   mockCategorizerPb.NewMockTxnCategorizerClient(ctl),
	}
}

func TestSummaryGenerator_GetSpendsSummary(t *testing.T) {
	logger.Init(cfg.TestEnv)
	type args struct {
		actorId     string
		requestMeta map[userAttributeFetcher.RequestMetadataKey]string
	}
	tests := []struct {
		name    string
		args    args
		before  func(m *mockFields)
		want    *moengageInsightsPb.SpendsSummary
		wantErr bool
	}{
		{
			name: "Successfully get spends summary",
			args: args{
				actorId: "actorId",
				requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
					userAttributeFetcher.RequestMetaadataKey_FromDate: "01-Jan-2023",
					userAttributeFetcher.RequestMetaadataKey_ToDate:   "15-Jan-2023",
				},
			},
			before: func(m *mockFields) {
				fiAccts := []string{"fiAcct1"}
				aaAccts := []string{"aaAcct1", "aaAcct2"}
				m.mockActorAccount.EXPECT().FetchAccountIds(gomock.Any(), "actorId", gomock.Any(), gomock.Any()).Return(&datafetcher.FetchAccountIdsResponse{
					FiAccountIds: fiAccts,
					AaAccountIds: aaAccts,
				}, nil)

				m.mockTxnAggregatesHelper.EXPECT().GetTopCategoriesAggs(gomock.Any(), "actorId",
					timestampPb.New(time.Date(2023, time.January, 1, 0, 0, 0, 0, datetime.IST)),
					timestampPb.New(time.Date(2023, time.January, 16, 0, 0, 0, 0, datetime.IST)),
					fiAccts, aaAccts, []txnaggregates.AggregationType{
						txnaggregates.AmountSum,
					}, paymentPb.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED).Return([]*txnaggregatesPb.Aggregate{
					{
						DisplayCategory: categorizerPb.DisplayCategory_GROCERIES,
						CategoryL0:      categorizerPb.L0_L0_SPEND,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        100,
							Decimals:     40,
						},
					},
					{
						DisplayCategory: categorizerPb.DisplayCategory_TRAVEL_VACATION,
						CategoryL0:      categorizerPb.L0_L0_SPEND,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        1002,
							Decimals:     55,
						},
					},
					{
						DisplayCategory: categorizerPb.DisplayCategory_SETTLEMENT,
						CategoryL0:      categorizerPb.L0_L0_DEBT_SETTLEMENT,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        20,
						},
					},
					{
						DisplayCategory: categorizerPb.DisplayCategory_SELF_TRANSFER_DEBIT,
						CategoryL0:      categorizerPb.L0_L0_SPEND,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        500,
						},
					},
					{
						DisplayCategory: categorizerPb.DisplayCategory_SELF_TRANSFER_CREDIT,
						CategoryL0:      categorizerPb.L0_L0_INCOME,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
					},
					{
						DisplayCategory: categorizerPb.DisplayCategory_SALARY,
						CategoryL0:      categorizerPb.L0_L0_INCOME,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        120,
						},
					},
					{
						DisplayCategory: categorizerPb.DisplayCategory_BORROWED,
						CategoryL0:      categorizerPb.L0_L0_LOAN,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        10,
						},
					},
					{
						DisplayCategory: categorizerPb.DisplayCategory_STOCKS_MF,
						CategoryL0:      categorizerPb.L0_L0_INVESTMENTS,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        150,
						},
					},
					{
						DisplayCategory: categorizerPb.DisplayCategory_UNKNOWN,
						CategoryL0:      categorizerPb.L0_L0_UNKNOWN,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        1000,
						},
					},
					{
						DisplayCategory: categorizerPb.DisplayCategory_DISPLAY_CATEGORY_UNSPECIFIED,
						CategoryL0:      categorizerPb.L0_L0_UNSPECIFIED,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        500,
						},
					},
				}, nil)

				m.mockCategorizerClient.EXPECT().GetCategoriesInfo(gomock.Any(), &categorizerPb.GetCategoriesInfoRequest{}).Return(&categorizerPb.GetCategoriesInfoResponse{
					Status: rpcPb.StatusOk(),
					CategoriesInfo: []*categorizerPb.CategoryInfo{
						{
							DisplayCategory: categorizerPb.DisplayCategory_TRAVEL_VACATION,
							DisplayName:     "Travel",
						},
						{
							DisplayCategory: categorizerPb.DisplayCategory_GROCERIES,
							DisplayName:     "Groceries",
						},
						{
							DisplayCategory: categorizerPb.DisplayCategory_SELF_TRANSFER_DEBIT,
							DisplayName:     "Debit",
						},
						{
							DisplayCategory: categorizerPb.DisplayCategory_SELF_TRANSFER_CREDIT,
							DisplayName:     "Credit",
						},
						{
							DisplayCategory: categorizerPb.DisplayCategory_SALARY,
							DisplayName:     "Salary",
						},
						{
							DisplayCategory: categorizerPb.DisplayCategory_STOCKS_MF,
							DisplayName:     "Stocks & MF",
						},
						{
							DisplayCategory: categorizerPb.DisplayCategory_SETTLEMENT,
							DisplayName:     "Settlement",
						},
						{
							DisplayCategory: categorizerPb.DisplayCategory_BORROWED,
							DisplayName:     "Borrowed",
						},
					},
				}, nil)
			},
			want: &moengageInsightsPb.SpendsSummary{
				MoneyIn: &moengageInsightsPb.Amount{
					DisplayValue: "₹230",
					Value:        230,
				},
				MoneyOut: &moengageInsightsPb.Amount{
					DisplayValue: "₹1,773",
					Value:        1772.95,
				},
				Income: &moengageInsightsPb.Amount{
					DisplayValue: "₹130",
					Value:        130,
				},
				Spends: &moengageInsightsPb.Amount{
					DisplayValue: "₹1,123",
					Value:        1122.95,
				},
				Investments: &moengageInsightsPb.Amount{
					DisplayValue: "₹150",
					Value:        150,
				},
				TopCategories: []*moengageInsightsPb.CategoryAggregate{
					{
						DisplayName: "Travel",
						Amount: &moengageInsightsPb.Amount{
							DisplayValue: "₹1,003",
							Value:        1002.55,
						},
					},
					{
						DisplayName: "Groceries",
						Amount: &moengageInsightsPb.Amount{
							DisplayValue: "₹100",
							Value:        100.4,
						},
					},
					{
						DisplayName: "Settlement",
						Amount: &moengageInsightsPb.Amount{
							DisplayValue: "₹20",
							Value:        20,
						},
					},
				},
			},
		},
		{
			name: "Failure to get account ids for actor should return error",
			args: args{
				actorId: "actorId",
				requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
					userAttributeFetcher.RequestMetaadataKey_FromDate: "01-Jan-2023",
					userAttributeFetcher.RequestMetaadataKey_ToDate:   "15-Jan-2023",
				},
			},
			before: func(m *mockFields) {
				m.mockActorAccount.EXPECT().FetchAccountIds(gomock.Any(), "actorId", gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("some err"))
			},
			wantErr: true,
		},
		{
			name: "Error in getting category aggregates should return error",
			args: args{
				actorId: "actorId",
				requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
					userAttributeFetcher.RequestMetaadataKey_FromDate: "01-Jan-2023",
					userAttributeFetcher.RequestMetaadataKey_ToDate:   "15-Jan-2023",
				},
			},
			before: func(m *mockFields) {
				fiAccts := []string{"fiAcct1"}
				aaAccts := []string{"aaAcct1", "aaAcct2"}
				m.mockActorAccount.EXPECT().FetchAccountIds(gomock.Any(), "actorId", gomock.Any(), gomock.Any()).Return(&datafetcher.FetchAccountIdsResponse{
					FiAccountIds: fiAccts,
					AaAccountIds: aaAccts,
				}, nil)

				m.mockTxnAggregatesHelper.EXPECT().GetTopCategoriesAggs(gomock.Any(), "actorId",
					timestampPb.New(time.Date(2023, time.January, 1, 0, 0, 0, 0, datetime.IST)),
					timestampPb.New(time.Date(2023, time.January, 16, 0, 0, 0, 0, datetime.IST)),
					fiAccts, aaAccts, []txnaggregates.AggregationType{
						txnaggregates.AmountSum,
					}, paymentPb.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED).Return(nil, fmt.Errorf("some err"))
			},
			wantErr: true,
		},
		{
			name: "Error in getting category info should return error",
			args: args{
				actorId: "actorId",
				requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
					userAttributeFetcher.RequestMetaadataKey_FromDate: "01-Jan-2023",
					userAttributeFetcher.RequestMetaadataKey_ToDate:   "15-Jan-2023",
				},
			},
			before: func(m *mockFields) {
				fiAccts := []string{"fiAcct1"}
				aaAccts := []string{"aaAcct1", "aaAcct2"}
				m.mockActorAccount.EXPECT().FetchAccountIds(gomock.Any(), "actorId", gomock.Any(), gomock.Any()).Return(&datafetcher.FetchAccountIdsResponse{
					FiAccountIds: fiAccts,
					AaAccountIds: aaAccts,
				}, nil)

				m.mockTxnAggregatesHelper.EXPECT().GetTopCategoriesAggs(gomock.Any(), "actorId",
					timestampPb.New(time.Date(2023, time.January, 1, 0, 0, 0, 0, datetime.IST)),
					timestampPb.New(time.Date(2023, time.January, 16, 0, 0, 0, 0, datetime.IST)),
					fiAccts, aaAccts, []txnaggregates.AggregationType{
						txnaggregates.AmountSum,
					}, paymentPb.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED).Return([]*txnaggregatesPb.Aggregate{
					{
						DisplayCategory: categorizerPb.DisplayCategory_GROCERIES,
						CategoryL0:      categorizerPb.L0_L0_SPEND,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        100,
							Decimals:     40,
						},
					},
				}, nil)

				m.mockCategorizerClient.EXPECT().GetCategoriesInfo(gomock.Any(), &categorizerPb.GetCategoriesInfoRequest{}).Return(&categorizerPb.GetCategoriesInfoResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Invalid date format should return error",
			args: args{
				actorId: "actorId",
				requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
					userAttributeFetcher.RequestMetaadataKey_FromDate: "2023-Jan-01",
					userAttributeFetcher.RequestMetaadataKey_ToDate:   "15-Jan-2023",
				},
			},
			before:  func(m *mockFields) {},
			wantErr: true,
		},
		{
			name: "Missing to date key should return error",
			args: args{
				actorId: "actorId",
				requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
					userAttributeFetcher.RequestMetaadataKey_FromDate: "01-Jan-2023",
				},
			},
			before:  func(m *mockFields) {},
			wantErr: true,
		},
		{
			name: "Missing category info should return error",
			args: args{
				actorId: "actorId",
				requestMeta: map[userAttributeFetcher.RequestMetadataKey]string{
					userAttributeFetcher.RequestMetaadataKey_FromDate: "01-Jan-2023",
					userAttributeFetcher.RequestMetaadataKey_ToDate:   "15-Jan-2023",
				},
			},
			before: func(m *mockFields) {
				fiAccts := []string{"fiAcct1"}
				aaAccts := []string{"aaAcct1", "aaAcct2"}
				m.mockActorAccount.EXPECT().FetchAccountIds(gomock.Any(), "actorId", gomock.Any(), gomock.Any()).Return(&datafetcher.FetchAccountIdsResponse{
					FiAccountIds: fiAccts,
					AaAccountIds: aaAccts,
				}, nil)

				m.mockTxnAggregatesHelper.EXPECT().GetTopCategoriesAggs(gomock.Any(), "actorId",
					timestampPb.New(time.Date(2023, time.January, 1, 0, 0, 0, 0, datetime.IST)),
					timestampPb.New(time.Date(2023, time.January, 16, 0, 0, 0, 0, datetime.IST)),
					fiAccts, aaAccts, []txnaggregates.AggregationType{
						txnaggregates.AmountSum,
					}, paymentPb.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED).Return([]*txnaggregatesPb.Aggregate{
					{
						DisplayCategory: categorizerPb.DisplayCategory_TRAVEL_VACATION,
						CategoryL0:      categorizerPb.L0_L0_SPEND,
						SumAmount: &typesPb.Money{
							CurrencyCode: "INR",
							Units:        1002,
							Decimals:     55,
						},
					},
				}, nil)

				m.mockCategorizerClient.EXPECT().GetCategoriesInfo(gomock.Any(), &categorizerPb.GetCategoriesInfoRequest{}).Return(&categorizerPb.GetCategoriesInfoResponse{
					Status: rpcPb.StatusOk(),
					CategoriesInfo: []*categorizerPb.CategoryInfo{
						{
							DisplayCategory: categorizerPb.DisplayCategory_GROCERIES,
							DisplayName:     "Groceries",
						},
					},
				}, nil)
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initMocks(ctl)
			tt.before(m)
			s := NewSummaryGenerator(m.mockActorAccount, m.mockTxnAggregatesHelper, m.mockCategorizerClient)
			got, err := s.GetSpendsSummary(context.Background(), tt.args.actorId, tt.args.requestMeta)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSpendsSummary() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("GetSpendsSummary() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}
