package userattributesfetcher

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	moneypb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	mockRewardsGenerator "github.com/epifi/gamma/api/rewards/mocks"
	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
)

func TestUserRewardAttributesFetcher_getRewardsSummary(t *testing.T) {
	var (
		mockRewardsClient *mockRewardsGenerator.MockRewardsGeneratorClient
		location          = datetime.IST
	)
	type args struct {
		ctx         context.Context
		actorId     string
		reqMetadata map[RequestMetadataKey]string
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRewardsClient *mockRewardsGenerator.MockRewardsGeneratorClient)
		want       *moengageVendorPb.RewardsSummary
		wantErr    bool
	}{
		{
			name: "should return error if request metadata doesn't contain duration or from_date key",
			args: args{
				ctx:         context.Background(),
				actorId:     "actor",
				reqMetadata: map[RequestMetadataKey]string{},
			},
			setupMocks: func(mockRewardsClient *mockRewardsGenerator.MockRewardsGeneratorClient) {},
			want:       nil,
			wantErr:    true,
		},
		{
			name: "should return error if duration is not parsable into a valid time.Duration value",
			args: args{
				ctx:     context.Background(),
				actorId: "actor",
				reqMetadata: map[RequestMetadataKey]string{
					RequestMetadataKey_Duration: "1",
				},
			},
			setupMocks: func(mockRewardsClient *mockRewardsGenerator.MockRewardsGeneratorClient) {},
			want:       nil,
			wantErr:    true,
		},
		{
			name: "should return error if from_date is not parsable into a valid timestamp",
			args: args{
				ctx:     context.Background(),
				actorId: "actor",
				reqMetadata: map[RequestMetadataKey]string{
					RequestMetaadataKey_FromDate: "10-1-2023", // format is DD-MM-YYYY
				},
			},
			setupMocks: func(mockRewardsClient *mockRewardsGenerator.MockRewardsGeneratorClient) {},
			want:       nil,
			wantErr:    true,
		},
		{
			name: "should return error if GetRewardSummary RPC returns error",
			args: args{
				ctx:     context.Background(),
				actorId: "actor",
				reqMetadata: map[RequestMetadataKey]string{
					RequestMetadataKey_Duration: "1h",
				},
			},
			setupMocks: func(mockRewardsClient *mockRewardsGenerator.MockRewardsGeneratorClient) {
				mockRewardsClient.EXPECT().GetRewardSummary(context.Background(), gomock.Any()).Return(&rewardsPb.GetRewardSummaryResponse{Status: rpc.StatusInternal()}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return error if time duration between fromTime and toTime is more than 31 days",
			args: args{
				ctx:     context.Background(),
				actorId: "actor",
				reqMetadata: map[RequestMetadataKey]string{
					RequestMetaadataKey_FromDate: "01-01-2023",
					RequestMetaadataKey_ToDate:   "02-02-2023",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "duration - should return correct response if no error occurs",
			args: args{
				ctx:     context.Background(),
				actorId: "actor",
				reqMetadata: map[RequestMetadataKey]string{
					RequestMetadataKey_Duration: "1h",
				},
			},
			setupMocks: func(mockRewardsClient *mockRewardsGenerator.MockRewardsGeneratorClient) {
				mockRewardsClient.EXPECT().GetRewardSummary(context.Background(), gomock.Any()).Return(&rewardsPb.GetRewardSummaryResponse{
					Status:                rpc.StatusOk(),
					TotalCashRewardEarned: &moneypb.Money{Units: 10},
					TotalFiCoinsEarned:    20,
				}, nil)
			},
			want: &moengageVendorPb.RewardsSummary{
				FiCoinsRewardsSummary: fmt.Sprintf("%v", 20),
				CashRewardsSummary:    fmt.Sprintf("%v", strconv.FormatInt(int64(10), 10)),
			},
			wantErr: false,
		},
		{
			name: "fromDate endDate - should return correct response if no error occurs",
			args: args{
				ctx:     context.Background(),
				actorId: "actor",
				reqMetadata: map[RequestMetadataKey]string{
					RequestMetaadataKey_FromDate: "01-01-2023",
					RequestMetaadataKey_ToDate:   "31-01-2023",
				},
			},
			setupMocks: func(mockRewardsClient *mockRewardsGenerator.MockRewardsGeneratorClient) {
				mockRewardsClient.EXPECT().GetRewardSummary(context.Background(), &rewardsPb.GetRewardSummaryRequest{
					ActorId: "actor",
					Filter: &rewardsPb.GetRewardSummaryRequest_Filter{
						FromTime: timestamppb.New(time.Date(2023, 01, 01, 0, 0, 0, 0, location)),
						UptoTime: timestamppb.New(time.Date(2023, 01, 31, 0, 0, 0, 0, location)),
					},
				}).Return(&rewardsPb.GetRewardSummaryResponse{
					Status:                rpc.StatusOk(),
					TotalCashRewardEarned: &moneypb.Money{Units: 10},
					TotalFiCoinsEarned:    20,
				}, nil)
			},
			want: &moengageVendorPb.RewardsSummary{
				FiCoinsRewardsSummary: fmt.Sprintf("%v", 20),
				CashRewardsSummary:    fmt.Sprintf("%v", strconv.FormatInt(int64(10), 10)),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockRewardsClient = mockRewardsGenerator.NewMockRewardsGeneratorClient(ctrl)
			if tt.setupMocks != nil {
				tt.setupMocks(mockRewardsClient)
			}

			u := &UserRewardAttributesFetcher{
				rewardsServiceClient: mockRewardsClient,
			}
			got, err := u.getRewardsSummary(tt.args.ctx, tt.args.actorId, tt.args.reqMetadata)
			if (err != nil) != tt.wantErr {
				t.Errorf("getRewardsSummary() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getRewardsSummary() got = %v, want %v", got, tt.want)
			}
		})
	}
}
