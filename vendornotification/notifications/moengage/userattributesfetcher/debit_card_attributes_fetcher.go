package userattributesfetcher

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	moneypb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendors/moengage"
	addressPkg "github.com/epifi/gamma/pkg/address"
)

type DebitCardAttributesFetcher struct {
	cardProvisioningClient provisioning.CardProvisioningClient
	payClient              pay.PayClient
	usersClient            userPb.UsersClient
	actorClient            actorPb.ActorClient
}

func NewDebitCardAttributesFetcher(cardProvisioningClient provisioning.CardProvisioningClient, payClient pay.PayClient,
	usersClient userPb.UsersClient, actorClient actorPb.ActorClient,
) *DebitCardAttributesFetcher {
	return &DebitCardAttributesFetcher{
		cardProvisioningClient: cardProvisioningClient,
		payClient:              payClient,
		usersClient:            usersClient,
		actorClient:            actorClient,
	}
}

// nolint dupl
func (c *DebitCardAttributesFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	var (
		actorId = req.GetActorId()
	)
	debitCardSummary, err := c.getDebitCardSummary(ctx, actorId, req.GetRequestMetadata(), req.GetFieldMask())
	if err != nil {
		logger.Error(ctx, "failed to get debit card summary for content api", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return nil, err
	}

	fieldToUserAttributesMap := map[string]*moengage.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_Summary:
			fieldToUserAttributesMap[string(UserAttributesReqField_Summary)] = &moengage.UserAttribute{
				ValueTypes: &moengage.UserAttribute_DebitCardSummary{
					DebitCardSummary: debitCardSummary,
				},
			}
		case UserAttributesReqField_Addresses:
			fieldToUserAttributesMap[string(UserAttributesReqField_Addresses)] = &moengage.UserAttribute{
				ValueTypes: &moengage.UserAttribute_DebitCardSummary{
					DebitCardSummary: debitCardSummary,
				},
			}
		default:
			logger.Info(ctx, "unsupported parameter field encountered in debit card attributes fetcher", zap.String("field", string(field)))
		}
	}
	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func (c *DebitCardAttributesFetcher) getDebitCardSummary(ctx context.Context, actorId string, metadata map[RequestMetadataKey]string, fieldMask []UserAttributesReqField) (*moengage.DebitCardSummary, error) {
	var (
		res = &moengage.DebitCardSummary{}
		err error
	)

	if len(fieldMask) == 0 {
		return nil, errors.New("No field mask provided")
	}

	if lo.Contains(fieldMask, UserAttributesReqField_Addresses) {
		res.PermanentAddress, res.CommunicationAddress, err = c.fetchAddressesForActor(ctx, actorId)
		if err != nil {
			return nil, err
		}
	}

	if lo.Contains(fieldMask, UserAttributesReqField_Summary) {
		res.ZeroForexMarkupSavedTillNow, res.ZeroForexMarkupSavedForLastSevenDays, err = c.getForexRefundsAggregates(ctx, actorId)
		if err != nil {
			return nil, err
		}

		res.DomesticTransactionsAmount, res.InternationalTransactionsAmount, err = c.getTxnsAmountAggregates(ctx, actorId, metadata)
		if err != nil {
			return nil, err
		}
	}

	return res, nil
}

func (d *DebitCardAttributesFetcher) fetchAddressesForActor(ctx context.Context, actorId string) (string, string, error) {
	actorResp, errResp := d.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if err := epifigrpc.RPCError(actorResp, errResp); err != nil {
		return "", "", errors.Wrap(err, "error in fetching actor by id")
	}

	addressResp, addressErr := d.usersClient.GetAllAddresses(ctx, &userPb.GetAllAddressesRequest{
		UserId: actorResp.GetActor().GetEntityId(),
		Format: types.AddressFormat_USER_READABLE,
	})
	if err := epifigrpc.RPCError(addressResp, addressErr); err != nil {
		return "", "", errors.Wrap(err, "error in fetching addresses for actor")
	}

	return getPermanentAddress(addressResp.GetAddresses()), getCommunicationAddress(addressResp.GetAddresses()), nil
}

func getCommunicationAddress(addresses map[string]*types.Addresses) string {
	communicationAddress, ok := addresses[types.AddressType_MAILING.String()]
	if !ok {
		return ""
	}

	if len(communicationAddress.GetAddresses()) == 0 {
		return ""
	}

	return addressPkg.ConvertPostalAddressToTitleString(communicationAddress.GetAddresses()[0])
}

func getPermanentAddress(addresses map[string]*types.Addresses) string {
	permanentAddress, ok := addresses[types.AddressType_PERMANENT.String()]
	if !ok {
		return ""
	}

	if len(permanentAddress.GetAddresses()) == 0 {
		return ""
	}

	return addressPkg.ConvertPostalAddressToTitleString(permanentAddress.GetAddresses()[0])
}

func (c *DebitCardAttributesFetcher) getForexRefundsAggregates(ctx context.Context, actorId string) (string, string, error) {
	var (
		forexRefundAmount = &moneypb.Money{
			CurrencyCode: "INR",
		}
		forexRefundAmountPastSevenDays = &moneypb.Money{
			CurrencyCode: "INR",
		}
		sevenDaysAgo = time.Now().AddDate(0, 0, -7)
	)
	forexRefunds, err := c.cardProvisioningClient.GetForexRefundsByActorId(ctx, &provisioning.GetForexRefundsByActorIdRequest{
		ActorId: actorId,
		RefundStatuses: []cardEnumsPb.RefundStatus{
			cardEnumsPb.RefundStatus_REFUND_STATUS_COMPLETED,
		},
	})

	if fetchErr := epifigrpc.RPCError(forexRefunds, err); fetchErr != nil {
		if forexRefunds.GetStatus().IsRecordNotFound() {
			return "", "", errors.Wrap(epifierrors.ErrRecordNotFound, "No record found for the user")
		}
		return "", "", errors.Wrap(fetchErr, "Error while fetching transactions from card provisioning client ")
	}

	for _, forexRefundTxn := range forexRefunds.GetRefunds() {
		refundCompletionTime := forexRefundTxn.GetUpdatedAt().AsTime()
		if refundCompletionTime.After(sevenDaysAgo) {
			forexRefundAmountPastSevenDays, err = money.Sum(forexRefundAmountPastSevenDays, forexRefundTxn.GetRefundAmount())
			if err != nil {
				return "", "", errors.Wrap(err, "error while summing all forex refund amounts")
			}
		}

		forexRefundAmount, err = money.Sum(forexRefundAmount, forexRefundTxn.GetRefundAmount())
		if err != nil {
			return "", "", errors.Wrap(err, "error while summing all forex refund amounts ")
		}
	}
	return money.ToDisplayStringInIndianFormat(forexRefundAmount, 2, true),
		money.ToDisplayStringInIndianFormat(forexRefundAmountPastSevenDays, 2, true), nil
}

//nolint:funlen
func (c *DebitCardAttributesFetcher) getTxnsAmountAggregates(ctx context.Context, actorId string, metadata map[RequestMetadataKey]string) (string, string, error) {
	var (
		dcTxnsAmountSum              *moneypb.Money
		dcInternationalTxnsAmountSum *moneypb.Money
		dcDomesticTxnsAmountSum      *moneypb.Money
	)

	if len(metadata) == 0 {
		return "₹0.00", "₹0.00", nil
	}

	toTime, fromTime, err := getToAndFromTimestampsFromMetadata(metadata)
	if err != nil {
		return "", "", err
	}

	dcTxnsRes, err := c.payClient.GetTransactionAggregates(ctx, &pay.GetTransactionAggregatesRequest{
		ActorId:             actorId,
		AccountingEntryType: paymentPb.AccountingEntryType_DEBIT,
		FromTime:            fromTime,
		ToTime:              toTime,
		PaymentProtocol:     []paymentPb.PaymentProtocol{paymentPb.PaymentProtocol_CARD},
		TransactionsStatus:  []paymentPb.TransactionStatus{paymentPb.TransactionStatus_SUCCESS},
		Provenance:          []order.OrderProvenance{order.OrderProvenance_ATM, order.OrderProvenance_ECOMM, order.OrderProvenance_POS},
		AccountTypes:        []accounts.Type{accounts.Type_SAVINGS},
	})

	if fetchErr := epifigrpc.RPCError(dcTxnsRes, err); fetchErr != nil {
		if dcTxnsRes.GetStatus().IsRecordNotFound() {
			return "", "", errors.Wrap(epifierrors.ErrRecordNotFound, "No record found for the user")
		}
		return "", "", errors.Wrap(fetchErr, "Error while fetching transactions from pay client ")
	}

	dcTxnsAmountSum = dcTxnsRes.GetTransactionAggregates().GetSumAmount()
	logger.Info(ctx, "count of all dc transactions for actor :", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.COUNT, strconv.Itoa((int)(dcTxnsRes.GetTransactionAggregates().GetCount()))))

	dcInternationalTxnsRes, err := c.payClient.GetTransactionAggregates(ctx, &pay.GetTransactionAggregatesRequest{
		ActorId:             actorId,
		AccountingEntryType: paymentPb.AccountingEntryType_DEBIT,
		FromTime:            fromTime,
		ToTime:              toTime,
		PaymentProtocol:     []paymentPb.PaymentProtocol{paymentPb.PaymentProtocol_CARD},
		TransactionsStatus:  []paymentPb.TransactionStatus{paymentPb.TransactionStatus_SUCCESS},
		Provenance:          []order.OrderProvenance{order.OrderProvenance_ATM, order.OrderProvenance_ECOMM, order.OrderProvenance_POS},
		OrderTag:            []order.OrderTag{order.OrderTag_INTERNATIONAL},
		AccountTypes:        []accounts.Type{accounts.Type_SAVINGS},
	})

	if fetchErr := epifigrpc.RPCError(dcInternationalTxnsRes, err); fetchErr != nil {
		if dcInternationalTxnsRes.GetStatus().IsRecordNotFound() {
			return "", "", errors.Wrap(epifierrors.ErrRecordNotFound, "No record found for the user")
		}
		return "", "", errors.Wrap(fetchErr, "Error while fetching international transactions from pay client ")
	}

	dcInternationalTxnsAmountSum = dcInternationalTxnsRes.GetTransactionAggregates().GetSumAmount()
	logger.Info(ctx, "count of international dc transactions for actor :", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.COUNT, strconv.Itoa((int)(dcInternationalTxnsRes.GetTransactionAggregates().GetCount()))))
	dcDomesticTxnsAmountSum, err = money.Subtract(dcTxnsAmountSum, dcInternationalTxnsAmountSum)
	if err != nil {
		return "", "", fmt.Errorf("error in calculating amount aggregate for domestic dc txns")
	}
	return money.ToDisplayStringInIndianFormat(dcDomesticTxnsAmountSum, 2, true),
		money.ToDisplayStringInIndianFormat(dcInternationalTxnsAmountSum, 2, true), nil
}

func getToAndFromTimestampsFromMetadata(metadata map[RequestMetadataKey]string) (*timestampPb.Timestamp, *timestampPb.Timestamp, error) {
	var (
		fromTime *timestampPb.Timestamp
		toTime   *timestampPb.Timestamp
	)
	durationString, containsDuration := metadata[RequestMetadataKey_Duration]
	fromDateString, containsFromDate := metadata[RequestMetaadataKey_FromDate]
	toDateString, containsToDate := metadata[RequestMetaadataKey_ToDate]

	switch {
	case containsDuration:
		duration, err := time.ParseDuration(durationString)
		if err != nil {
			return nil, nil, fmt.Errorf("error while parsing duration, err: %w", err)
		}
		fromTime = timestampPb.New(time.Now().Add(-duration))
		toTime = timestampPb.Now()
	case containsFromDate:
		var parseDateErr error
		fromTime, parseDateErr = parseDate(fromDateString)
		if parseDateErr != nil {
			return nil, nil, fmt.Errorf("error in parsing %s: %w", RequestMetaadataKey_FromDate, parseDateErr)
		}
		if containsToDate {
			toTime, parseDateErr = parseDate(toDateString)
			if parseDateErr != nil {
				return nil, nil, fmt.Errorf("error in parsing %s: %w", RequestMetaadataKey_ToDate, parseDateErr)
			}
		} else {
			toTime = timestampPb.Now()
		}
	default:
		return nil, nil, errors.New("No recognised attributes in request metadata")
	}
	return toTime, fromTime, nil
}
