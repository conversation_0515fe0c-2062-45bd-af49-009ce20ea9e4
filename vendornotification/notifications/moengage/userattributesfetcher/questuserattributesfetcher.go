package userattributesfetcher

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/api/user"

	"go.uber.org/zap"

	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
	"github.com/epifi/be-common/pkg/logger"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

type UserQuestAttributesFetcher struct {
	questSdkClient *questSdk.Client
	usersClient    user.UsersClient
}

func NewUserQuestAttributesFetcher(questSdkClient *questSdk.Client, usersClient user.UsersClient) *UserQuestAttributesFetcher {
	return &UserQuestAttributesFetcher{
		questSdkClient: questSdkClient,
		usersClient:    usersClient,
	}
}

// nolint dupl
func (u *UserQuestAttributesFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	fieldToUserAttributesMap := map[string]*moengageVendorPb.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_Variant:
			variantId, err := u.getQuestVariant(ctx, req.GetActorId(), req.GetRequestMetadata())
			if err != nil {
				return nil, fmt.Errorf("error while getting quest variant, err: %w", err)
			}
			fieldToUserAttributesMap[string(UserAttributesReqField_Variant)] = &moengageVendorPb.UserAttribute{
				ValueTypes: &moengageVendorPb.UserAttribute_StringValue{
					StringValue: variantId,
				},
			}
		default:
			logger.Info(ctx, "unsupported parameter field encountered in quest attributes fetcher", zap.String("field", string(field)))
		}
	}
	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func (u *UserQuestAttributesFetcher) getQuestVariant(ctx context.Context, actorId string, reqMetadata map[RequestMetadataKey]string) (string, error) {
	if reqMetadata == nil || reqMetadata[RequestMetadataKey_Experiment] == "" {
		return "", fmt.Errorf("experiment id is required for fetching quest variant")
	}
	experimentId := reqMetadata[RequestMetadataKey_Experiment]

	variantResp, err := u.questSdkClient.GetVariantEvaluatedForActor(ctx, u.usersClient, experimentId, actorId)
	if err != nil {
		logger.Error(ctx, "error while fetching the quest variant for given actor", zap.String("actorId", actorId), zap.String("experimentId", experimentId))
		return "", err
	}
	return variantResp.GetId(), nil
}
