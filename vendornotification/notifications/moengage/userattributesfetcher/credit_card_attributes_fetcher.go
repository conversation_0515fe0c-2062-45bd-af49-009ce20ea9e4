package userattributesfetcher

import (
	"context"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendors/moengage"
	ffHelper "github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
)

type CreditCardAttributesFetcher struct {
	commsDataHelper *ffHelper.CommsDataHelper
}

func NewCreditCardAttributesFetcher(commsDataHelper *ffHelper.CommsDataHelper) *CreditCardAttributesFetcher {
	return &CreditCardAttributesFetcher{
		commsDataHelper: commsDataHelper,
	}
}

// nolint:dupl
func (c *CreditCardAttributesFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	var (
		actorId = req.GetActorId()
	)

	creditCardSummary, err := c.getCreditCardSummary(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "failed to get credit card summary for content api", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return nil, err
	}

	fieldToUserAttributesMap := map[string]*moengage.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_Summary:
			fieldToUserAttributesMap[string(UserAttributesReqField_Summary)] = &moengage.UserAttribute{
				ValueTypes: &moengage.UserAttribute_CreditCardSummary{
					CreditCardSummary: creditCardSummary,
				},
			}
		default:
			logger.Info(ctx, "unsupported parameter field encountered in credit card attributes fetcher", zap.String("field", string(field)))
		}
	}
	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

// nolint:funlen
func (c *CreditCardAttributesFetcher) getCreditCardSummary(ctx context.Context, actorId string) (*moengage.CreditCardSummary, error) {
	var (
		res = &moengage.CreditCardSummary{}
	)
	userDetails, err := c.commsDataHelper.GetUserDetails(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId},
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching user details")
	}

	dueInfo, err := c.commsDataHelper.GetDueInfo(ctx, &accounting.GetCreditAccountDueInformationRequest{
		GetBy: &accounting.GetCreditAccountDueInformationRequest_ActorId{ActorId: actorId},
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching dueInfo")
	}

	creditCardDetails, err := c.commsDataHelper.GetCreditCardDetails(ctx, &firefly.GetCreditCardRequest{
		GetBy: &firefly.GetCreditCardRequest_ActorId{ActorId: actorId},
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching credit card details")
	}

	limitInfo, err := c.commsDataHelper.GetLimitInfo(ctx, &accounting.GetCreditAccountLimitUtilisationRequest{
		GetBy: &accounting.GetCreditAccountLimitUtilisationRequest_ActorId{ActorId: actorId},
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching limitInfo")
	}

	ccLastFourDigits, err := ffHelper.GetCreditCardLastKDigits(creditCardDetails.GetCreditCard().GetBasicInfo().GetMaskedCardNumber(), 4)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching credit card last four digits")
	}

	billInfo, err := c.commsDataHelper.GetBillInfo(ctx, &billing.GetCreditCardBillRequest{
		GetBy: &billing.GetCreditCardBillRequest_ActorId{ActorId: actorId},
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching credit card bill for the user")
	}

	statementDate := datetime.TimestampToDateInLoc(billInfo.GetCreditCardBill().GetStatementDate(), datetime.IST)

	limitUtilizedPercent, err := ffHelper.GetLimitUtilizationPercentageString(limitInfo)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching limitUtilized percentage")
	}

	overDueDays := ffHelper.GetOverdueDays(&date.Date{
		Year:  dueInfo.GetDueDate().GetYear(),
		Month: dueInfo.GetDueDate().GetMonth(),
		Day:   dueInfo.GetDueDate().GetDay(),
	})

	res.FirstName = userDetails.GetUser().GetProfile().GetPanName().GetFirstName()
	res.BillAmount = money.ToDisplayStringInIndianFormat(dueInfo.GetTotalDueAmount(), 2, true)
	res.LastFourDigitsOfCard = ccLastFourDigits
	res.DueDate = strconv.Itoa((int)(dueInfo.GetDueDate().GetDay())) + " " + time.Month(dueInfo.GetDueDate().GetMonth()).String() + " " + strconv.Itoa((int)(dueInfo.GetDueDate().GetYear()))
	res.BillGenerationDate = strconv.Itoa((int)(statementDate.GetDay())) + " " + time.Month(statementDate.GetMonth()).String() + " " + strconv.Itoa((int)(statementDate.GetYear()))
	res.MinimumAmountDue = money.ToDisplayStringInIndianFormat(dueInfo.GetMinimumDueAmount(), 2, true)
	res.LimitUtilizedPercent = limitUtilizedPercent
	res.NumberOfDaysOverdue = strconv.Itoa(overDueDays)
	res.UnpaidMinimumAmountDue = money.ToDisplayStringInIndianFormat(dueInfo.GetUnpaidMinDue(), 2, true)
	res.UnpaidBillAmount = money.ToDisplayStringInIndianFormat(dueInfo.GetUnpaidTotalDue(), 2, true)
	res.IsMinimumAmountDuePaid = money.IsZero(dueInfo.GetMinimumDueAmount())
	res.IsBillAmountPaid = money.IsZero(dueInfo.GetUnpaidTotalDue())
	res.InterestCharges = money.ToDisplayStringInIndianFormat(dueInfo.GetInterestAccumulated(), 2, true)
	return res, nil
}
