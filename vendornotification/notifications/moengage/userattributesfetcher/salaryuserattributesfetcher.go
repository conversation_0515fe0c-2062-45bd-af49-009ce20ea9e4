package userattributesfetcher

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	salaryEnumsPb "github.com/epifi/gamma/api/salaryprogram/enums"
	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
)

var aaSalaryBandToCashbackPercent = map[salaryEnumsPb.SalaryBand]string{
	salaryEnumsPb.SalaryBand_SALARY_BAND_1: "1",
	salaryEnumsPb.SalaryBand_SALARY_BAND_2: "2",
	salaryEnumsPb.SalaryBand_SALARY_BAND_3: "3",
}

type UserSalaryAttributesFetcher struct {
	salaryServiceClient salaryPb.SalaryProgramClient
}

func NewUserSalaryAttributesFetcher(salaryServiceClient salaryPb.SalaryProgramClient) *UserSalaryAttributesFetcher {
	return &UserSalaryAttributesFetcher{salaryServiceClient: salaryServiceClient}
}

// nolint dupl
func (u *UserSalaryAttributesFetcher) GetAttributes(ctx context.Context, req *GetAttributesRequest) (*GetAttributesResponse, error) {
	fieldToUserAttributesMap := map[string]*moengageVendorPb.UserAttribute{}
	for _, field := range req.GetFieldMask() {
		switch field {
		case UserAttributesReqField_AaSalarySummary:
			aaSalarySummary, err := u.getAaSalarySummary(ctx, req.GetActorId())
			if err != nil {
				return nil, fmt.Errorf("error while getting salary summary, err: %w", err)
			}
			fieldToUserAttributesMap[string(UserAttributesReqField_AaSalarySummary)] = &moengageVendorPb.UserAttribute{
				ValueTypes: &moengageVendorPb.UserAttribute_AaSalaryProgramSummary{
					AaSalaryProgramSummary: aaSalarySummary,
				},
			}
		default:
			logger.Info(ctx, "unsupported parameter field encountered in salary attributes fetcher", zap.String("field", string(field)))
		}
	}

	return &GetAttributesResponse{
		FieldNameToAttributesMap: fieldToUserAttributesMap,
	}, nil
}

func (u *UserSalaryAttributesFetcher) getAaSalarySummary(ctx context.Context, actorId string) (*moengageVendorPb.AaSalaryProgramSummary, error) {
	summaryResponse, err := u.salaryServiceClient.GetAaSalaryDetails(ctx, &salaryPb.GetAaSalaryDetailsRequest{
		ActorId: actorId,
	})

	if rpcErr := epifigrpc.RPCError(summaryResponse, err); rpcErr != nil {
		return nil, fmt.Errorf("error in calling aaSalaryServiceClient.GetAaSalaryDetails, err: %w", rpcErr)
	}

	// transferDaysDue represents the number of days before which the user must pay dues to get activated for the upcoming activation month(upcomingActivationMonth)/ year(upcomingActivationYear).
	transferDaysDue := calculateTransferDaysDue(summaryResponse.GetUpcomingTransferCriteria().GetTransferWindowEndTime())
	daysSinceLastTransfer := calculateDaysSinceLastTransfer(summaryResponse.GetLastTransferDate())

	var (
		isAaSalaryActive                 bool
		cashbackForLatestCommittedAmount string
		latestCommittedAmount            int64
	)

	switch summaryResponse.GetCurrentStage() {
	case salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_ACTIVATED_IN_GRACE, salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_ACTIVATED:
		isAaSalaryActive = true
	default:
		isAaSalaryActive = false
	}

	if summaryResponse.GetCurrentStage() >= salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_SALARY_COMMITTED {
		cashbackForLatestCommittedAmount = aaSalaryBandToCashbackPercent[summaryResponse.GetLatestSalaryTxnVerificationRequest().GetSalaryBand()]
		latestCommittedAmount = summaryResponse.GetLatestSalaryTxnVerificationRequest().GetSalaryAmountCommitted().GetUnits()
	}

	upcomingActivationMonth := summaryResponse.GetUpcomingTransferCriteria().GetRewardActivationMonth()
	upcomingActivationYear := summaryResponse.GetUpcomingTransferCriteria().GetRewardActivationYear()

	return &moengageVendorPb.AaSalaryProgramSummary{
		LastActivatedMonth:               0,
		LastActivatedYear:                0,
		TransferDaysDue:                  transferDaysDue,
		TransferOverDueSince:             0,
		IsAaSalaryActive:                 isAaSalaryActive,
		DaysSinceLastTransfer:            daysSinceLastTransfer,
		UpcomingActivationMonth:          upcomingActivationMonth,
		UpcomingActivationYear:           upcomingActivationYear,
		CashbackForLatestCommittedAmount: cashbackForLatestCommittedAmount,
		LatestCommittedAmount:            latestCommittedAmount,
	}, nil
}

func calculateTransferDaysDue(transferWindowEndTime *timestamp.Timestamp) int32 {
	curTime := time.Now()
	dueTime := transferWindowEndTime.AsTime()
	return int32(dueTime.Sub(curTime).Hours()) / 24
}

func calculateDaysSinceLastTransfer(lastTransferDate *timestamp.Timestamp) int32 {
	lastTransferDateStartOfDay := datetime.StartOfDay(lastTransferDate.AsTime())
	curTime := time.Now().In(datetime.IST)
	return int32(curTime.Sub(lastTransferDateStartOfDay).Hours()) / 24
}
