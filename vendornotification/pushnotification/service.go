package pushnotification

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/emptypb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/comms"
	fireflyV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	userPb "github.com/epifi/gamma/api/user"
	vnPushNotificationPb "github.com/epifi/gamma/api/vendornotification/pushnotification"
	"github.com/epifi/gamma/api/vendors/pushnotification"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/pkg/jwt"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

const (
	// API names
	SendPushNotificationRequest = "SendPushNotificationRequest"

	// JWT claims field name
	requestPayloadClaimsFieldName = "request_payload"
)

type Service struct {
	vnPushNotificationPb.UnimplementedPushNotificationServer
	commsClient     comms.CommsClient
	fireflyV2Client fireflyV2Pb.FireflyV2Client
	userClient      userPb.UsersClient
	jwtVerifier     jwt.Verifier
	conf            *config.Config
}

func NewService(
	commsClient comms.CommsClient,
	fireflyV2Client fireflyV2Pb.FireflyV2Client,
	userClient userPb.UsersClient,
	conf *config.Config,
) (*Service, error) {
	jwtVerifier, err := jwt.NewVerifier(jwt.AlgorithmRS256, []string{conf.SavenRewardVnSecrets.JwtVerificationKeyBase64}, commonvgpb.Vendor_SAVEN.String())
	if err != nil {
		return nil, errors.Wrap(err, "failed to create JWT verifier")
	}

	return &Service{
		commsClient:     commsClient,
		fireflyV2Client: fireflyV2Client,
		userClient:      userClient,
		jwtVerifier:     jwtVerifier,
		conf:            conf,
	}, nil
}

func (s *Service) SendPushNotification(ctx context.Context, req *pushnotification.SendPushNotificationRequest) (*emptypb.Empty, error) {
	logger.Debug(ctx, fmt.Sprintf("%v token: %v", SendPushNotificationRequest, req.GetToken()))

	// Security check - using Saven whitelist for now, can be made vendor-specific later
	if err := security.CheckWhiteList(ctx, s.conf.SavenWhiteList, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		return nil, status.Errorf(codes.Unauthenticated, "access forbidden")
	}

	// Extract payload from JWT token
	reqPayloadString, err := s.getRequestPayloadClaimsFromJwtToken(ctx, req.GetToken())
	if err != nil {
		logger.Error(ctx, "failed to get request_payload from jwt token for SendPushNotification request", zap.Error(err))
		return nil, err
	}

	// Unmarshal payload
	requestPayloadProto := &pushnotification.SendPushNotificationRequestPayload{}
	protoUnmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	err = protoUnmarshaller.Unmarshal([]byte(reqPayloadString), requestPayloadProto)
	if err != nil {
		logger.Error(ctx, "error while unmarshalling request payload for SendPushNotification request", zap.Error(err))
		return nil, status.Errorf(codes.FailedPrecondition, "invalid request_payload")
	}

	// Log request with redaction
	redactor.LogCallbackRequestData(ctx, requestPayloadProto, SendPushNotificationRequest, requestPayloadProto.GetNotificationId(), vendorsRedactor.Config)

	// Validate request
	if valErr := s.validatePushNotificationRequest(requestPayloadProto); valErr != nil {
		logger.Error(ctx, "invalid push notification request", zap.Error(valErr))
		return nil, status.Errorf(codes.InvalidArgument, "invalid request: %v", valErr)
	}

	// Validate vendor using vendorgateway.Vendor enum
	vendor, err := s.validateVendor(requestPayloadProto.GetVendor())
	if err != nil {
		logger.Error(ctx, "got push notification request from unknown vendor",
			zap.String("VendorUserId", requestPayloadProto.GetVendorInternalUserId()),
			zap.String("NotificationId", requestPayloadProto.GetNotificationId()),
			zap.String(logger.VENDOR, requestPayloadProto.GetVendor()))
		return nil, status.Errorf(codes.Unauthenticated, "unknown vendor")
	}

	// Get user ID from vendor internal user ID
	userId, err := s.getUserIdFromVendorInternalUserId(ctx, requestPayloadProto.GetVendorInternalUserId(), vendor)
	if err != nil {
		logger.Error(ctx, "failed to get user ID",
			zap.String("VendorUserId", requestPayloadProto.GetVendorInternalUserId()),
			zap.String(logger.VENDOR, requestPayloadProto.GetVendor()),
			zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to resolve user")
	}

	// Send notification
	if err := s.sendPushNotificationToUser(ctx, userId, requestPayloadProto); err != nil {
		logger.Error(ctx, "failed to send push notification",
			zap.String("VendorUserId", requestPayloadProto.GetVendorInternalUserId()),
			zap.String(logger.VENDOR, requestPayloadProto.GetVendor()),
			zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to send notification")
	}

	logger.Info(ctx, "push notification sent successfully",
		zap.String("VendorUserId", requestPayloadProto.GetVendorInternalUserId()),
		zap.String("NotificationId", requestPayloadProto.GetNotificationId()),
		zap.String(logger.VENDOR, requestPayloadProto.GetVendor()),
		zap.String("UserId", userId))

	return &emptypb.Empty{}, nil
}

func (s *Service) validatePushNotificationRequest(req *pushnotification.SendPushNotificationRequestPayload) error {
	if req.GetVendorInternalUserId() == "" {
		return fmt.Errorf("vendor_internal_user_id is required")
	}

	if req.GetVendor() == "" {
		return fmt.Errorf("vendor is required")
	}

	if req.GetContent() == nil {
		return fmt.Errorf("notification content is required")
	}

	content := req.GetContent()
	if content.GetTitle() == "" || content.GetSubtitle() == "" {
		return fmt.Errorf("title and subtitle are required")
	}

	// Validate created_at timestamp if provided
	if req.GetCreatedAt() != "" {
		createdAt, err := time.Parse(time.RFC3339, req.GetCreatedAt())
		if err != nil {
			return fmt.Errorf("invalid created_at timestamp format, expected RFC3339: %w", err)
		}

		// Check if timestamp is not too old (e.g., older than 24 hours)
		if createdAt.Before(time.Now().Add(-24 * time.Hour)) {
			return fmt.Errorf("created_at timestamp is too old, notifications older than 24 hours are rejected")
		}
	}

	// Validate expires_at if provided
	if req.GetExpiresAt() != "" {
		expiresAt, err := time.Parse(time.RFC3339, req.GetExpiresAt())
		if err != nil {
			return fmt.Errorf("invalid expires_at timestamp format, expected RFC3339: %w", err)
		}

		// Check if notification has already expired
		if expiresAt.Before(time.Now()) {
			return fmt.Errorf("notification has already expired")
		}
	}

	return nil
}

func (s *Service) validateVendor(vendorStr string) (commonvgpb.Vendor, error) {
	// Use vendorgateway.Vendor enum for validation
	vendor, ok := commonvgpb.Vendor_value[strings.ToUpper(vendorStr)]
	if !ok {
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED, fmt.Errorf("unknown vendor: %s", vendorStr)
	}

	vendorEnum := commonvgpb.Vendor(vendor)

	// For now, only support SAVEN - can be extended later
	switch vendorEnum {
	case commonvgpb.Vendor_SAVEN:
		return vendorEnum, nil
	default:
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED, fmt.Errorf("vendor not supported for push notifications: %s", vendorStr)
	}
}

func (s *Service) getUserIdFromVendorInternalUserId(ctx context.Context, vendorInternalUserId string, vendor commonvgpb.Vendor) (string, error) {
	switch vendor {
	case commonvgpb.Vendor_SAVEN:
		// For SAVEN (credit card), use firefly client like reward service
		return s.getUserIdFromSavenVendorUserId(ctx, vendorInternalUserId)
	default:
		return "", fmt.Errorf("unsupported vendor for user ID resolution: %s", vendor.String())
	}
}

func (s *Service) getUserIdFromSavenVendorUserId(ctx context.Context, vendorInternalUserId string) (string, error) {
	// Follow the same pattern as reward service - use firefly client to get credit cards
	getCardsResp, err := s.fireflyV2Client.GetCreditCards(ctx, &fireflyV2Pb.GetCreditCardsRequest{
		Identifier: &fireflyV2Pb.GetCreditCardsRequest_ExternalUserId{
			ExternalUserId: vendorInternalUserId,
		},
		StateFilters: []ccEnumsV2Pb.CardState{ccEnumsV2Pb.CardState_CARD_STATE_CREATED},
	})
	if te := epifigrpc.RPCError(getCardsResp, err); te != nil {
		return "", fmt.Errorf("failed to get credit card for external user id: %w", te)
	}

	if len(getCardsResp.GetCreditCards()) == 0 {
		return "", fmt.Errorf("no credit cards found for vendor user id: %s", vendorInternalUserId)
	}

	actorId := getCardsResp.GetCreditCards()[0].GetActorId()

	// Convert actorId to userId using user client
	userResp, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		return "", fmt.Errorf("failed to get user by actor id %s: %w", actorId, te)
	}

	userId := userResp.GetUser().GetId()
	return userId, nil
}

func (s *Service) sendPushNotificationToUser(ctx context.Context, userId string, payload *pushnotification.SendPushNotificationRequestPayload) error {
	// Build notification message
	notification := s.buildNotificationMessage(ctx, payload)

	// Use default priority and campaign
	priority := comms.NotificationPriority_NORMAL

	// Send message
	sendResp, err := s.commsClient.SendMessage(ctx, &comms.SendMessageRequest{
		Type: comms.QoS_BEST_EFFORT,
		UserIdentifier: &comms.SendMessageRequest_UserId{
			UserId: userId,
		},
		Medium: comms.Medium_NOTIFICATION,
		Message: &comms.SendMessageRequest_Notification{
			Notification: &comms.NotificationMessage{
				Priority: priority,
				AndroidConfig: &comms.AndroidConfig{
					NotificationDelivery: comms.DeliveryQoS_IMMEDIATE,
				},
				Notification: notification,
			},
		},
		ExternalReferenceId: payload.GetNotificationId(),
	})

	if te := epifigrpc.RPCError(sendResp, err); te != nil {
		return fmt.Errorf("failed to send notification: %w", te)
	}

	logger.Info(ctx, "notification sent via comms", zap.String("message_id", sendResp.GetMessageId()))
	return nil
}

func (s *Service) buildNotificationMessage(ctx context.Context, payload *pushnotification.SendPushNotificationRequestPayload) *fcmPb.Notification {
	content := payload.GetContent()

	// Build common template fields
	commonFields := &fcmPb.CommonTemplateFields{
		Title:                   content.GetTitle(),
		Body:                    content.GetSubtitle(),
		NotificationReferenceId: payload.GetNotificationId(),
	}

	// Add icon if provided
	if content.GetIconUrl() != "" {
		commonFields.IconAttributes = &fcmPb.IconAttributes{
			IconUrl: content.GetIconUrl(),
		}
	}

	// Parse deeplink from vendor response, fallback to HOME if parsing fails or empty
	if parsedDeeplink := s.parseDeeplink(ctx, content.GetDeeplink()); parsedDeeplink != nil {
		commonFields.Deeplink = parsedDeeplink
	} else {
		// Default to HOME when vendor doesn't provide a valid deeplink
		commonFields.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HOME,
		}
	}

	// Add image if provided
	if content.GetImageUrl() != "" {
		commonFields.ImageUrlList = []string{content.GetImageUrl()}
	}

	return &fcmPb.Notification{
		NotificationType: fcmPb.NotificationType_SYSTEM_TRAY,
		NotificationTemplates: &fcmPb.Notification_SystemTrayTemplate{
			SystemTrayTemplate: &fcmPb.SystemTrayTemplate{
				CommonTemplateFields: commonFields,
			},
		},
	}
}

func (s *Service) parseDeeplink(ctx context.Context, deeplinkStr string) *deeplinkPb.Deeplink {
	// Return nil if input is empty - let caller decide on defaults
	if strings.TrimSpace(deeplinkStr) == "" {
		return nil
	}

	// Parse JSON string into Deeplink protobuf object
	deeplink := &deeplinkPb.Deeplink{}
	protoUnmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}

	err := protoUnmarshaller.Unmarshal([]byte(deeplinkStr), deeplink)
	if err != nil {
		logger.Error(ctx, "failed to parse deeplink JSON from vendor", zap.Error(err), zap.String("deeplink_str", deeplinkStr))
		return nil
	}

	// Validate that the parsed screen is not unspecified
	if deeplink.GetScreen() == deeplinkPb.Screen_DEEP_LINK_URI_UNSPECIFIED {
		logger.Error(ctx, "vendor provided deeplink with unspecified screen", zap.String("deeplink_str", deeplinkStr))
		return nil
	}

	logger.Info(ctx, "successfully parsed deeplink from vendor", zap.String(logger.SCREEN, deeplink.GetScreen().String()))

	return deeplink
}

func (s *Service) getRequestPayloadClaimsFromJwtToken(ctx context.Context, tokenString string) (string, error) {
	claims, err := s.jwtVerifier.VerifyTokenAndGetClaims(ctx, tokenString)
	if err != nil {
		return "", status.Errorf(codes.Unauthenticated, "%v", err)
	}

	requestPayload, ok := claims[requestPayloadClaimsFieldName]
	if !ok {
		return "", status.Errorf(codes.FailedPrecondition, "invalid token claims, could not find request_payload")
	}

	requestPayloadJson, err := json.Marshal(requestPayload)
	if err != nil {
		return "", status.Errorf(codes.FailedPrecondition, "error while marshalling request_payload to JSON string: %v", err)
	}

	return string(requestPayloadJson), nil
}
