package razorpay

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/multierr"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/emptypb"

	queuePb "github.com/epifi/be-common/api/queue"
	vendorPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	"github.com/epifi/gamma/api/accounts"
	pgPb "github.com/epifi/gamma/api/pay/paymentgateway"
	pgConsumerPb "github.com/epifi/gamma/api/pay/paymentgateway/consumer"
	"github.com/epifi/gamma/api/vendornotification/paymentgateway/razorpay"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vnotificationgw/serverhelper"

	razorpayConv "github.com/epifi/gamma/pkg/vendors/paymentgateway/razorpay"
)

type PgRazorpayInboundEventPublisher queue.Publisher

type Service struct {
	razorpay.UnimplementedRazorpayPaymentGatewayServer
	gconf                           *genconf.Config
	pgRazorpayInboundEventPublisher queue.Publisher
}

func NewService(gconf *genconf.Config, pgRazorpayInboundEventPublisher PgRazorpayInboundEventPublisher) *Service {
	return &Service{
		gconf:                           gconf,
		pgRazorpayInboundEventPublisher: pgRazorpayInboundEventPublisher,
	}
}

// getEventIdFromCtx fetches the razorpay eventId in a best-effort basis.
func (s *Service) getEventIdFromCtx(ctx context.Context) string {
	var eventId string

	md, isMdPresent := metadata.FromIncomingContext(ctx)
	if isMdPresent {
		data := md.Get(serverhelper.XRazorpayEventIdHeaderKey)
		if len(data) > 0 {
			eventId = data[0]
		}
	}
	return eventId
}

func (s *Service) ReceiveRazorpayEvent(ctx context.Context, req *razorpay.ReceiveRazorpayEventRequest) (*emptypb.Empty, error) {
	// TODO(Sundeep): Remove this log line once the webhooks integration is stabilised in prod
	logger.Info(ctx, "received webhook event", zap.String(logger.EVENT_ID, s.getEventIdFromCtx(ctx)), zap.String(logger.EVENT_TYPE, req.GetEvent()))

	payload, err := s.convertRazorpayEventPayloadToInternalPayload(ctx, req)
	if err != nil {
		logger.Error(ctx, "Error in generating payload for publishing to internal queue", zap.Error(err), zap.String(logger.EVENT_ID, s.getEventIdFromCtx(ctx)), zap.String(logger.EVENT_TYPE, req.GetEvent()))
		// returning minimal error message as this api is called by external party and we do not want to expose too much info to them for security reasons.
		return nil, errors.New("failure")
	}

	_, err = s.pgRazorpayInboundEventPublisher.Publish(ctx, payload)
	if err != nil {
		logger.Error(ctx, "unable to publish razorpay event to razorpay inbound event queue",
			zap.String(logger.EVENT_TYPE, req.GetEvent()), zap.Error(err),
			zap.String(logger.EVENT_ID, s.getEventIdFromCtx(ctx)))
	}
	return &emptypb.Empty{}, nil
}

func (s *Service) toPgCallbackEntity(rawEntity string) pgPb.PgEntity {
	switch rawEntity {
	case "payment":
		return pgPb.PgEntity_PG_ENTITY_PAYMENT
	case "order":
		return pgPb.PgEntity_PG_ENTITY_ORDER
	case "token":
		return pgPb.PgEntity_PG_ENTITY_TOKEN
	default:
		return pgPb.PgEntity_PG_ENTITY_UNSPECIFIED
	}
}

func (s *Service) toPgCallbackEvent(eventType string) pgConsumerPb.PgCallbackEvent {
	switch eventType {
	case "order.paid":
		return pgConsumerPb.PgCallbackEvent_PG_CALLBACK_EVENT_ORDER_PAID
	case "payment.authorized":
		return pgConsumerPb.PgCallbackEvent_PG_CALLBACK_EVENT_PAYMENT_AUTHORIZED
	case "payment.captured":
		return pgConsumerPb.PgCallbackEvent_PG_CALLBACK_EVENT_PAYMENT_CAPTURED
	case "payment.failed":
		return pgConsumerPb.PgCallbackEvent_PG_CALLBACK_EVENT_PAYMENT_FAILED
	case "token.confirmed":
		return pgConsumerPb.PgCallbackEvent_PG_CALLBACK_EVENT_MANDATE_CONFIRMED
	case "token.rejected":
		return pgConsumerPb.PgCallbackEvent_PG_CALLBACK_EVENT_MANDATE_REJECTED
	case "token.cancelled":
		return pgConsumerPb.PgCallbackEvent_PG_CALLBACK_EVENT_MANDATE_CANCELLED
	case "token.paused":
		return pgConsumerPb.PgCallbackEvent_PG_CALLBACK_EVENT_MANDATE_PAUSED
	default:
		return pgConsumerPb.PgCallbackEvent_PG_CALLBACK_EVENT_UNSPECIFIED
	}
}

func (s *Service) toAccountTypeToEnum(rawAccountType string) accounts.Type {
	switch rawAccountType {
	case "savings":
		return accounts.Type_SAVINGS
	case "current":
		return accounts.Type_CURRENT
	default:
		return accounts.Type_TYPE_UNSPECIFIED
	}
}

func (s *Service) convertVendorEntityToPayloadEntity(entityType pgPb.PgEntity, req *razorpay.ReceiveRazorpayEventRequest) (*pgConsumerPb.EntityPayload, error) {
	switch entityType {
	case pgPb.PgEntity_PG_ENTITY_TOKEN:
		return &pgConsumerPb.EntityPayload{
			EntityPayload: &pgConsumerPb.EntityPayload_PgTokenEntity{
				PgTokenEntity: &pgPb.PgTokenEntity{
					Id:        req.GetPayload().GetToken().GetTokenEntity().GetId(),
					Entity:    pgPb.PgEntity_PG_ENTITY_TOKEN,
					Token:     req.GetPayload().GetToken().GetTokenEntity().GetToken(),
					Bank:      req.GetPayload().GetToken().GetTokenEntity().GetBank(),
					Wallet:    req.GetPayload().GetToken().GetTokenEntity().GetWallet(),
					Method:    req.GetPayload().GetToken().GetTokenEntity().GetMethod(),
					Recurring: req.GetPayload().GetToken().GetTokenEntity().GetRecurring(),
					RecurringDetails: &pgPb.RecurringDetails{
						Status:        razorpayConv.GetMandateStatusEnum(req.GetPayload().GetToken().GetTokenEntity().GetRecurringDetails().GetStatus()),
						FailureReason: req.GetPayload().GetToken().GetTokenEntity().GetRecurringDetails().GetFailureReason(),
					},
					AuthType:  req.GetPayload().GetToken().GetTokenEntity().GetAuthType(),
					Mrn:       req.GetPayload().GetToken().GetTokenEntity().GetMrn(),
					UsedAt:    req.GetPayload().GetToken().GetTokenEntity().GetUsedAt(),
					CreatedAt: req.GetPayload().GetToken().GetTokenEntity().GetCreatedAt(),
					BankDetails: &pgPb.BankDetails{
						BeneficiaryName: req.GetPayload().GetToken().GetTokenEntity().GetBankDetails().GetBeneficiaryName(),
						AccountNumber:   req.GetPayload().GetToken().GetTokenEntity().GetBankDetails().GetAccountNumber(),
						Ifsc:            req.GetPayload().GetToken().GetTokenEntity().GetBankDetails().GetIfsc(),
						AccountType:     s.toAccountTypeToEnum(req.GetPayload().GetToken().GetTokenEntity().GetBankDetails().GetAccountType()),
					},
					MaxAmount:  req.GetPayload().GetToken().GetTokenEntity().GetMaxAmount(),
					ExpiredAt:  req.GetPayload().GetToken().GetTokenEntity().GetExpiredAt(),
					DccEnabled: req.GetPayload().GetToken().GetTokenEntity().GetDccEnabled(),
				},
			},
		}, nil
	case pgPb.PgEntity_PG_ENTITY_ORDER:
		return &pgConsumerPb.EntityPayload{
			EntityPayload: &pgConsumerPb.EntityPayload_PgOrderEntity{
				PgOrderEntity: &pgPb.PgOrderEntity{
					Id:         req.GetPayload().GetOrder().GetOrderEntity().GetId(),
					Entity:     pgPb.PgEntity_PG_ENTITY_ORDER,
					Amount:     req.GetPayload().GetOrder().GetOrderEntity().GetAmount(),
					AmountPaid: req.GetPayload().GetOrder().GetOrderEntity().GetAmountPaid(),
					AmountDue:  req.GetPayload().GetOrder().GetOrderEntity().GetAmountDue(),
					Currency:   req.GetPayload().GetOrder().GetOrderEntity().GetCurrency(),
					Receipt:    req.GetPayload().GetOrder().GetOrderEntity().GetReceipt(),
					OfferId:    req.GetPayload().GetOrder().GetOrderEntity().GetOfferId(),
					Status:     razorpayConv.GetVendorOrderStatusEnum(req.GetPayload().GetOrder().GetOrderEntity().GetStatus()),
					Attempts:   req.GetPayload().GetOrder().GetOrderEntity().GetAttempts(),
					CreatedAt:  req.GetPayload().GetOrder().GetOrderEntity().GetCreatedAt(),
					Token: &pgPb.Token{
						Method:          razorpayConv.GetAuthorisationMethodEnum(req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetMethod()),
						RecurringStatus: req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetRecurringStatus(),
						FailureReason:   req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetFailureReason(),
						Currency:        req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetCurrency(),
						MaxAmount:       req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetMaxAmount(),
						AuthType:        req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetAuthType(),
						ExpireAt:        req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetExpireAt(),
						BankAccount: &pgPb.BankAccount{
							Ifsc:              req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetBankAccount().GetIfsc(),
							BankName:          req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetBankAccount().GetBankName(),
							Name:              req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetBankAccount().GetName(),
							AccountNumber:     req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetBankAccount().GetAccountNumber(),
							AccountType:       s.toAccountTypeToEnum(req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetBankAccount().GetAccountNumber()),
							BeneficiaryEmail:  req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetBankAccount().GetBeneficiaryEmail(),
							BeneficiaryMobile: req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetBankAccount().GetBeneficiaryMobile(),
						},
						FirstPaymentAmount: req.GetPayload().GetOrder().GetOrderEntity().GetToken().GetFirstPaymentAmount(),
					},
				},
			},
		}, nil
	case pgPb.PgEntity_PG_ENTITY_PAYMENT:
		return &pgConsumerPb.EntityPayload{
			EntityPayload: &pgConsumerPb.EntityPayload_PgPaymentEntity{
				PgPaymentEntity: &pgPb.PgPaymentEntity{
					Id:               req.GetPayload().GetPayment().GetPaymentEntity().GetId(),
					Entity:           pgPb.PgEntity_PG_ENTITY_PAYMENT,
					Amount:           req.GetPayload().GetPayment().GetPaymentEntity().GetAmount(),
					Currency:         req.GetPayload().GetPayment().GetPaymentEntity().GetCurrency(),
					Status:           razorpayConv.GetVendorPaymentStatusEnum(req.GetPayload().GetPayment().GetPaymentEntity().GetStatus()),
					OrderId:          req.GetPayload().GetPayment().GetPaymentEntity().GetOrderId(),
					InvoiceId:        req.GetPayload().GetPayment().GetPaymentEntity().GetInvoiceId(),
					International:    req.GetPayload().GetPayment().GetPaymentEntity().GetInternational(),
					Method:           razorpayConv.GetPaymentMethodEnum(req.GetPayload().GetPayment().GetPaymentEntity().GetMethod()),
					AmountRefunded:   req.GetPayload().GetPayment().GetPaymentEntity().GetAmountRefunded(),
					RefundStatus:     razorpayConv.GetRefundStatusEnum(req.GetPayload().GetPayment().GetPaymentEntity().GetRefundStatus()),
					Captured:         req.GetPayload().GetPayment().GetPaymentEntity().GetCaptured(),
					Description:      req.GetPayload().GetPayment().GetPaymentEntity().GetDescription(),
					CardId:           req.GetPayload().GetPayment().GetPaymentEntity().GetCardId(),
					Bank:             req.GetPayload().GetPayment().GetPaymentEntity().GetBank(),
					Wallet:           req.GetPayload().GetPayment().GetPaymentEntity().GetWallet(),
					Vpa:              req.GetPayload().GetPayment().GetPaymentEntity().GetVpa(),
					Email:            req.GetPayload().GetPayment().GetPaymentEntity().GetEmail(),
					Contact:          req.GetPayload().GetPayment().GetPaymentEntity().GetContact(),
					CustomerId:       req.GetPayload().GetPayment().GetPaymentEntity().GetCustomerId(),
					TokenId:          req.GetPayload().GetPayment().GetPaymentEntity().GetTokenId(),
					Fee:              req.GetPayload().GetPayment().GetPaymentEntity().GetFee(),
					Tax:              req.GetPayload().GetPayment().GetPaymentEntity().GetTax(),
					ErrorCode:        req.GetPayload().GetPayment().GetPaymentEntity().GetErrorCode(),
					ErrorDescription: req.GetPayload().GetPayment().GetPaymentEntity().GetErrorDescription(),
					ErrorSource:      req.GetPayload().GetPayment().GetPaymentEntity().GetErrorSource(),
					ErrorStep:        req.GetPayload().GetPayment().GetPaymentEntity().GetErrorStep(),
					ErrorReason:      req.GetPayload().GetPayment().GetPaymentEntity().GetErrorReason(),
					AcquirerData: &pgPb.AcquirerData{
						Rrn:               req.GetPayload().GetPayment().GetPaymentEntity().GetAcquirerData().GetRrn(),
						AuthRefNumber:     req.GetPayload().GetPayment().GetPaymentEntity().GetAcquirerData().GetAuthRefNumber(),
						BankTransactionId: req.GetPayload().GetPayment().GetPaymentEntity().GetAcquirerData().GetBankTransactionId(),
						AuthCode:          req.GetPayload().GetPayment().GetPaymentEntity().GetAcquirerData().GetAuthCode(),
					},
					CreatedAt: req.GetPayload().GetPayment().GetPaymentEntity().GetCreatedAt(),
				},
			},
		}, nil
	default:
		return nil, fmt.Errorf("unsupported entity type: %s", entityType.String())
	}
}

func (s *Service) convertRazorpayEventPayloadToInternalPayload(ctx context.Context, req *razorpay.ReceiveRazorpayEventRequest) (*pgConsumerPb.ProcessPaymentGatewayWebhookEventRequest, error) {
	res := &pgConsumerPb.ProcessPaymentGatewayWebhookEventRequest{
		RequestHeader: &queuePb.ConsumerRequestHeader{},
		Event:         s.toPgCallbackEvent(req.GetEvent()),
		EntitiesContained: lo.Map(req.GetEntitiesContained(), func(entity string, _ int) pgPb.PgEntity {
			return s.toPgCallbackEntity(entity)
		}),
		Vendor: vendorPb.Vendor_RAZORPAY,
	}

	if res.GetEvent() == pgConsumerPb.PgCallbackEvent_PG_CALLBACK_EVENT_UNSPECIFIED {
		logger.Error(ctx, "Received unhandled event type",
			zap.String(logger.EVENT_TYPE, req.GetEvent()),
			zap.String(logger.EVENT_ID, s.getEventIdFromCtx(ctx)))
		return nil, fmt.Errorf("received unsupported callback event type, %s", req.GetEvent())
	}

	var entityConversionErr error
	for _, entity := range res.GetEntitiesContained() {
		convertedEntity, err := s.convertVendorEntityToPayloadEntity(entity, req)
		if err != nil {
			logger.Error(ctx, "Error in converting webhook payload to internal entity",
				zap.String(logger.EVENT_TYPE, req.GetEvent()), zap.Error(err),
				zap.String(logger.EVENT_ID, s.getEventIdFromCtx(ctx)))
			entityConversionErr = multierr.Append(entityConversionErr, err)
			continue
		}
		res.EntityPayloads = append(res.GetEntityPayloads(), convertedEntity)
	}
	if entityConversionErr != nil {
		return nil, fmt.Errorf("error in converting received payload in webhook: %w", entityConversionErr)
	}

	return res, nil
}
