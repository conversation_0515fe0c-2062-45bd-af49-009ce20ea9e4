package riskcovry

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"strconv"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	healthinsurancePb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	consumerPb "github.com/epifi/gamma/api/salaryprogram/healthinsurance/consumer"
	riskcovryVnPb "github.com/epifi/gamma/api/vendornotification/healthinsurance/riskcovry"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/api/vendors/riskcovry"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type Service struct {
	conf                         *config.Config
	healthInsuranceClient        healthinsurancePb.HealthInsuranceClient
	policyIssueCallbackPublisher queue.Publisher
}

type HealthInsurancePolicyIssuanceEventPublisher queue.Publisher

func NewService(conf *config.Config, healthInsuranceClient healthinsurancePb.HealthInsuranceClient, policyIssueCallbackPublisher HealthInsurancePolicyIssuanceEventPublisher) *Service {
	return &Service{conf: conf, healthInsuranceClient: healthInsuranceClient, policyIssueCallbackPublisher: policyIssueCallbackPublisher}
}

// compile time check to make sure Service implements riskcovryVnPb.RiskcovryCallbackServer
var _ riskcovryVnPb.RiskcovryCallbackServer = &Service{}

const (
	RiskcovryPolicyIssueCallback                = "RiskcovryPolicyIssue"
	RiskcovryPolicyPurchaseVerificationCallBack = "RiskcovryPolicyPurchaseVerification"
)

func (s *Service) ProcessPolicyPurchaseVerificationCallBack(ctx context.Context, req *riskcovry.PolicyPurchaseVerificationCallbackRequest) (*emptypb.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, RiskcovryPolicyPurchaseVerificationCallBack, req.GetExtTransactionId(), vendorsRedactor.Config)

	if err := security.CheckWhiteList(ctx, s.conf.RiskcovryWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		logger.Error(ctx, "IP whitelisting check failed for RiskcovryPolicyPurchaseVerificationCallBack", zap.Error(err))
		return nil, err
	}
	purchaseVerificationRes, err := s.healthInsuranceClient.ProcessPolicyPurchaseVerificationCallback(ctx, &healthinsurancePb.PolicyPurchaseVerificationCallbackRequest{
		PolicyVendor:              commonvgpb.Vendor_RISKCOVRY,
		PolicyIssuanceVendorReqId: req.GetExtTransactionId(),
		RequestMetadata: &healthinsurancePb.PolicyPurchaseVerificationRequestMetadata{
			UserId:      req.GetProposer().GetCustomerId(),
			ProductCode: req.GetProductCode(),
		},
	})
	if rpcErr := epifigrpc.RPCError(purchaseVerificationRes, err); rpcErr != nil {
		logger.Error(ctx, "healthInsuranceClient.ProcessPolicyPurchaseVerificationCallback rpc call failed", zap.String(logger.REQUEST_ID, req.GetExtTransactionId()), zap.String("vendorUserId", req.GetProposer().GetCustomerId()), zap.Error(rpcErr))
		// intentionally return rpcErr to send a non-ok http status code to the vendor in this case.
		return nil, errors.New("transient failure")
	}
	if !purchaseVerificationRes.GetIsPurchaseAllowed() {
		logger.Info(ctx, "policy purchase verification failed, policy purchase is not allowed", zap.String(logger.REQUEST_ID, req.GetExtTransactionId()))
		// intentionally return rpcErr to send a non-ok http status code to the vendor in this case.
		return nil, errors.New("policy purchase is not allowed")
	}
	return &emptypb.Empty{}, nil
}

func (s *Service) ProcessPolicyIssueCallback(ctx context.Context, req *riskcovry.PolicyIssuanceCallbackRequest) (*emptypb.Empty, error) {
	redactor.LogCallbackRequestData(ctx, req, RiskcovryPolicyIssueCallback, req.GetExternalTxnId(), vendorsRedactor.Config)

	if err := security.CheckWhiteList(ctx, s.conf.RiskcovryWhitelist, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
		logger.Error(ctx, "IP whitelisting check failed for RiskcovryPolicyIssueCallback", zap.Error(err))
		return nil, err
	}

	vendorReqId := req.GetExternalTxnId()

	sumAssured, err := strconv.ParseFloat(req.GetSumAssured(), 32)
	if err != nil {
		logger.Error(ctx, "error typecasting sum_assured field to float type", zap.String("sumAssured", req.GetSumAssured()), zap.String(logger.REQUEST_ID, vendorReqId), zap.Error(err))
		return nil, errors.New("error parsing sum_assured field")
	}

	premium, err := strconv.ParseFloat(req.GetBasePremium(), 32)
	if err != nil {
		logger.Error(ctx, "error typecasting premium field to float type", zap.String("premium", req.GetBasePremium()), zap.String(logger.REQUEST_ID, vendorReqId), zap.Error(err))
		return nil, errors.New("error parsing premium field")
	}

	policyActiveFrom, err := datetime.ParseStringTimestampProtoInLocation("02/01/2006", req.GetPolicyStartDate(), datetime.IST)
	if err != nil {
		logger.Error(ctx, "error parsing policy_start_date string to timestamp", zap.String("policyStartDate", req.GetPolicyStartDate()), zap.String(logger.REQUEST_ID, vendorReqId), zap.Error(err))
		return nil, errors.New("error parsing policy_start_date")
	}

	policyActiveTill, err := datetime.ParseStringTimestampProtoInLocation("02/01/2006", req.GetPolicyExpiryDate(), datetime.IST)
	if err != nil {
		logger.Error(ctx, "error parsing policy_expiry_date string to timestamp", zap.String("policyExpiryDate", req.GetPolicyExpiryDate()), zap.String(logger.REQUEST_ID, vendorReqId), zap.Error(err))
		return nil, errors.New("error parsing policy_expiry_date")
	}

	var insuredPeople []*healthinsurancePb.PolicyMetadata_InsuredPeople
	for _, insuredPeopleVendorRes := range req.GetInsuredPeople() {
		insuredPeople = append(insuredPeople, &healthinsurancePb.PolicyMetadata_InsuredPeople{
			Name:  insuredPeopleVendorRes.GetName(),
			Email: insuredPeopleVendorRes.GetEmail(),
		})
	}

	// publish policy issuance event
	policyIssuanceEvent := &consumerPb.PolicyIssuanceCompletedEventRequest{
		PolicyVendor:              commonvgpb.Vendor_RISKCOVRY,
		PolicyIssuanceVendorReqId: req.GetExternalTxnId(),
		PolicyMetadata: &healthinsurancePb.PolicyMetadata{
			VendorPolicyId:       req.GetRiskcovryId(),
			VendorParentPolicyId: req.GetInitialRiskcovryId(),
			PolicyActiveFrom:     timestampPb.New(datetime.StartOfDay(policyActiveFrom.AsTime())),
			PolicyActiveTill:     timestampPb.New(datetime.EndOfDay(policyActiveTill.AsTime())),
			SumAssured:           moneyPb.ParseFloat(sumAssured, moneyPb.RupeeCurrencyCode),
			Premium:              moneyPb.ParseFloat(premium, moneyPb.RupeeCurrencyCode),
			Coi:                  req.GetCoi(),
			CertificateNumber:    req.GetCertificateNo(),
			InsuredPeople:        insuredPeople,
		},
	}
	if _, err := s.policyIssueCallbackPublisher.Publish(ctx, policyIssuanceEvent); err != nil {
		logger.Error(ctx, "error publishing policy issuance callback event", zap.String(logger.REQUEST_ID, vendorReqId), zap.Error(err))
		return nil, errors.New("transient error")
	}

	return &emptypb.Empty{}, nil
}
