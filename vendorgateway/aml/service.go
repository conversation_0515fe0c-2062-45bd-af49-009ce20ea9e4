package aml

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/vendorgateway/config"
)

const ctxKeyReqOwner = "reqOwner"

type Service struct {
	handler        *vendorapi.HTTPRequestHandler
	conf           *config.Aml
	secureExchange *SecureExchange
}

func NewService(
	handler *vendorapi.HTTPRequestHandler,
	conf *config.Aml,
	secureExchange *SecureExchange,
) *Service {
	return &Service{
		handler:        handler,
		conf:           conf,
		secureExchange: secureExchange,
	}
}

func (s *Service) ScreenCustomer(ctx context.Context, req *aml.ScreenCustomerRequest) (*aml.ScreenCustomerResponse, error) {
	reqFactoryMap, err := s.getRequestFactoryMap(req.GetHeader().GetVendor(), req)
	if err != nil {
		logger.Error(ctx, "error getting request factory map", zap.Error(err))
		return &aml.ScreenCustomerResponse{Status: rpcPb.StatusInternal()}, nil
	}
	vendorReq, err := vendorapi.NewVendorRequest(req, reqFactoryMap)
	if err != nil {
		logger.Error(ctx, "error while getting vendor request", zap.Error(err))
		return &aml.ScreenCustomerResponse{Status: rpcPb.StatusInternal()}, nil
	}

	res, err := s.handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing ScreenCustomer request", zap.Error(err))
		return &aml.ScreenCustomerResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*aml.ScreenCustomerResponse), nil
}

func (s *Service) InitiateScreening(ctx context.Context, req *aml.InitiateScreeningRequest) (*aml.InitiateScreeningResponse, error) {
	// Note: Context is populated with req owner to be used by cryptor to fetch the correct private key for encryption
	// nolint:staticcheck
	ctx = context.WithValue(ctx, ctxKeyReqOwner, req.GetOwner().String())
	reqFactoryMap, err := s.getRequestFactoryMap(req.GetHeader().GetVendor(), req)
	if err != nil {
		logger.Error(ctx, "error getting request factory map", zap.Error(err))
		return &aml.InitiateScreeningResponse{Status: rpcPb.StatusInternal()}, nil
	}
	vendorReq, err := vendorapi.NewVendorRequest(req, reqFactoryMap)
	if err != nil {
		logger.Error(ctx, "error getting vendor request", zap.Error(err))
		return &aml.InitiateScreeningResponse{Status: rpcPb.StatusInternal()}, nil
	}
	res, err := s.handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error handling request", zap.Error(err))
		return &aml.InitiateScreeningResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*aml.InitiateScreeningResponse), nil
}

func (s *Service) ListCases(ctx context.Context, req *aml.ListCasesRequest) (*aml.ListCasesResponse, error) {
	reqFactoryMap, err := s.getRequestFactoryMap(req.GetHeader().GetVendor(), req)
	if err != nil {
		logger.Error(ctx, "error getting request factory map", zap.Error(err))
		return &aml.ListCasesResponse{Status: rpcPb.StatusInternal()}, nil
	}
	vendorReq, err := vendorapi.NewVendorRequest(req, reqFactoryMap)
	if err != nil {
		logger.Error(ctx, "error getting vendor request", zap.Error(err))
		return &aml.ListCasesResponse{Status: rpcPb.StatusInternal()}, nil
	}
	res, err := s.handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error handling request", zap.Error(err))
		return &aml.ListCasesResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*aml.ListCasesResponse), nil
}

func (s *Service) GetCaseDetails(ctx context.Context, req *aml.GetCaseDetailsRequest) (*aml.GetCaseDetailsResponse, error) {
	reqFactoryMap, err := s.getRequestFactoryMap(req.GetHeader().GetVendor(), req)
	if err != nil {
		logger.Error(ctx, "error getting request factory map", zap.Error(err))
		return &aml.GetCaseDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	vendorReq, err := vendorapi.NewVendorRequest(req, reqFactoryMap)
	if err != nil {
		logger.Error(ctx, "error getting vendor request", zap.Error(err))
		return &aml.GetCaseDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	res, err := s.handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error handling request", zap.Error(err))
		return &aml.GetCaseDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return res.(*aml.GetCaseDetailsResponse), nil
}
