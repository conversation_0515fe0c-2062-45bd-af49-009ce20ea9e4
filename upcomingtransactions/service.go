package upcomingtransactions

import (
	"context"
	"fmt"
	"sort"
	"strconv"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	pb "github.com/epifi/gamma/api/upcomingtransactions"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/gamma/upcomingtransactions/config/genconf"
	"github.com/epifi/gamma/upcomingtransactions/transactionhandler"
)

type Service struct {
	pb.UnimplementedUpcomingTransactionsServer
	fitTxnHandler         transactionhandler.IFitTransactionHandler
	dsPredictedTxnHandler transactionhandler.IDsPredictedTransactionHandler
	config                *genconf.Config
}

func NewService(fitTxnHandler transactionhandler.IFitTransactionHandler, dsPredictedTxnHandler transactionhandler.IDsPredictedTransactionHandler,
	config *genconf.Config) *Service {
	return &Service{
		fitTxnHandler:         fitTxnHandler,
		dsPredictedTxnHandler: dsPredictedTxnHandler,
		config:                config,
	}
}

func (s *Service) GetUpcomingTxnsForActor(ctx context.Context, req *pb.GetUpcomingTxnsForActorRequest) (*pb.GetUpcomingTxnsForActorResponse, error) {
	var (
		upcomingTxns, dsUpcomingTxns, fitttUpcomingTxns []*pb.UpcomingTransaction
	)
	if len(req.GetTxnSources()) == 0 {
		logger.Error(ctx, "txn source cannot be empty")
		return &pb.GetUpcomingTxnsForActorResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("txn source cannot be empty"),
		}, nil
	}
	errGrp, gctx := errgroup.WithContext(ctx)

	// setting default from time to cur time if not present in request
	if req.GetFromTime() == nil {
		req.FromTime = timestamppb.Now()
	}

	for _, source := range lo.Uniq(req.GetTxnSources()) {
		switch source {
		case modelPb.TxnSource_TXN_SOURCE_DS:
			if s.config.DisableDSSuggestions() {
				continue
			}
			errGrp.Go(func() error {
				txns, err := s.dsPredictedTxnHandler.GetDsSuggestedUpcomingTxns(gctx, req.GetActorId(), req.GetFromTime(), req.GetToTime(), req.GetAccountingEntry())
				dsUpcomingTxns = txns
				return err
			})
		case modelPb.TxnSource_TXN_SOURCE_FITTT:
			errGrp.Go(func() error {
				txns, err := s.fitTxnHandler.GetFitUpcomingTxnsForActor(gctx, req.GetActorId(), req.GetFromTime(), req.GetToTime())
				fitttUpcomingTxns = txns
				return err
			})
		default:
			logger.Error(ctx, fmt.Sprintf("unhandled txn source in request : %s", source.String()))
			return &pb.GetUpcomingTxnsForActorResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("unhandled txn source in request : %s", source.String())),
			}, nil
		}
	}

	if gErr := errGrp.Wait(); gErr != nil {
		logger.Error(ctx, "failed to get upcoming txns", zap.Error(gErr))
		return &pb.GetUpcomingTxnsForActorResponse{
			Status: rpc.StatusInternalWithDebugMsg(gErr.Error()),
		}, nil
	}

	upcomingTxns = append(upcomingTxns, dsUpcomingTxns...)
	upcomingTxns = append(upcomingTxns, fitttUpcomingTxns...)

	// For debugging https://monorail.pointz.in/p/fi-app/issues/detail?id=96120
	logUpcomingTransactionsInfo(ctx, dsUpcomingTxns, fitttUpcomingTxns)

	// sort all txns primarily on min time (increasing order) and secondarily on max time
	sort.Slice(upcomingTxns, func(i, j int) bool {
		if upcomingTxns[i].GetMinTime().AsTime().Equal(upcomingTxns[j].GetMinTime().AsTime()) {
			return upcomingTxns[i].GetMaxTime().AsTime().Before(upcomingTxns[j].GetMaxTime().AsTime())
		}
		return upcomingTxns[i].GetMinTime().AsTime().Before(upcomingTxns[j].GetMinTime().AsTime())
	})
	return &pb.GetUpcomingTxnsForActorResponse{
		Status:       rpc.StatusOk(),
		Transactions: upcomingTxns,
	}, nil
}

func logUpcomingTransactionsInfo(ctx context.Context, dsUpcomingTxns, fitttUpcomingTxns []*pb.UpcomingTransaction) {
	dsTxnCount := len(dsUpcomingTxns)
	fitttTxnCount := len(fitttUpcomingTxns)
	fitttTxnUniqueIds := make(map[string]bool)
	entityComboUniqueIds := make(map[string]bool)

	repeats := []string{}
	entityComboRepeats := []string{}
	for _, txn := range fitttUpcomingTxns {
		subId := txn.GetMetadata().GetFitTxnMetadata().GetSubscriptionId()
		if _, ok := fitttTxnUniqueIds[subId]; ok {
			repeats = append(repeats, subId)
			continue
		}
		fitttTxnUniqueIds[subId] = true
	}

	// Check for duplicate EntityType and DerivedEntityId combinations
	for _, txn := range append(dsUpcomingTxns, fitttUpcomingTxns...) {
		entityCombo := fmt.Sprintf("%s-%s", txn.GetEntityType(), txn.GetDerivedEntityId())
		if _, ok := entityComboUniqueIds[entityCombo]; ok {
			entityComboRepeats = append(entityComboRepeats, entityCombo)
			continue
		}
		entityComboUniqueIds[entityCombo] = true
	}

	logger.Info(ctx, "Upcoming transactions info",
		zap.Int("ds_txns", dsTxnCount),
		zap.Int("fittt_txns", fitttTxnCount),
		zap.Int("fittt_unique_txns", len(fitttTxnUniqueIds)),
		zap.Strings("repeats", repeats),
		zap.Strings("entity_combo_repeats", entityComboRepeats),
		zap.String(logger.RESULT, strconv.FormatBool(len(repeats) == 0 && len(entityComboRepeats) == 0)),
	)
}
