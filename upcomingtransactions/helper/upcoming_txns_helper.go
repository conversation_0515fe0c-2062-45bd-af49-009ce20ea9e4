//go:generate mockgen -source=upcoming_txns_helper.go -destination=./mocks/mock_upcoming_txns_helper.go package=mocks
package helper

import (
	"context"
	"fmt"

	"github.com/google/wire"

	"github.com/epifi/gamma/api/actor"
	actorPb "github.com/epifi/gamma/api/actor"
	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/investment/mutualfund"
	mfCatalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	merchantPb "github.com/epifi/gamma/api/merchant"
	upcomingTxnsPb "github.com/epifi/gamma/api/upcomingtransactions"
	"github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/gamma/pkg/investment"
)

var UpcomingTransactionsHelperWireSet = wire.NewSet(NewUpcomingTransactionsHelperImpl, wire.Bind(new(IUpcomingTransactionsHelper), new(*UpcomingTransactionsHelperImpl)))

type IUpcomingTransactionsHelper interface {
	// GetEntityDetailsForUpcomingTxns returns entity details for derived entity in upcoming txns
	// response is a map with key as derived entity id and value as entity details
	GetEntityDetailsForUpcomingTxns(ctx context.Context, upTxns []*upcomingTxnsPb.UpcomingTransaction) (map[string]*EntityDetails, error)
}

type UpcomingTransactionsHelperImpl struct {
	actorClient            actorPb.ActorClient
	merchantClient         merchantPb.MerchantServiceClient
	mfCatalogManagerClient mfCatalogPb.CatalogManagerClient
	depositClient          depositPb.DepositClient
}

func NewUpcomingTransactionsHelperImpl(actorClient actorPb.ActorClient, merchantClient merchantPb.MerchantServiceClient,
	mfCatalogManagerClient mfCatalogPb.CatalogManagerClient, depositClient depositPb.DepositClient) *UpcomingTransactionsHelperImpl {
	return &UpcomingTransactionsHelperImpl{
		actorClient:            actorClient,
		merchantClient:         merchantClient,
		mfCatalogManagerClient: mfCatalogManagerClient,
		depositClient:          depositClient,
	}
}

func (h *UpcomingTransactionsHelperImpl) GetEntityDetailsForUpcomingTxns(ctx context.Context, upTxns []*upcomingTxnsPb.UpcomingTransaction) (map[string]*EntityDetails, error) {
	var (
		merchantTxns, actorTxns, mfTxns, sdTxns []*upcomingTxnsPb.UpcomingTransaction
	)
	for _, txn := range upTxns {
		switch txn.GetEntityType() {
		case model.EntityType_ENTITY_TYPE_MERCHANT:
			merchantTxns = append(merchantTxns, txn)
		case model.EntityType_ENTITY_TYPE_ACTOR:
			actorTxns = append(actorTxns, txn)
		case model.EntityType_ENTITY_TYPE_MUTUAL_FUND:
			mfTxns = append(mfTxns, txn)
		case model.EntityType_ENTITY_TYPE_SD_DEPOSIT_ACCOUNT:
			sdTxns = append(sdTxns, txn)
		default:
			return nil, fmt.Errorf("invalid entity_type %s in upcoming txn", txn.GetEntityType().String())
		}
	}
	return h.getEntityDetails(ctx, actorTxns, merchantTxns, mfTxns, sdTxns)
}

func (h *UpcomingTransactionsHelperImpl) getEntityDetails(ctx context.Context, actorTxns, merchantTxns, mfTxns, sdTxns []*upcomingTxnsPb.UpcomingTransaction) (map[string]*EntityDetails, error) {
	var (
		merchantTxnsEntityDetails, actorTxnsEntityDetails, mfTxnsEntityDetails, sdTxnsEntityDetails map[string]*EntityDetails
	)
	errGrp, gCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		var err error
		merchantTxnsEntityDetails, err = h.getMerchantTxnsEntityDetails(gCtx, merchantTxns)
		if err != nil {
			return fmt.Errorf("failed to get entity details for merchant txns : %w", err)
		}
		return nil
	})
	errGrp.Go(func() error {
		var err error
		actorTxnsEntityDetails, err = h.getActorTxnsEntityDetails(gCtx, actorTxns)
		if err != nil {
			return fmt.Errorf("failed to get entity details for actor txn : %w", err)
		}
		return nil
	})
	errGrp.Go(func() error {
		var err error
		mfTxnsEntityDetails, err = h.getMfTxnEntityDetails(gCtx, mfTxns)
		if err != nil {
			return fmt.Errorf("failed to get entity details for mf txns : %w", err)
		}
		return nil
	})
	errGrp.Go(func() error {
		var err error
		sdTxnsEntityDetails, err = h.getSdTxnEntityDetails(gCtx, sdTxns)
		if err != nil {
			return fmt.Errorf("failed to get entity details for sd txns : %w", err)
		}
		return nil
	})

	if dataPopulationErr := errGrp.Wait(); dataPopulationErr != nil {
		return nil, dataPopulationErr
	}

	resEntityDetailsMap := make(map[string]*EntityDetails)

	mergeMapBIntoMapA(resEntityDetailsMap, merchantTxnsEntityDetails)
	mergeMapBIntoMapA(resEntityDetailsMap, actorTxnsEntityDetails)
	mergeMapBIntoMapA(resEntityDetailsMap, mfTxnsEntityDetails)
	mergeMapBIntoMapA(resEntityDetailsMap, sdTxnsEntityDetails)

	return resEntityDetailsMap, nil
}

type EntityDetails struct {
	Id      string
	Name    string
	IconUrl string
}

func (b *UpcomingTransactionsHelperImpl) getMerchantTxnsEntityDetails(ctx context.Context, txns []*upcomingTxnsPb.UpcomingTransaction) (map[string]*EntityDetails, error) {
	var (
		merchantIds []string
	)
	entityIdToEntityDetailsMap := make(map[string]*EntityDetails)
	if len(txns) == 0 {
		return nil, nil
	}
	for _, txn := range txns {
		merchantIds = append(merchantIds, txn.GetDerivedEntityId())
	}
	merchantsResp, merchantRespErr := b.merchantClient.GetMerchants(ctx, &merchantPb.GetMerchantsRequest{
		Identifier: &merchantPb.GetMerchantsRequest_MerchantIdentifier_{
			MerchantIdentifier: &merchantPb.GetMerchantsRequest_MerchantIdentifier{
				MerchantIds: merchantIds,
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(merchantsResp, merchantRespErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to get merchants by ids : %w", rpcErr)
	}
	for _, merchant := range merchantsResp.GetMerchants() {
		entityIdToEntityDetailsMap[merchant.GetId()] = &EntityDetails{
			Id:      merchant.GetId(),
			Name:    merchant.GetName(),
			IconUrl: merchant.GetLogoUrl(),
		}
	}
	return entityIdToEntityDetailsMap, nil
}

func (b *UpcomingTransactionsHelperImpl) getActorTxnsEntityDetails(ctx context.Context, txns []*upcomingTxnsPb.UpcomingTransaction) (map[string]*EntityDetails, error) {
	var (
		actorIds []string
	)
	entityIdToEntityDetailsMap := make(map[string]*EntityDetails)
	if len(txns) == 0 {
		return nil, nil
	}
	for _, txn := range txns {
		actorIds = append(actorIds, txn.GetDerivedEntityId())
	}
	actorsResp, actorsRespErr := b.actorClient.GetEntityDetails(ctx, &actor.GetEntityDetailsRequest{
		ActorIds: actorIds,
	})
	if rpcErr := epifigrpc.RPCError(actorsResp, actorsRespErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to get entity details by actor ids : %w", rpcErr)
	}
	for _, actor := range actorsResp.GetEntityDetails() {
		entityIdToEntityDetailsMap[actor.GetActorId()] = &EntityDetails{
			Id:      actor.GetActorId(),
			Name:    actor.GetName().ToString(),
			IconUrl: actor.GetProfileImageUrl(),
		}
	}
	return entityIdToEntityDetailsMap, nil
}

func (b *UpcomingTransactionsHelperImpl) getMfTxnEntityDetails(ctx context.Context, txns []*upcomingTxnsPb.UpcomingTransaction) (map[string]*EntityDetails, error) {
	if len(txns) == 0 {
		return nil, nil
	}
	var (
		mfIds []string
	)
	entityIdToEntityDetailsMap := make(map[string]*EntityDetails)

	for _, txn := range txns {
		mfIds = append(mfIds, txn.GetDerivedEntityId())
	}
	mfIdToMfMap, err := b.GetMfDetailsByIds(ctx, mfIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get mf details for mutual funds : %w", err)
	}

	for _, txn := range txns {
		mf, found := mfIdToMfMap[txn.GetDerivedEntityId()]
		if !found {
			return nil, fmt.Errorf("mf details not found for mf_id : %s", txn.GetDerivedEntityId())
		}
		iconUrl, found := investment.IconsForAmc[mf.GetAmc()]
		if !found {
			return nil, fmt.Errorf("failed to get icon url for amc : %s", mf.GetAmc().String())
		}
		entityIdToEntityDetailsMap[txn.GetDerivedEntityId()] = &EntityDetails{
			Id:      txn.GetDerivedEntityId(),
			Name:    mf.GetNameData().GetDisplayName(),
			IconUrl: iconUrl,
		}
	}
	return entityIdToEntityDetailsMap, nil
}

func (b *UpcomingTransactionsHelperImpl) getSdTxnEntityDetails(ctx context.Context, txns []*upcomingTxnsPb.UpcomingTransaction) (map[string]*EntityDetails, error) {
	if len(txns) == 0 {
		return nil, nil
	}
	var (
		sdIds []string
	)
	entityIdToEntityDetailsMap := make(map[string]*EntityDetails)

	for _, txn := range txns {
		sdIds = append(sdIds, txn.GetDerivedEntityId())
	}
	sfIdToSdMap, err := b.GetSdDetailsByIds(ctx, sdIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get sd details for sd accounts : %w", err)
	}

	for _, txn := range txns {
		sd, found := sfIdToSdMap[txn.GetDerivedEntityId()]
		if !found {
			return nil, fmt.Errorf("sd details not found for sd_id : %s", txn.GetDerivedEntityId())
		}
		iconUrl := sd.GetDepositIcon().GetImageUrl()
		entityIdToEntityDetailsMap[txn.GetDerivedEntityId()] = &EntityDetails{
			Id:      txn.GetDerivedEntityId(),
			Name:    sd.GetName(),
			IconUrl: iconUrl,
		}
	}
	return entityIdToEntityDetailsMap, nil
}

func (b *UpcomingTransactionsHelperImpl) GetMfDetailsByIds(ctx context.Context, mfIds []string) (map[string]*mutualfund.MutualFund, error) {
	if len(mfIds) == 0 {
		return map[string]*mutualfund.MutualFund{}, nil
	}
	uniqueMfIdsMap := make(map[string]bool)
	for _, mfId := range mfIds {
		uniqueMfIdsMap[mfId] = true
	}
	var uniqueMfIds []string
	for mfId := range uniqueMfIdsMap {
		uniqueMfIds = append(uniqueMfIds, mfId)
	}
	res, err := b.mfCatalogManagerClient.GetMutualFunds(ctx, &mfCatalogPb.GetMutualFundsRequest{
		FundIdentifier: mutualfund.MutualFundIdentifier_MUTUAL_FUND_IDENTIFIER_ID,
		Ids:            uniqueMfIds,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return map[string]*mutualfund.MutualFund{}, fmt.Errorf("failed to fetch mutual fund details for subscription : %w", rpcErr)
	}
	mfIdToMfMap := make(map[string]*mutualfund.MutualFund)
	for _, mf := range res.GetMutualFunds() {
		mfIdToMfMap[mf.GetId()] = mf
	}
	return mfIdToMfMap, nil
}

func (b *UpcomingTransactionsHelperImpl) GetSdDetailsByIds(ctx context.Context, sdIds []string) (map[string]*depositPb.DepositAccount, error) {
	if len(sdIds) == 0 {
		return map[string]*depositPb.DepositAccount{}, nil
	}
	uniqueSdIdsMap := make(map[string]bool)
	for _, sdId := range sdIds {
		uniqueSdIdsMap[sdId] = true
	}
	var uniqueSdIds []string
	for sdId := range uniqueSdIdsMap {
		uniqueSdIds = append(uniqueSdIds, sdId)
	}

	ch := make(chan *depositPb.DepositAccount, len(uniqueSdIds))
	errGrp, gctx := errgroup.WithContext(ctx)
	errGrp.SetLimit(5)
	for _, id := range uniqueSdIds {
		sdId := id
		errGrp.Go(func() error {
			//TODO: Add a batch rpc to fetch deposit (https://monorail.pointz.in/p/fi-app/issues/detail?id=63143)
			sdRes, err := b.depositClient.GetById(gctx, &depositPb.GetByIdRequest{
				Id: sdId,
			})
			if rpcErr := epifigrpc.RPCError(sdRes, err); rpcErr != nil {
				return fmt.Errorf("failed to get sd details for acc %s : %w", sdId, rpcErr)
			}
			ch <- sdRes.GetAccount()
			return nil
		})
	}
	if gErr := errGrp.Wait(); gErr != nil {
		close(ch)
		return map[string]*depositPb.DepositAccount{}, gErr
	}
	close(ch)
	sdIdToSdMap := make(map[string]*depositPb.DepositAccount)
	for sdAcc := range ch {
		sdIdToSdMap[sdAcc.GetId()] = sdAcc
	}
	return sdIdToSdMap, nil
}

func mergeMapBIntoMapA(mapA, mapB map[string]*EntityDetails) {
	for key, val := range mapB {
		mapA[key] = val
	}
}
