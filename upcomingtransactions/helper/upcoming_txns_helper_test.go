package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/actor"
	mockActor "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/deposit/mocks"
	"github.com/epifi/gamma/api/investment/mutualfund"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	mockCatalog "github.com/epifi/gamma/api/investment/mutualfund/catalog/mocks"
	"github.com/epifi/gamma/api/merchant"
	mock_merchant "github.com/epifi/gamma/api/merchant/mocks"
	upcomingTxnsPb "github.com/epifi/gamma/api/upcomingtransactions"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/be-common/pkg/mock"
)

type fields struct {
	actorClient            *mockActor.MockActorClient
	merchantClient         *mock_merchant.MockMerchantServiceClient
	mfCatalogManagerClient *mockCatalog.MockCatalogManagerClient
	depositClient          *mocks.MockDepositClient
}

func initFields(ctrl *gomock.Controller) *fields {
	return &fields{
		actorClient:            mockActor.NewMockActorClient(ctrl),
		merchantClient:         mock_merchant.NewMockMerchantServiceClient(ctrl),
		mfCatalogManagerClient: mockCatalog.NewMockCatalogManagerClient(ctrl),
		depositClient:          mocks.NewMockDepositClient(ctrl),
	}
}

func TestUpcomingTransactionsHelperImpl_GetEntityDetailsForUpcomingTxns(t *testing.T) {
	type args struct {
		ctx    context.Context
		upTxns []*upcomingTxnsPb.UpcomingTransaction
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       map[string]*EntityDetails
		wantErr    bool
	}{
		{
			name: "success fetch entity details",
			args: args{
				ctx: context.Background(),
				upTxns: []*upcomingTxnsPb.UpcomingTransaction{
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_ACTOR,
						DerivedEntityId: "actor-1",
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_ACTOR,
						DerivedEntityId: "actor-2",
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
						DerivedEntityId: "merch-1",
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
						DerivedEntityId: "merch-2",
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MUTUAL_FUND,
						DerivedEntityId: "mf-1",
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MUTUAL_FUND,
						DerivedEntityId: "mf-2",
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_SD_DEPOSIT_ACCOUNT,
						DerivedEntityId: "sd-1",
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_SD_DEPOSIT_ACCOUNT,
						DerivedEntityId: "sd-2",
					},
				},
			},
			setupMocks: func(f *fields) {
				f.actorClient.EXPECT().GetEntityDetails(gomock.Any(), &actor.GetEntityDetailsRequest{
					ActorIds: []string{"actor-1", "actor-2"},
				}).Return(&actor.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actor.GetEntityDetailsResponse_EntityDetail{
						{
							Name: &commontypes.Name{
								FirstName: "Mohit",
								LastName:  "Rathi",
							},
							ProfileImageUrl: "mohit.png",
							ActorId:         "actor-1",
						},
						{
							Name: &commontypes.Name{
								FirstName: "Ayush",
								LastName:  "Bhardwaj",
							},
							ProfileImageUrl: "ayush.png",
							ActorId:         "actor-2",
						},
					},
				}, nil)

				f.merchantClient.EXPECT().GetMerchants(gomock.Any(), &merchant.GetMerchantsRequest{
					Identifier: &merchant.GetMerchantsRequest_MerchantIdentifier_{
						MerchantIdentifier: &merchant.GetMerchantsRequest_MerchantIdentifier{
							MerchantIds: []string{"merch-1", "merch-2"},
						},
					},
				}).Return(&merchant.GetMerchantsResponse{
					Status: rpc.StatusOk(),
					Merchants: []*merchant.Merchant{
						{
							Id:          "merch-1",
							LogoUrl:     "amazon.png",
							DisplayName: "Amazon",
						},
						{
							Id:          "merch-2",
							LogoUrl:     "swiggy.png",
							DisplayName: "Swiggy",
						},
					},
				}, nil)
				f.depositClient.EXPECT().GetById(gomock.Any(), mock.NewProtoMatcher(&deposit.GetByIdRequest{
					Id: "sd-1",
				})).Return(&deposit.GetByIdResponse{
					Status: rpc.StatusOk(),
					Account: &deposit.DepositAccount{
						Id:   "sd-1",
						Name: "Account 1",
						DepositIcon: &commontypes.Image{
							ImageUrl: "acc1.png",
						},
					},
				}, nil)
				f.depositClient.EXPECT().GetById(gomock.Any(), mock.NewProtoMatcher(&deposit.GetByIdRequest{
					Id: "sd-2",
				})).Return(&deposit.GetByIdResponse{
					Status: rpc.StatusOk(),
					Account: &deposit.DepositAccount{
						Id:   "sd-2",
						Name: "Account 2",
						DepositIcon: &commontypes.Image{
							ImageUrl: "acc2.png",
						},
					},
				}, nil)

				f.mfCatalogManagerClient.EXPECT().GetMutualFunds(gomock.Any(), &catalog.GetMutualFundsRequest{
					FundIdentifier: mutualfund.MutualFundIdentifier_MUTUAL_FUND_IDENTIFIER_ID,
					Ids:            []string{"mf-1", "mf-2"},
				}).Return(&catalog.GetMutualFundsResponse{
					Status: rpc.StatusOk(),
					MutualFunds: map[string]*mutualfund.MutualFund{
						"mf-1": {
							Id:  "mf-1",
							Amc: mutualfund.Amc_RELIANCE,
							NameData: &mutualfund.FundNameMetadata{
								DisplayName: "Reliance Fund",
							},
						},
						"mf-2": {
							Id:  "mf-2",
							Amc: mutualfund.Amc_ICICI_PRUDENTIAL,
							NameData: &mutualfund.FundNameMetadata{
								DisplayName: "ICICI Fund",
							},
						},
					},
				}, nil)
			},
			want: map[string]*EntityDetails{
				"actor-1": {
					Id:      "actor-1",
					Name:    "Mohit Rathi",
					IconUrl: "mohit.png",
				},
				"actor-2": {
					Id:      "actor-2",
					Name:    "Ayush Bhardwaj",
					IconUrl: "ayush.png",
				},
				"mf-1": {
					Id:      "mf-1",
					Name:    "Reliance Fund",
					IconUrl: "https://epifi-icons.pointz.in/amc_logos/reliance.png",
				},
				"mf-2": {
					Id:      "mf-2",
					Name:    "ICICI Fund",
					IconUrl: "https://epifi-icons.pointz.in/amc_logos/ipru_logo.png",
				},
				"sd-1": {
					Id:      "sd-1",
					Name:    "Account 1",
					IconUrl: "acc1.png",
				},
				"sd-2": {
					Id:      "sd-2",
					Name:    "Account 2",
					IconUrl: "acc2.png",
				},
				"merch-1": {
					Id:      "merch-1",
					Name:    "Amazon",
					IconUrl: "amazon.png",
				},
				"merch-2": {
					Id:      "merch-2",
					Name:    "Swiggy",
					IconUrl: "swiggy.png",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			tt.setupMocks(f)
			h := NewUpcomingTransactionsHelperImpl(f.actorClient, f.merchantClient, f.mfCatalogManagerClient, f.depositClient)
			got, err := h.GetEntityDetailsForUpcomingTxns(tt.args.ctx, tt.args.upTxns)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEntityDetailsForUpcomingTxns() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetEntityDetailsForUpcomingTxns() map value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
