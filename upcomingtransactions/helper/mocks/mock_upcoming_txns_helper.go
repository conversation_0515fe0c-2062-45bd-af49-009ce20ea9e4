// Code generated by MockGen. DO NOT EDIT.
// Source: upcoming_txns_helper.go

// Package mock_helper is a generated GoMock package.
package mock_helper

import (
	context "context"
	reflect "reflect"

	upcomingtransactions "github.com/epifi/gamma/api/upcomingtransactions"
	helper "github.com/epifi/gamma/upcomingtransactions/helper"
	gomock "github.com/golang/mock/gomock"
)

// MockIUpcomingTransactionsHelper is a mock of IUpcomingTransactionsHelper interface.
type MockIUpcomingTransactionsHelper struct {
	ctrl     *gomock.Controller
	recorder *MockIUpcomingTransactionsHelperMockRecorder
}

// MockIUpcomingTransactionsHelperMockRecorder is the mock recorder for MockIUpcomingTransactionsHelper.
type MockIUpcomingTransactionsHelperMockRecorder struct {
	mock *MockIUpcomingTransactionsHelper
}

// NewMockIUpcomingTransactionsHelper creates a new mock instance.
func NewMockIUpcomingTransactionsHelper(ctrl *gomock.Controller) *MockIUpcomingTransactionsHelper {
	mock := &MockIUpcomingTransactionsHelper{ctrl: ctrl}
	mock.recorder = &MockIUpcomingTransactionsHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUpcomingTransactionsHelper) EXPECT() *MockIUpcomingTransactionsHelperMockRecorder {
	return m.recorder
}

// GetEntityDetailsForUpcomingTxns mocks base method.
func (m *MockIUpcomingTransactionsHelper) GetEntityDetailsForUpcomingTxns(ctx context.Context, upTxns []*upcomingtransactions.UpcomingTransaction) (map[string]*helper.EntityDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityDetailsForUpcomingTxns", ctx, upTxns)
	ret0, _ := ret[0].(map[string]*helper.EntityDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityDetailsForUpcomingTxns indicates an expected call of GetEntityDetailsForUpcomingTxns.
func (mr *MockIUpcomingTransactionsHelperMockRecorder) GetEntityDetailsForUpcomingTxns(ctx, upTxns interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityDetailsForUpcomingTxns", reflect.TypeOf((*MockIUpcomingTransactionsHelper)(nil).GetEntityDetailsForUpcomingTxns), ctx, upTxns)
}
