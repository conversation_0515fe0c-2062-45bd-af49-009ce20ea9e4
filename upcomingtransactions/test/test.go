package test

import (
	"log"

	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/logger"
	pkgTestv2 "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/upcomingtransactions/config"
	"github.com/epifi/gamma/upcomingtransactions/config/genconf"
)

// InitTestServer initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only

func InitTestServer() (*config.Config, *genconf.Config, *gormv2.DB, func()) {
	// Init config
	conf, err := config.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	gconf, err := genconf.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	budgetingDb, dbName, releaseFn, err := pkgTestv2.PrepareRandomScopedRdsTestDb(conf.BudgetingDb, false)
	if err != nil {
		log.Fatal("failed to connect to budgeting db", err)
	}
	conf.BudgetingDb.Name = dbName

	return conf, gconf, budgetingDb, func() {
		_ = logger.Log.Sync()
		sqlDB, _ := budgetingDb.DB()
		_ = sqlDB.Close()
		releaseFn()
	}
}
