// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/insights/utils"
	"github.com/epifi/gamma/upcomingtransactions"
	"github.com/epifi/gamma/upcomingtransactions/config/genconf"
	"github.com/epifi/gamma/upcomingtransactions/consumer"
	"github.com/epifi/gamma/upcomingtransactions/dao"
	"github.com/epifi/gamma/upcomingtransactions/developer"
	"github.com/epifi/gamma/upcomingtransactions/developer/processor"
	"github.com/epifi/gamma/upcomingtransactions/subscriptionmanager"
	"github.com/epifi/gamma/upcomingtransactions/transactionhandler"
)

// Injectors from wire.go:

func InitializeUpcomingTransactionsService(db types.BudgetingPGDB, rmsClient manager.RuleManagerClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, actorClient actor.ActorClient, config *genconf.Config) *upcomingtransactions.Service {
	fitTransactionHandlerImpl := transactionhandler.NewFitTransactionHandlerImpl(rmsClient, actorClient, recurringPaymentClient)
	upcomingTransactionDaoPgdb := dao.NewUpcomingTransactionDaoPgdb(db)
	dsPredictedTransactionHandlerImpl := transactionhandler.NewDsPredictedTransactionHandlerImpl(upcomingTransactionDaoPgdb)
	service := upcomingtransactions.NewService(fitTransactionHandlerImpl, dsPredictedTransactionHandlerImpl, config)
	return service
}

// config: {"s3Client": "DsSubscriptionsIngestionParams().S3BucketName()"}
func InitialiseUpcomingTransactionsConsumerService(db types.BudgetingPGDB, genconf2 *genconf.Config, s3Client s3.S3Client, actorClient actor.ActorClient) *consumer.ConsumerService {
	subscriptionParserImpl := consumer.NewSubscriptionParserImpl()
	upcomingTransactionDaoPgdb := dao.NewUpcomingTransactionDaoPgdb(db)
	subscriptionDaoPgdb := dao.NewSubscriptionDaoPgdb(db)
	currentTimeGeneratorImpl := utils.NewCurrentTimeGeneratorImpl()
	gormDB := types.BudgetingPGDBGormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	subscriptionManagerImpl := subscriptionmanager.NewSubscriptionManagerImpl(upcomingTransactionDaoPgdb, subscriptionDaoPgdb, currentTimeGeneratorImpl, gormTxnExecutor)
	recurringTxnsProcessorImpl := consumer.NewRecurringTxnsProcessorImpl(subscriptionParserImpl, subscriptionManagerImpl)
	consumerService := consumer.NewConsumerService(genconf2, s3Client, recurringTxnsProcessorImpl, subscriptionManagerImpl, actorClient)
	return consumerService
}

func InitializeUpcomingTransactionsDevEntityService(db types.BudgetingPGDB) *developer.UpcomingTransactionsDevService {
	upcomingTransactionDaoPgdb := dao.NewUpcomingTransactionDaoPgdb(db)
	upcomingTransaction := processor.NewUpcomingTransaction(upcomingTransactionDaoPgdb)
	subscriptionDaoPgdb := dao.NewSubscriptionDaoPgdb(db)
	subscription := processor.NewSubscription(subscriptionDaoPgdb)
	devFactory := developer.NewDevFactory(upcomingTransaction, subscription)
	upcomingTransactionsDevService := developer.NewUpcomingTransactionsDevService(devFactory)
	return upcomingTransactionsDevService
}
