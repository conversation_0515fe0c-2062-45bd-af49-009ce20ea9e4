//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cmd/types"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/actor"
	recurringPay "github.com/epifi/gamma/api/recurringpayment"
	rms "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/insights/utils"
	"github.com/epifi/gamma/upcomingtransactions"
	"github.com/epifi/gamma/upcomingtransactions/config/genconf"
	"github.com/epifi/gamma/upcomingtransactions/consumer"
	"github.com/epifi/gamma/upcomingtransactions/dao"
	"github.com/epifi/gamma/upcomingtransactions/developer"
	"github.com/epifi/gamma/upcomingtransactions/developer/processor"
	"github.com/epifi/gamma/upcomingtransactions/subscriptionmanager"
	"github.com/epifi/gamma/upcomingtransactions/transactionhandler"
)

func InitializeUpcomingTransactionsService(db types.BudgetingPGDB, rmsClient rms.RuleManagerClient, recurringPaymentClient recurringPay.RecurringPaymentServiceClient,
	actorClient actor.ActorClient, config *genconf.Config) *upcomingtransactions.Service {
	wire.Build(
		upcomingtransactions.NewService,
		transactionhandler.DsPredictedTransactionHandlerWireSet,
		transactionhandler.FitTransactionHandlerWireSet,
		dao.WireUpcomingTransactionDaoSet,
	)
	return &upcomingtransactions.Service{}
}

// config: {"s3Client": "DsSubscriptionsIngestionParams().S3BucketName()"}
func InitialiseUpcomingTransactionsConsumerService(db types.BudgetingPGDB, genconf *genconf.Config, s3Client s3.S3Client, actorClient actor.ActorClient) *consumer.ConsumerService {
	wire.Build(
		consumer.NewConsumerService,
		dao.WireUpcomingTransactionDaoSet,
		dao.WireSubscriptionDaoSet,
		utils.CurrentTimeGeneratorWireSet,
		subscriptionmanager.SubscriptionManagerWireSet,
		consumer.SubscriptionParserWireSet,
		consumer.RecurringTxnsProcessorWireSet,
		types.BudgetingPGDBGormDBProvider,
		storagev2.DefaultTxnExecutorWireSet,
	)
	return &consumer.ConsumerService{}
}

func InitializeUpcomingTransactionsDevEntityService(db types.BudgetingPGDB) *developer.UpcomingTransactionsDevService {
	wire.Build(
		developer.NewUpcomingTransactionsDevService,
		developer.NewDevFactory,
		dao.WireUpcomingTransactionDaoSet,
		dao.WireSubscriptionDaoSet,
		processor.NewUpcomingTransaction,
		processor.NewSubscription,
	)
	return &developer.UpcomingTransactionsDevService{}
}
