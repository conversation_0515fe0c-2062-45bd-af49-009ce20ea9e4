package dao

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"

	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/upcomingtransactions/dao/model"
)

var (
	actorIdEmptyErr = fmt.Errorf("actor id empty, failure")
	hashArrEmptyErr = fmt.Errorf("hash array is emprty, failure")
)
var UpcomingTransactionColNameMap = map[modelPb.UpcomingTransactionFieldMask]string{
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_ID:                  "id",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_COMPUTED_HASH:       "computed_hash",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_ACTOR_ID:            "actor_id",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_WITH_ENTITY_ID:      "with_entity_id",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_WITH_ENTITY_TYPE:    "with_entity_type",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_MIN_AMOUNT:          "min_amount",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_MAX_AMOUNT:          "max_amount",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_MIN_DATE:            "min_date",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_MAX_DATE:            "max_date",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_SUBSCRIPTION_ID:     "subscription_id",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_CREDIT_DEBIT:        "credit_debit",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_LAST_TRANSACTION_ID: "last_transaction_id",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_EXECUTION_STATUS:    "execution_status",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_SOURCE:              "source",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_TYPE:                "type",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_CREATED_AT:          "created_at",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_UPDATED_AT:          "updated_at",
	modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_DELETED_AT:          "deleted_at",
}

type UpcomingTransactionDaoPgdb struct {
	db *gorm.DB
}

func NewUpcomingTransactionDaoPgdb(db cmdtypes.BudgetingPGDB) *UpcomingTransactionDaoPgdb {
	return &UpcomingTransactionDaoPgdb{
		db: db,
	}
}

func (d *UpcomingTransactionDaoPgdb) Upsert(ctx context.Context, txns []*modelPb.UpcomingTransaction, filterOptions ...storagev2.FilterOption) error {
	defer metric_util.TrackDuration("upcomingtransactions/dao", "UpcomingTransactionDaoPgdb", "Upsert", time.Now())
	if len(txns) == 0 {
		return nil
	}
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	for _, opt := range filterOptions {
		db = opt.ApplyInGorm(db)
	}
	filterOptions = append(filterOptions)
	var modelTxns []*model.UpcomingTransaction
	for _, txn := range txns {
		modelTxns = append(modelTxns, model.NewUpcomingTransaction(txn))
	}
	if res := db.Create(modelTxns); res.Error != nil {
		return res.Error
	}
	return nil
}

func (d *UpcomingTransactionDaoPgdb) GetByActor(ctx context.Context, actorId string, filterOptions ...storagev2.FilterOption) ([]*modelPb.UpcomingTransaction, error) {
	defer metric_util.TrackDuration("upcomingtransactions/dao", "UpcomingTransactionDaoPgdb", "GetByActor", time.Now())
	if actorId == "" {
		return nil, actorIdEmptyErr
	}
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	for _, opt := range filterOptions {
		db = opt.ApplyInGorm(db)
	}
	var modelTxns []*model.UpcomingTransaction
	if err := db.Where(&model.UpcomingTransaction{ActorId: actorId}).Find(&modelTxns).Error; err != nil {
		return nil, err
	}
	return convertUpcomingTxnsModelToProto(modelTxns), nil
}

func (d *UpcomingTransactionDaoPgdb) GetByComputedHash(ctx context.Context, hashArr []string) ([]*modelPb.UpcomingTransaction, error) {
	defer metric_util.TrackDuration("upcomingtransactions/dao", "UpcomingTransactionDaoPgdb", "GetByComputedHash", time.Now())
	if len(hashArr) == 0 {
		return nil, hashArrEmptyErr
	}
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var modelTxns []*model.UpcomingTransaction
	if err := db.Where("computed_hash IN (?)", hashArr).Find(&modelTxns).Error; err != nil {
		return nil, err
	}
	return convertUpcomingTxnsModelToProto(modelTxns), nil
}

func (d *UpcomingTransactionDaoPgdb) UpdateForActor(ctx context.Context, actorId string, txn *modelPb.UpcomingTransaction, updateMask []modelPb.UpcomingTransactionFieldMask, filterOptions ...storagev2.FilterOption) error {
	defer metric_util.TrackDuration("upcomingtransactions/dao", "UpcomingTransactionDaoPgdb", "UpdateForActor", time.Now())
	if actorId == "" {
		return actorIdEmptyErr
	}
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	for _, opt := range filterOptions {
		db = opt.ApplyInGorm(db)
	}
	updateCols := getSelectColumnsForContentTemplate(updateMask)
	modelTxn := model.NewUpcomingTransaction(txn)
	if err := db.Model(modelTxn).Where("actor_id = ?", actorId).Select(updateCols).Updates(modelTxn).Error; err != nil {
		return fmt.Errorf("unable to update upcoming txns: %w", err)
	}
	return nil
}

func convertUpcomingTxnsModelToProto(models []*model.UpcomingTransaction) []*modelPb.UpcomingTransaction {
	var protos []*modelPb.UpcomingTransaction
	for _, txn := range models {
		protos = append(protos, txn.ToProto())
	}
	return protos
}

func getSelectColumnsForContentTemplate(fieldMask []modelPb.UpcomingTransactionFieldMask) []string {
	var cols []string
	for _, field := range fieldMask {
		cols = append(cols, UpcomingTransactionColNameMap[field])
	}
	return cols
}
