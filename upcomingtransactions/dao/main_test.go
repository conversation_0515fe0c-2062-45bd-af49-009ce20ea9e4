package dao_test

import (
	"flag"
	"os"
	"testing"

	dao2 "github.com/epifi/gamma/upcomingtransactions/dao"
	"github.com/epifi/gamma/upcomingtransactions/test"
)

// nolint:dogsled
// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, db, teardown := test.InitTestServer()
	upcomingTxnDTS = &UpcomingTxnDaoTestSuite{
		conf: conf,
		db:   db,
		dao:  dao2.NewUpcomingTransactionDaoPgdb(db),
	}
	subDts = &SubscriptionDaoTestSuite{
		conf: conf,
		db:   db,
		dao:  dao2.NewSubscriptionDaoPgdb(db),
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
