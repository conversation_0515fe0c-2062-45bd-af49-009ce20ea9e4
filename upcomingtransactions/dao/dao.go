//go:generate mockgen -source=dao.go -destination=./mocks/mock_dao.go package=mocks
//go:generate dao_metrics_gen .
package dao

import (
	"context"

	"github.com/google/wire"

	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

var WireUpcomingTransactionDaoSet = wire.NewSet(NewUpcomingTransactionDaoPgdb, wire.Bind(new(UpcomingTransactionDao), new(*UpcomingTransactionDaoPgdb)))
var WireSubscriptionDaoSet = wire.NewSet(NewSubscriptionDaoPgdb, wire.Bind(new(SubscriptionDao), new(*SubscriptionDaoPgdb)))

type UpcomingTransactionDao interface {
	// Upsert method is used to bulk insert upcoming transactions. If there is a conflict on we need to provide on-conflict
	// on unique field (computed_hash) with upsert fields, otherwise the upsert fails
	// refer to filter_options.go
	Upsert(ctx context.Context, txns []*modelPb.UpcomingTransaction, filterOptions ...storagev2.FilterOption) error
	// GetByActor return upcoming txns for a given actor filter by certain condition (if filter options are given)
	GetByActor(ctx context.Context, actorId string, filterOptions ...storagev2.FilterOption) ([]*modelPb.UpcomingTransaction, error)
	// GetByComputedHash returns all entries where hash is part of hashArr in input. Refer def of computed_hash in model declaration
	GetByComputedHash(ctx context.Context, hashArr []string) ([]*modelPb.UpcomingTransaction, error)
	// UpdateForActor updates txn entries for an actor (conditional on filer options)
	UpdateForActor(ctx context.Context, actorId string, txn *modelPb.UpcomingTransaction, updateMask []modelPb.UpcomingTransactionFieldMask, filterOptions ...storagev2.FilterOption) error
}

type SubscriptionDao interface {
	// Upsert method is used to bulk insert subscriptions. If there is a conflict on we need to provide on-conflict
	// on unique field (id) with upsert fields, otherwise the upsert fails
	// refer to filter_options.go
	Upsert(ctx context.Context, subs []*modelPb.Subscription, filterOptions ...storagev2.FilterOption) error
	// GetByActor returns subscriptions for the actor filtered with conditions (if filter options are provided)
	GetByActor(ctx context.Context, actorId string, filterOptions ...storagev2.FilterOption) ([]*modelPb.Subscription, error)
}
