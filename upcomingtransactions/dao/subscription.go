package dao

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"

	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/upcomingtransactions/dao/model"
)

type SubscriptionDaoPgdb struct {
	db *gorm.DB
}

func NewSubscriptionDaoPgdb(db cmdtypes.BudgetingPGDB) *SubscriptionDaoPgdb {
	return &SubscriptionDaoPgdb{
		db: db,
	}
}

func (s *SubscriptionDaoPgdb) Upsert(ctx context.Context, subs []*modelPb.Subscription, filterOptions ...storagev2.FilterOption) error {
	defer metric_util.TrackDuration("upcomingtransactions/dao", "SubscriptionDaoPgdb", "Upsert", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	for _, opt := range filterOptions {
		db = opt.ApplyInGorm(db)
	}
	filterOptions = append(filterOptions)
	var modelTxns []*model.Subscription
	for _, sub := range subs {
		if sub.GetId() == "" {
			return fmt.Errorf("subscription id is empty cannot upsert, failure")
		}
		modelTxns = append(modelTxns, model.NewSubscription(sub))
	}
	if res := db.Create(modelTxns); res.Error != nil {
		return res.Error
	}
	return nil
}

func (s *SubscriptionDaoPgdb) GetByActor(ctx context.Context, actorId string, filterOptions ...storagev2.FilterOption) ([]*modelPb.Subscription, error) {
	defer metric_util.TrackDuration("upcomingtransactions/dao", "SubscriptionDaoPgdb", "GetByActor", time.Now())
	if actorId == "" {
		return nil, actorIdEmptyErr
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	for _, opt := range filterOptions {
		db = opt.ApplyInGorm(db)
	}
	var modelTxns []*model.Subscription
	if err := db.Where(&model.Subscription{ActorId: actorId}).Find(&modelTxns).Error; err != nil {
		return nil, err
	}
	return convertSubscriptionModelToProto(modelTxns), nil
}

func convertSubscriptionModelToProto(models []*model.Subscription) []*modelPb.Subscription {
	var protos []*modelPb.Subscription
	for _, txn := range models {
		protos = append(protos, txn.ToProto())
	}
	return protos
}
