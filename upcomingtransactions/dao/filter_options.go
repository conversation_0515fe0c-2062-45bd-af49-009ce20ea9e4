package dao

import (
	"fmt"
	"strings"
	"time"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

// Can be used to run ON CONFLICT DO UPDATE queries.
// ON CONFLICT (`conflictCols`) Do Update set `updateCols` to the new values.
// Use this it when conflict columns are not part of partial unique index.
func ClauseOnConflictDoUpdateCols(conflictCols []string, updateCols []string, whereClause clause.Where) storagev2.FilterOption {
	updateColumns := make([]clause.Column, 0)
	for _, col := range conflictCols {
		updateColumns = append(updateColumns, clause.Column{Name: col})
	}
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Clauses(clause.OnConflict{
			Columns:   updateColumns,
			Where:     whereClause,
			DoUpdates: clause.AssignmentColumns(updateCols),
		})
	})
}

func ClauseOnConflictDoUpdateAll(conflictCols []string, whereClause clause.Where) storagev2.FilterOption {
	updateColumns := make([]clause.Column, 0)
	for _, col := range conflictCols {
		updateColumns = append(updateColumns, clause.Column{Name: col})
	}
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Clauses(clause.OnConflict{
			Columns:   updateColumns,
			Where:     whereClause,
			UpdateAll: true,
		})
	})
}

func MaxDateAfterTimestamp(ts *timestamp.Timestamp) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("max_date > ?", ts.AsTime())
	})
}

func WhereSubscriptionStateIs(state modelPb.SubscriptionState) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("state = ?", state)
	})
}

func WhereClauseSubscriptionStateNotIn(states ...modelPb.SubscriptionState) clause.Where {
	if len(states) == 0 {
		return clause.Where{}
	}
	statesStr := ""
	for _, state := range states {
		statesStr = strings.Join([]string{statesStr, "'", modelPb.SubscriptionState_name[int32(state)], "', "}, "")
	}
	statesStr = statesStr[:len(statesStr)-2]
	return clause.Where{
		Exprs: []clause.Expression{
			clause.Expr{
				SQL: fmt.Sprintf("subscriptions.state NOT IN (%s)", statesStr),
			},
		},
	}
}

func WhereExecutionStatusIs(status modelPb.TxnStatus) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("execution_status = ?", status)
	})
}

func WhereExecutionStatusNotInClause(statuses ...modelPb.TxnStatus) clause.Where {
	if len(statuses) == 0 {
		return clause.Where{}
	}
	str := ""
	for _, status := range statuses {
		str = strings.Join([]string{str, "'", modelPb.TxnStatus_name[int32(status)], "', "}, "")
	}
	str = str[:len(str)-2]
	return clause.Where{
		Exprs: []clause.Expression{
			clause.Expr{
				SQL: fmt.Sprintf("upcoming_transactions.execution_status NOT IN (%s)", str),
			},
		},
	}
}

func WhereSubscriptionIdIs(subId string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("subscription_id = ?", subId)
	})
}

func WhereSubscriptionIdsIn(subIds []string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("subscription_id IN (?)", subIds)
	})
}

func WhereIdIs(subId string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", subId)
	})
}

func WhereComputedHashNotIn(computedHash []string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("computed_hash NOT IN (?)", computedHash)
	})
}

func WhereMaxDateAfter(time time.Time) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("max_date >= ?", time)
	})
}

func WhereMaxDateBefore(time time.Time) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("max_date <= ?", time)
	})
}

func WhereMinDateAfter(time time.Time) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("min_date >= ?", time)
	})
}

func WhereMinDateBefore(time time.Time) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("min_date <= ?", time)
	})
}

func WhereWithEntityIdIs(withEntityId string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("with_entity_id = ?", withEntityId)
	})
}

func WhereUpcomingTxnIdIs(txnId string) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", txnId)
	})
}

func WhereCreditDebitIs(accountingEntry paymentPb.AccountingEntryType) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("credit_debit = ?", accountingEntry)
	})
}

func WhereTxnSourceIs(source modelPb.TxnSource) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("source = ?", source)
	})
}

func WhereTxnInDateRange(minDate, maxDate time.Time) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("max_date >= ? and min_date <= ?", minDate, maxDate)
	})
}
