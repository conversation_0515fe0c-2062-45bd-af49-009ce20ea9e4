package dao_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/upcomingtransactions/config"
	dao2 "github.com/epifi/gamma/upcomingtransactions/dao"
)

type SubscriptionDaoTestSuite struct {
	db   *gorm.DB
	conf *config.Config
	dao  dao2.SubscriptionDao
}

var (
	subDts *SubscriptionDaoTestSuite

	subFixture1 = &modelPb.Subscription{
		Id:                "amazon-sub",
		ActorId:           "actor-1",
		Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
		State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
		WithEntityId:      "amazon-entity",
		LastTransactionId: "txn-1",
		WithEntityType:    modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		CreatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		UpdatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		DeletedAt:         nil,
	}

	subFixture2 = &modelPb.Subscription{
		Id:                "swiggy-sub",
		ActorId:           "actor-1",
		Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_WEEKLY,
		State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_DISABLED_BY_USER,
		WithEntityId:      "swiggy-entity",
		LastTransactionId: "txn-2",
		WithEntityType:    modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		CreatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		UpdatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
	}

	sub1 = &modelPb.Subscription{
		Id:                "zomato-sub",
		ActorId:           "actor-1",
		Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
		State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_EXPIRED,
		WithEntityType:    modelPb.EntityType_ENTITY_TYPE_ACTOR,
		WithEntityId:      "zomato-actor",
		LastTransactionId: "txn-3",
	}

	sub2 = &modelPb.Subscription{
		Id:                "amazon-sub",
		ActorId:           "actor-1",
		Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
		State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_DISABLED_BY_USER,
		WithEntityId:      "amazon-entity",
		WithEntityType:    modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		LastTransactionId: "txn-4",
	}
)

func TestSubscriptionDaoPgdb_Upsert(t *testing.T) {
	t.Parallel()
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(subDts.conf.BudgetingDb, true)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}

	type args struct {
		ctx           context.Context
		subs          []*modelPb.Subscription
		filterOptions []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "success with on-conflict upsert",
			args: args{
				ctx: context.Background(),
				subs: []*modelPb.Subscription{
					sub1,
					sub2,
				},
				filterOptions: []storagev2.FilterOption{
					dao2.ClauseOnConflictDoUpdateAll([]string{"id"}, clause.Where{}),
				},
			},
			wantErr: false,
		},
		{
			name: "failed due to pk conflict",
			args: args{
				ctx: context.Background(),
				subs: []*modelPb.Subscription{
					{
						Id:        "swiggy-sub",
						ActorId:   "actor-2",
						Frequency: modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_WEEKLY,
						State:     modelPb.SubscriptionState_SUBSCRIPTION_STATE_DISABLED_BY_USER,
					},
				},
				filterOptions: nil,
			},
			wantErr: true,
		},
	}

	dao := dao2.NewSubscriptionDaoPgdb(db)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			if err := dao.Upsert(tt.args.ctx, tt.args.subs, tt.args.filterOptions...); (err != nil) != tt.wantErr {
				t.Errorf("Upsert() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSubscriptionDaoPgdb_GetByActor(t *testing.T) {
	t.Parallel()
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(subDts.conf.BudgetingDb, true)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}
	type args struct {
		ctx           context.Context
		actorId       string
		filterOptions []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*modelPb.Subscription
		wantErr bool
	}{
		{
			name: "actor id is empty, failure",
			args: args{
				ctx:           context.Background(),
				actorId:       "",
				filterOptions: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "no record found, success",
			args: args{
				ctx:           context.Background(),
				actorId:       "actor-2",
				filterOptions: nil,
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "fetch all subs, success",
			args: args{
				ctx:           context.Background(),
				actorId:       "actor-1",
				filterOptions: nil,
			},
			want: []*modelPb.Subscription{
				subFixture1,
				subFixture2,
			},
			wantErr: false,
		},
		{
			name: "fetch all subs (with filter options), success",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				filterOptions: []storagev2.FilterOption{
					dao2.WhereSubscriptionStateIs(modelPb.SubscriptionState_SUBSCRIPTION_STATE_DISABLED_BY_USER),
				},
			},
			want: []*modelPb.Subscription{
				subFixture2,
			},
			wantErr: false,
		},
	}

	dao := dao2.NewSubscriptionDaoPgdb(db)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := dao.GetByActor(tt.args.ctx, tt.args.actorId, tt.args.filterOptions...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualSubscriptionArr(got, tt.want) {
				t.Errorf("GetByActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isDeepEqualSubscription(actual, expected *modelPb.Subscription) bool {
	if expected != nil && actual != nil {
		expected.Id = actual.Id
		expected.UpdatedAt = actual.UpdatedAt
		expected.CreatedAt = actual.CreatedAt
		expected.DeletedAt = actual.DeletedAt
	}
	diff := cmp.Diff(actual, expected, protocmp.Transform())
	return diff == ""
}

func isDeepEqualSubscriptionArr(actual, expected []*modelPb.Subscription) bool {
	if len(actual) != len(expected) {
		return false
	}
	for _, expectedEntry := range expected {
		found := false
		for _, actualEntry := range actual {
			if isDeepEqualSubscription(actualEntry, expectedEntry) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}
