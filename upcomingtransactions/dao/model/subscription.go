package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
)

type Subscription struct {
	// Primary identifier in the table
	Id string `gorm:"primary_key;column:id"`
	// actor_id of user who has this subscription
	ActorId string
	// SubscriptionFrequency determines the repeating period in which a txn is bound to happen for this subscription
	Frequency modelPb.SubscriptionFrequency
	// SubscriptionState defines the state of subscription i.e. active, disabled_by_user, etc
	State modelPb.SubscriptionState
	// actor_id / merchant_id of entity involved in other side of txn
	WithEntityId string
	// type of other side entity
	WithEntityType modelPb.EntityType
	// txn id of last successful txn in the subscription
	LastTransactionId string

	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt
}

func (s *Subscription) ToProto() *modelPb.Subscription {
	p := &modelPb.Subscription{
		Id:                s.Id,
		ActorId:           s.ActorId,
		Frequency:         s.Frequency,
		State:             s.State,
		WithEntityId:      s.WithEntityId,
		WithEntityType:    s.WithEntityType,
		LastTransactionId: s.LastTransactionId,
		CreatedAt:         timestamppb.New(s.CreatedAt),
		UpdatedAt:         timestamppb.New(s.UpdatedAt),
	}
	if s.DeletedAt.Valid {
		p.DeletedAt = timestamppb.New(s.DeletedAt.Time)
	}
	return p
}

func NewSubscription(p *modelPb.Subscription) *Subscription {
	m := &Subscription{
		Id:                p.GetId(),
		ActorId:           p.GetActorId(),
		Frequency:         p.GetFrequency(),
		State:             p.GetState(),
		WithEntityId:      p.GetWithEntityId(),
		WithEntityType:    p.GetWithEntityType(),
		LastTransactionId: p.GetLastTransactionId(),
	}
	if p.GetDeletedAt().IsValid() {
		m.DeletedAt = gorm.DeletedAt{
			Time:  p.GetDeletedAt().AsTime(),
			Valid: true,
		}
	}
	return m
}
