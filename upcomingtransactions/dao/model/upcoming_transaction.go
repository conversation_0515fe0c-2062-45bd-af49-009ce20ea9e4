package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/be-common/pkg/nulltypes"
)

type UpcomingTransaction struct {
	// primary key identifier
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	// computed_hash is hash of (actor_id, with_entity_id, min_amount, max_amount, min_date, max_date)
	// this field will be used to uniquely determine an upcoming transaction
	ComputedHash string
	// actor id of user for whom the transaction is upcoming
	ActorId string
	// with entity_id is the other side entity of txn
	// with_entity_id is actor_id in case transaction is P2P and merchant_id in case of P2M/M2P
	WithEntityId string
	// with_entity_type defines the type of entity involved in other side of txn i.e. Actor or Merchant
	WithEntityType modelPb.EntityType
	// minimum expected amount for the upcoming transaction
	MinAmount *money.Money
	// maximum expected amount for the upcoming transaction
	MaxAmount *money.Money
	// min date after which upcoming transaction to expected happen
	MinDate time.Time
	// max date before which upcoming transaction to expected happen
	MaxDate time.Time
	// if upcoming transaction is part of a subscription (e.g. OTT) we have a subscription_id
	// if not, this field will be empty
	SubscriptionId nulltypes.NullString
	// defines whether the upcoming transaction is debit/credit wrt the actor
	CreditDebit payment.AccountingEntryType
	// if the upcoming transaction is part of a subscription then this field will contain the txn_id of
	// last executed txn in that subscription, otherwise it will be empty
	LastTransactionId string
	// TxnStatus determines the status of txn e.g. executed,
	ExecutionStatus modelPb.TxnStatus
	// source of upcoming transaction (whether provided by DS or via fittt subscription)
	Source modelPb.TxnSource
	// Type of txn e.g. P2P or P2M
	Type modelPb.TxnType

	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt
}

func (t *UpcomingTransaction) ToProto() *modelPb.UpcomingTransaction {
	proto := &modelPb.UpcomingTransaction{
		Id:                t.Id,
		ComputedHash:      t.ComputedHash,
		ActorId:           t.ActorId,
		WithEntityId:      t.WithEntityId,
		WithEntityType:    t.WithEntityType,
		MinAmount:         t.MinAmount.GetPb(),
		MaxAmount:         t.MaxAmount.GetPb(),
		SubscriptionId:    t.SubscriptionId.GetValue(),
		MinDate:           timestamppb.New(t.MinDate),
		MaxDate:           timestamppb.New(t.MaxDate),
		CreditDebit:       t.CreditDebit,
		LastTransactionId: t.LastTransactionId,
		ExecutionStatus:   t.ExecutionStatus,
		Source:            t.Source,
		Type:              t.Type,
		CreatedAt:         timestamppb.New(t.CreatedAt),
		UpdatedAt:         timestamppb.New(t.UpdatedAt),
	}
	if t.DeletedAt.Valid {
		proto.DeletedAt = timestamppb.New(t.DeletedAt.Time)
	}
	return proto
}

func NewUpcomingTransaction(p *modelPb.UpcomingTransaction) *UpcomingTransaction {
	m := &UpcomingTransaction{
		Id:             p.GetId(),
		ComputedHash:   p.GetComputedHash(),
		ActorId:        p.GetActorId(),
		WithEntityId:   p.GetWithEntityId(),
		WithEntityType: p.GetWithEntityType(),
		MinAmount: &money.Money{
			Pb: p.GetMinAmount(),
		},
		MaxAmount: &money.Money{
			Pb: p.GetMaxAmount(),
		},
		MinDate:           p.GetMinDate().AsTime(),
		MaxDate:           p.GetMaxDate().AsTime(),
		SubscriptionId:    nulltypes.NewNullString(p.GetSubscriptionId()),
		CreditDebit:       p.GetCreditDebit(),
		LastTransactionId: p.GetLastTransactionId(),
		ExecutionStatus:   p.GetExecutionStatus(),
		Source:            p.GetSource(),
		Type:              p.GetType(),
	}
	if p.GetDeletedAt().IsValid() {
		m.DeletedAt = gorm.DeletedAt{
			Time:  p.GetDeletedAt().AsTime(),
			Valid: true,
		}
	}
	return m
}
