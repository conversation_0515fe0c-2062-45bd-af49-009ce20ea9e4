package dao_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"

	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/be-common/pkg/datetime"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/upcomingtransactions/config"
	"github.com/epifi/gamma/upcomingtransactions/dao"
	dao2 "github.com/epifi/gamma/upcomingtransactions/dao"
)

type UpcomingTxnDaoTestSuite struct {
	db   *gorm.DB
	conf *config.Config
	dao  dao2.UpcomingTransactionDao
}

var (
	upcomingTxnDTS *UpcomingTxnDaoTestSuite

	actor1 = "actor-1"
	actor2 = "actor-2"
	jan5   = timestamppb.New(time.Date(2023, 1, 5, 0, 0, 0, 0, datetime.IST))
	jan10  = timestamppb.New(time.Date(2023, 1, 10, 0, 0, 0, 0, datetime.IST))
	jan15  = timestamppb.New(time.Date(2023, 1, 15, 0, 0, 0, 0, datetime.IST))
	feb10  = timestamppb.New(time.Date(2023, 2, 10, 10, 0, 0, 0, datetime.IST))

	utFixture1 = &modelPb.UpcomingTransaction{
		Id:             "c8e6f32b-8b8f-4276-a119-41f010e57a39",
		ComputedHash:   "1efd5a8d-7feb-48d7-bf72-3a886cce7326",
		ActorId:        actor1,
		WithEntityId:   "merchant-1",
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		MinAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		MaxAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1200,
			Nanos:        0,
		},
		MinDate:           timestamppb.New(time.Date(2023, 02, 01, 0, 0, 0, 0, time.UTC)),
		MaxDate:           timestamppb.New(time.Date(2023, 02, 10, 0, 0, 0, 0, time.UTC)),
		SubscriptionId:    "",
		CreditDebit:       payment.AccountingEntryType_CREDIT,
		LastTransactionId: "txn-101",
		ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
		Source:            modelPb.TxnSource_TXN_SOURCE_DS,
		Type:              modelPb.TxnType_TXN_TYPE_P2P,
		CreatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		UpdatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		DeletedAt:         nil,
	}
	utFixture2 = &modelPb.UpcomingTransaction{
		Id:             "a8e6f32b-8b8f-4276-a119-41f010e57a39",
		ComputedHash:   "2efd5a8d-7feb-48d7-bf72-3a886cce7326",
		ActorId:        actor1,
		WithEntityId:   "merchant-2",
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		MinAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        2000,
			Nanos:        0,
		},
		MaxAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        2200,
			Nanos:        0,
		},
		MinDate:           timestamppb.New(time.Date(2023, 02, 02, 0, 0, 0, 0, time.UTC)),
		MaxDate:           timestamppb.New(time.Date(2023, 02, 11, 0, 0, 0, 0, time.UTC)),
		SubscriptionId:    "",
		CreditDebit:       payment.AccountingEntryType_DEBIT,
		LastTransactionId: "txn-102",
		ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
		Source:            modelPb.TxnSource_TXN_SOURCE_DS,
		Type:              modelPb.TxnType_TXN_TYPE_P2P,
		CreatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		UpdatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		DeletedAt:         nil,
	}
	utFixture3 = &modelPb.UpcomingTransaction{
		Id:             "b8e6f32b-8b8f-4276-a119-41f010e57a39",
		ComputedHash:   "3efd5a8d-7feb-48d7-bf72-3a886cce7326",
		ActorId:        actor2,
		WithEntityId:   actor1,
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
		MinAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        100,
			Nanos:        0,
		},
		MaxAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        250,
			Nanos:        0,
		},
		MinDate:           timestamppb.New(time.Date(2023, 02, 03, 0, 0, 0, 0, time.UTC)),
		MaxDate:           timestamppb.New(time.Date(2023, 02, 12, 0, 0, 0, 0, time.UTC)),
		SubscriptionId:    "",
		CreditDebit:       payment.AccountingEntryType_DEBIT,
		LastTransactionId: "txn-103",
		ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
		Source:            modelPb.TxnSource_TXN_SOURCE_DS,
		Type:              modelPb.TxnType_TXN_TYPE_P2P,
		CreatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		UpdatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		DeletedAt:         nil,
	}

	txn1 = &modelPb.UpcomingTransaction{
		ComputedHash:   "06d904b7-a8cc-49ac-8fa3-ff1a34cdb831",
		ActorId:        actor1,
		WithEntityId:   actor2,
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
		MinAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        120,
			Nanos:        0,
		},
		MaxAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        200,
			Nanos:        0,
		},
		MinDate:           jan5,
		MaxDate:           jan10,
		CreditDebit:       payment.AccountingEntryType_DEBIT,
		LastTransactionId: "t-100",
		ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
		Source:            modelPb.TxnSource_TXN_SOURCE_DS,
		Type:              modelPb.TxnType_TXN_TYPE_P2P,
	}
	txn2 = &modelPb.UpcomingTransaction{
		ComputedHash:   "********-4f9d-493f-91d4-6ab01be4201c",
		ActorId:        actor1,
		WithEntityId:   actor2,
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
		MinAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        200,
			Nanos:        0,
		},
		MaxAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        250,
			Nanos:        0,
		},
		MinDate:           jan10,
		MaxDate:           jan15,
		CreditDebit:       payment.AccountingEntryType_DEBIT,
		LastTransactionId: "t-100",
		ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
		Source:            modelPb.TxnSource_TXN_SOURCE_DS,
		Type:              modelPb.TxnType_TXN_TYPE_P2P,
	}

	txn3 = &modelPb.UpcomingTransaction{
		Id:             "c8e6f32b-8b8f-4276-a119-41f010e57a39",
		ComputedHash:   "1efd5a8d-7feb-48d7-bf72-3a886cce7326",
		ActorId:        actor1,
		WithEntityId:   "merchant-1",
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		MinAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1300,
			Nanos:        0,
		},
		MaxAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1500,
			Nanos:        0,
		},
		MinDate:           timestamppb.New(time.Date(2023, 03, 01, 0, 0, 0, 0, time.UTC)),
		MaxDate:           timestamppb.New(time.Date(2023, 03, 10, 0, 0, 0, 0, time.UTC)),
		SubscriptionId:    "",
		CreditDebit:       payment.AccountingEntryType_DEBIT,
		LastTransactionId: "txn-101",
		ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
		Source:            modelPb.TxnSource_TXN_SOURCE_DS,
		Type:              modelPb.TxnType_TXN_TYPE_P2P,
		CreatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		UpdatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		DeletedAt:         nil,
	}

	txn4 = &modelPb.UpcomingTransaction{
		Id:             "a8e6f32b-8b8f-4276-a119-41f010e57a39",
		ComputedHash:   "2efd5a8d-7feb-48d7-bf72-3a886cce7326",
		ActorId:        actor1,
		WithEntityId:   "merchant-2",
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		MinAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        700,
			Nanos:        0,
		},
		MaxAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        800,
			Nanos:        0,
		},
		MinDate:           timestamppb.New(time.Date(2023, 05, 02, 0, 0, 0, 0, time.UTC)),
		MaxDate:           timestamppb.New(time.Date(2023, 05, 11, 0, 0, 0, 0, time.UTC)),
		SubscriptionId:    "",
		CreditDebit:       payment.AccountingEntryType_DEBIT,
		LastTransactionId: "txn-102",
		ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
		Source:            modelPb.TxnSource_TXN_SOURCE_DS,
		Type:              modelPb.TxnType_TXN_TYPE_P2P,
		CreatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		UpdatedAt:         timestamppb.New(time.Date(2022, 10, 25, 0, 0, 0, 0, time.UTC)),
		DeletedAt:         nil,
	}
)

func TestUpcomingTransactionDaoCrdb_Upsert(t *testing.T) {
	t.Parallel()
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(upcomingTxnDTS.conf.BudgetingDb, true)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}

	type args struct {
		ctx           context.Context
		txns          []*modelPb.UpcomingTransaction
		filterOptions []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*modelPb.UpcomingTransaction
		wantErr bool
	}{
		{
			name: "insert success",
			args: args{
				ctx: context.Background(),
				txns: []*modelPb.UpcomingTransaction{
					txn1,
				},
				filterOptions: nil,
			},
			want: []*modelPb.UpcomingTransaction{
				txn1,
			},
			wantErr: false,
		},
		{
			name: "failed to upsert, conflict on computed_hash",
			args: args{
				ctx: context.Background(),
				txns: []*modelPb.UpcomingTransaction{
					txn2,
					txn3,
				},
				filterOptions: []storagev2.FilterOption{},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success with conflict (upsert all col)",
			args: args{
				ctx: context.Background(),
				txns: []*modelPb.UpcomingTransaction{
					txn2,
					txn3,
				},
				filterOptions: []storagev2.FilterOption{
					dao2.ClauseOnConflictDoUpdateAll([]string{"computed_hash"}, clause.Where{}),
				},
			},
			want: []*modelPb.UpcomingTransaction{
				txn2,
				txn3,
			},
			wantErr: false,
		},
		{
			name: "success with conflict (upsert selected cols)",
			args: args{
				ctx: context.Background(),
				txns: []*modelPb.UpcomingTransaction{
					txn4,
				},
				filterOptions: []storagev2.FilterOption{
					dao.ClauseOnConflictDoUpdateCols([]string{"computed_hash"}, []string{"min_amount", "max_amount"}, clause.Where{}),
				},
			},
			want: []*modelPb.UpcomingTransaction{
				{
					ComputedHash:      utFixture2.GetComputedHash(),
					ActorId:           utFixture2.GetActorId(),
					WithEntityId:      utFixture2.GetWithEntityId(),
					WithEntityType:    utFixture2.GetWithEntityType(),
					MinAmount:         txn4.GetMinAmount(),
					MaxAmount:         txn4.GetMaxAmount(),
					MinDate:           utFixture2.GetMinDate(),
					MaxDate:           utFixture2.GetMaxDate(),
					SubscriptionId:    utFixture2.GetSubscriptionId(),
					CreditDebit:       utFixture2.GetCreditDebit(),
					LastTransactionId: utFixture2.GetLastTransactionId(),
					ExecutionStatus:   utFixture2.GetExecutionStatus(),
					Source:            utFixture2.GetSource(),
					Type:              utFixture2.GetType(),
				},
			},
			wantErr: false,
		},
	}

	dao := dao2.NewUpcomingTransactionDaoPgdb(db)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			err := dao.Upsert(tt.args.ctx, tt.args.txns, tt.args.filterOptions...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Upsert() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				return
			}
			var hashArr []string
			for _, txn := range tt.args.txns {
				hashArr = append(hashArr, txn.GetComputedHash())
			}
			got, gotErr := dao.GetByComputedHash(context.Background(), hashArr)
			if gotErr != nil {
				t.Errorf("Upsert() gotErr = %v, want nil", gotErr)
				return
			}
			if !isDeepEqualUpcomingTransactionsArr(tt.want, got) {
				t.Errorf("Upsert() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUpcomingTransactionDaoPgdb_GetByActor(t *testing.T) {
	t.Parallel()
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(upcomingTxnDTS.conf.BudgetingDb, true)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}

	type args struct {
		ctx           context.Context
		actorId       string
		filterOptions []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*modelPb.UpcomingTransaction
		wantErr bool
	}{
		{
			name: "actorId is empty, failure",
			args: args{
				ctx:           context.Background(),
				actorId:       "",
				filterOptions: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success fetch all records",
			args: args{
				ctx:           context.Background(),
				actorId:       actor1,
				filterOptions: nil,
			},
			want: []*modelPb.UpcomingTransaction{
				utFixture1,
				utFixture2,
			},
			wantErr: false,
		},
		{
			name: "success fetch records with filterOptions",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
				filterOptions: []storagev2.FilterOption{
					dao2.MaxDateAfterTimestamp(feb10),
				},
			},
			want: []*modelPb.UpcomingTransaction{
				utFixture2,
			},
			wantErr: false,
		},
	}
	dao := dao2.NewUpcomingTransactionDaoPgdb(db)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := dao.GetByActor(tt.args.ctx, tt.args.actorId, tt.args.filterOptions...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualUpcomingTransactionsArr(tt.want, got) {
				t.Errorf("GetByActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUpcomingTransactionDaoPgdb_GetByComputedHash(t *testing.T) {
	t.Parallel()
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(upcomingTxnDTS.conf.BudgetingDb, true)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}

	type args struct {
		ctx     context.Context
		hashArr []string
	}
	tests := []struct {
		name    string
		args    args
		want    []*modelPb.UpcomingTransaction
		wantErr bool
	}{
		{
			name: "no hash in input, failure",
			args: args{
				ctx:     context.Background(),
				hashArr: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "no record for given hash inputs",
			args: args{
				ctx: context.Background(),
				hashArr: []string{
					uuid.New().String(),
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "no record for given hash inputs",
			args: args{
				ctx: context.Background(),
				hashArr: []string{
					utFixture1.GetComputedHash(),
					utFixture2.GetComputedHash(),
				},
			},
			want: []*modelPb.UpcomingTransaction{
				utFixture2,
				utFixture1,
			},
			wantErr: false,
		},
	}

	dao := dao2.NewUpcomingTransactionDaoPgdb(db)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := dao.GetByComputedHash(tt.args.ctx, tt.args.hashArr)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByComputedHash() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualUpcomingTransactionsArr(tt.want, got) {
				t.Errorf("GetByComputedHash() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isDeepEqualUpcomingTransactions(actual, expected *modelPb.UpcomingTransaction) bool {
	if expected != nil && actual != nil {
		expected.Id = actual.Id
		expected.UpdatedAt = actual.UpdatedAt
		expected.CreatedAt = actual.CreatedAt
		expected.DeletedAt = actual.DeletedAt
	}
	diff := cmp.Diff(actual, expected, protocmp.Transform())
	return diff == ""
}

func isDeepEqualUpcomingTransactionsArr(actual, expected []*modelPb.UpcomingTransaction) bool {
	if len(actual) != len(expected) {
		return false
	}
	for _, expectedEntry := range expected {
		found := false
		for _, actualEntry := range actual {
			if isDeepEqualUpcomingTransactions(actualEntry, expectedEntry) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}

func TestUpcomingTransactionDaoPgdb_UpdateForActor(t *testing.T) {
	t.Parallel()
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(upcomingTxnDTS.conf.BudgetingDb, true)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}
	type args struct {
		ctx           context.Context
		actorId       string
		txn           *modelPb.UpcomingTransaction
		updateMask    []modelPb.UpcomingTransactionFieldMask
		filterOptions []storagev2.FilterOption
	}
	tests := []struct {
		name        string
		args        args
		resultState []*modelPb.UpcomingTransaction
		wantErr     bool
	}{
		{
			name: "actor id is empty, failure",
			args: args{
				ctx:     context.Background(),
				actorId: "",
				txn: &modelPb.UpcomingTransaction{
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
						Nanos:        0,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        1000,
					},
				},
				updateMask: []modelPb.UpcomingTransactionFieldMask{
					modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_MIN_AMOUNT,
					modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_MAX_AMOUNT,
				},
				filterOptions: nil,
			},
			wantErr: true,
		},
		{
			name: "successfully update txn",
			args: args{
				ctx:     context.Background(),
				actorId: actor2,
				txn: &modelPb.UpcomingTransaction{
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
						Nanos:        0,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        1000,
					},
				},
				updateMask: []modelPb.UpcomingTransactionFieldMask{
					modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_MIN_AMOUNT,
					modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_MAX_AMOUNT,
				},
				filterOptions: nil,
			},
			wantErr: false,
			resultState: []*modelPb.UpcomingTransaction{
				{
					ComputedHash:   utFixture3.GetComputedHash(),
					ActorId:        utFixture3.GetActorId(),
					WithEntityId:   utFixture3.GetWithEntityId(),
					WithEntityType: utFixture3.GetWithEntityType(),
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
						Nanos:        0,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        1000,
					},
					MinDate:           utFixture3.GetMinDate(),
					MaxDate:           utFixture3.GetMaxDate(),
					SubscriptionId:    utFixture3.GetSubscriptionId(),
					CreditDebit:       utFixture3.GetCreditDebit(),
					LastTransactionId: utFixture3.GetLastTransactionId(),
					ExecutionStatus:   utFixture3.GetExecutionStatus(),
					Source:            utFixture3.GetSource(),
					Type:              utFixture3.GetType(),
				},
			},
		},
		{
			name: "successfully update multiple txns",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
				txn: &modelPb.UpcomingTransaction{
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
						Nanos:        0,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        1000,
					},
				},
				updateMask: []modelPb.UpcomingTransactionFieldMask{
					modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_MIN_AMOUNT,
					modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_MAX_AMOUNT,
				},
				filterOptions: nil,
			},
			wantErr: false,
			resultState: []*modelPb.UpcomingTransaction{
				{
					ComputedHash:   utFixture1.GetComputedHash(),
					ActorId:        utFixture1.GetActorId(),
					WithEntityId:   utFixture1.GetWithEntityId(),
					WithEntityType: utFixture1.GetWithEntityType(),
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
						Nanos:        0,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        1000,
					},
					MinDate:           utFixture1.GetMinDate(),
					MaxDate:           utFixture1.GetMaxDate(),
					SubscriptionId:    utFixture1.GetSubscriptionId(),
					CreditDebit:       utFixture1.GetCreditDebit(),
					LastTransactionId: utFixture1.GetLastTransactionId(),
					ExecutionStatus:   utFixture1.GetExecutionStatus(),
					Source:            utFixture1.GetSource(),
					Type:              utFixture1.GetType(),
				},
				{
					ComputedHash:   utFixture2.GetComputedHash(),
					ActorId:        utFixture2.GetActorId(),
					WithEntityId:   utFixture2.GetWithEntityId(),
					WithEntityType: utFixture2.GetWithEntityType(),
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
						Nanos:        0,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        1000,
					},
					MinDate:           utFixture2.GetMinDate(),
					MaxDate:           utFixture2.GetMaxDate(),
					SubscriptionId:    utFixture2.GetSubscriptionId(),
					CreditDebit:       utFixture2.GetCreditDebit(),
					LastTransactionId: utFixture2.GetLastTransactionId(),
					ExecutionStatus:   utFixture2.GetExecutionStatus(),
					Source:            utFixture2.GetSource(),
					Type:              utFixture2.GetType(),
				},
			},
		},
	}
	dao := dao2.NewUpcomingTransactionDaoPgdb(db)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			if err := dao.UpdateForActor(tt.args.ctx, tt.args.actorId, tt.args.txn, tt.args.updateMask, tt.args.filterOptions...); (err != nil) != tt.wantErr {
				t.Errorf("UpdateForActor() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.wantErr {
				return
			}
			got, err := dao.GetByActor(context.Background(), tt.args.actorId)
			if err != nil {
				t.Errorf("UpdateForActor() failed to GetByActor to compare state: gotErr %v", err)
			}
			if !isDeepEqualUpcomingTransactionsArr(got, tt.resultState) {
				t.Errorf("UpdateForActor() got = %v, want = %v", got, tt.resultState)
			}
		})
	}
}
