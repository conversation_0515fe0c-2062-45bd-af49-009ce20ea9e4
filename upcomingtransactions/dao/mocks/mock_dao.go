// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mock_dao is a generated GoMock package.
package mock_dao

import (
	context "context"
	reflect "reflect"

	model "github.com/epifi/gamma/api/upcomingtransactions/model"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	gomock "github.com/golang/mock/gomock"
)

// MockUpcomingTransactionDao is a mock of UpcomingTransactionDao interface.
type MockUpcomingTransactionDao struct {
	ctrl     *gomock.Controller
	recorder *MockUpcomingTransactionDaoMockRecorder
}

// MockUpcomingTransactionDaoMockRecorder is the mock recorder for MockUpcomingTransactionDao.
type MockUpcomingTransactionDaoMockRecorder struct {
	mock *MockUpcomingTransactionDao
}

// NewMockUpcomingTransactionDao creates a new mock instance.
func NewMockUpcomingTransactionDao(ctrl *gomock.Controller) *MockUpcomingTransactionDao {
	mock := &MockUpcomingTransactionDao{ctrl: ctrl}
	mock.recorder = &MockUpcomingTransactionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUpcomingTransactionDao) EXPECT() *MockUpcomingTransactionDaoMockRecorder {
	return m.recorder
}

// GetByActor mocks base method.
func (m *MockUpcomingTransactionDao) GetByActor(ctx context.Context, actorId string, filterOptions ...storagev2.FilterOption) ([]*model.UpcomingTransaction, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActor", varargs...)
	ret0, _ := ret[0].([]*model.UpcomingTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActor indicates an expected call of GetByActor.
func (mr *MockUpcomingTransactionDaoMockRecorder) GetByActor(ctx, actorId interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActor", reflect.TypeOf((*MockUpcomingTransactionDao)(nil).GetByActor), varargs...)
}

// GetByComputedHash mocks base method.
func (m *MockUpcomingTransactionDao) GetByComputedHash(ctx context.Context, hashArr []string) ([]*model.UpcomingTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByComputedHash", ctx, hashArr)
	ret0, _ := ret[0].([]*model.UpcomingTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByComputedHash indicates an expected call of GetByComputedHash.
func (mr *MockUpcomingTransactionDaoMockRecorder) GetByComputedHash(ctx, hashArr interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByComputedHash", reflect.TypeOf((*MockUpcomingTransactionDao)(nil).GetByComputedHash), ctx, hashArr)
}

// UpdateForActor mocks base method.
func (m *MockUpcomingTransactionDao) UpdateForActor(ctx context.Context, actorId string, txn *model.UpcomingTransaction, updateMask []model.UpcomingTransactionFieldMask, filterOptions ...storagev2.FilterOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, txn, updateMask}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateForActor", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateForActor indicates an expected call of UpdateForActor.
func (mr *MockUpcomingTransactionDaoMockRecorder) UpdateForActor(ctx, actorId, txn, updateMask interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, txn, updateMask}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateForActor", reflect.TypeOf((*MockUpcomingTransactionDao)(nil).UpdateForActor), varargs...)
}

// Upsert mocks base method.
func (m *MockUpcomingTransactionDao) Upsert(ctx context.Context, txns []*model.UpcomingTransaction, filterOptions ...storagev2.FilterOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, txns}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Upsert", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockUpcomingTransactionDaoMockRecorder) Upsert(ctx, txns interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, txns}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockUpcomingTransactionDao)(nil).Upsert), varargs...)
}

// MockSubscriptionDao is a mock of SubscriptionDao interface.
type MockSubscriptionDao struct {
	ctrl     *gomock.Controller
	recorder *MockSubscriptionDaoMockRecorder
}

// MockSubscriptionDaoMockRecorder is the mock recorder for MockSubscriptionDao.
type MockSubscriptionDaoMockRecorder struct {
	mock *MockSubscriptionDao
}

// NewMockSubscriptionDao creates a new mock instance.
func NewMockSubscriptionDao(ctrl *gomock.Controller) *MockSubscriptionDao {
	mock := &MockSubscriptionDao{ctrl: ctrl}
	mock.recorder = &MockSubscriptionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSubscriptionDao) EXPECT() *MockSubscriptionDaoMockRecorder {
	return m.recorder
}

// GetByActor mocks base method.
func (m *MockSubscriptionDao) GetByActor(ctx context.Context, actorId string, filterOptions ...storagev2.FilterOption) ([]*model.Subscription, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActor", varargs...)
	ret0, _ := ret[0].([]*model.Subscription)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActor indicates an expected call of GetByActor.
func (mr *MockSubscriptionDaoMockRecorder) GetByActor(ctx, actorId interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActor", reflect.TypeOf((*MockSubscriptionDao)(nil).GetByActor), varargs...)
}

// Upsert mocks base method.
func (m *MockSubscriptionDao) Upsert(ctx context.Context, subs []*model.Subscription, filterOptions ...storagev2.FilterOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, subs}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Upsert", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockSubscriptionDaoMockRecorder) Upsert(ctx, subs interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, subs}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockSubscriptionDao)(nil).Upsert), varargs...)
}
