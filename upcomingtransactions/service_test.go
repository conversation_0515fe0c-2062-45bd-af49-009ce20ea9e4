package upcomingtransactions

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/rms/manager"
	pb "github.com/epifi/gamma/api/upcomingtransactions"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/gamma/upcomingtransactions/config/genconf"
	mock_transactionhandler "github.com/epifi/gamma/upcomingtransactions/transactionhandler/mocks"
)

var (
	feb15 = timestamppb.New(time.Date(2023, 02, 15, 0, 0, 0, 0, datetime.IST))
	feb5  = timestamppb.New(time.Date(2023, 02, 5, 0, 0, 0, 0, datetime.IST))
	feb10 = timestamppb.New(time.Date(2023, 02, 10, 0, 0, 0, 0, datetime.IST))
	feb12 = timestamppb.New(time.Date(2023, 02, 12, 0, 0, 0, 0, datetime.IST))

	txn1 = &modelPb.UpcomingTransaction{
		WithEntityId:   "swiggy-entity",
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        250,
			Nanos:        *********,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        300,
			Nanos:        *********,
		},
		MinDate:     feb5,
		MaxDate:     feb10,
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
	}
	txn2 = &modelPb.UpcomingTransaction{
		WithEntityId:   "zomato-entity",
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        400,
			Nanos:        0,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        500,
			Nanos:        0,
		},
		MinDate:     feb10,
		MaxDate:     feb12,
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
	}
	txn3 = &modelPb.UpcomingTransaction{
		WithEntityId:   "actor-2",
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        150,
			Nanos:        0,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        250,
			Nanos:        0,
		},
		MinDate:     feb5,
		MaxDate:     feb12,
		CreditDebit: paymentPb.AccountingEntryType_CREDIT,
	}

	fitttExec1 = &manager.UpcomingExecution{
		SubId:  "sub-1",
		RuleId: "rule-1",
		DebitAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		ExecutionDate: &date.Date{
			Year:  2023,
			Month: 2,
			Day:   5,
		},
		DestinationEntityName: "ABC",
	}
	fitttExec2 = &manager.UpcomingExecution{
		SubId:  "sub-2",
		RuleId: "rule-2",
		DebitAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        200,
		},
		ExecutionDate: &date.Date{
			Year:  2023,
			Month: 2,
			Day:   10,
		},
		DestinationEntityName: "ABC",
	}
	fitttExec3 = &manager.UpcomingExecution{
		SubId:  "sub-3",
		RuleId: "rule-3",
		DebitAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        300,
		},
		ExecutionDate: &date.Date{
			Year:  2023,
			Month: 2,
			Day:   12,
		},
		DestinationEntityName: "ABC",
	}
	fitttExec4 = &manager.UpcomingExecution{
		SubId:  "sub-4",
		RuleId: "rule-5",
		DebitAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        400,
		},
		ExecutionDate: &date.Date{
			Year:  2023,
			Month: 2,
			Day:   15,
		},
		DestinationEntityName: "ABC",
	}
	fitUpTxn1 = &pb.UpcomingTransaction{
		EntityType:      modelPb.EntityType_ENTITY_TYPE_SD_DEPOSIT_ACCOUNT,
		DerivedEntityId: "acc-1",
		MinTime:         feb5,
		MaxTime:         timestamppb.New(feb5.AsTime().In(datetime.IST).Add(24 * time.Hour).Add(-1 * time.Second)),
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
		TxnSource:   modelPb.TxnSource_TXN_SOURCE_FITTT,
		Metadata: &pb.TransactionMetadata{
			Type: pb.TransactionMetadataType_TRANSACTION_METADATA_TYPE_FIT,
			Metadata: &pb.TransactionMetadata_FitTxnMetadata{
				FitTxnMetadata: &pb.FitTxnMetadata{
					SubscriptionId: "sub-1",
					RuleSubscription: &manager.RuleSubscription{
						Id:      "sub-1",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_SdValue{
										SdValue: &manager.SdParamValue{
											AccountId: "acc-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_DAILY_RULE,
					},
				},
			},
		},
	}
	fitUpTxn2 = &pb.UpcomingTransaction{
		EntityType:      modelPb.EntityType_ENTITY_TYPE_MUTUAL_FUND,
		DerivedEntityId: "mf-1",
		MinTime:         feb10,
		MaxTime:         timestamppb.New(feb10.AsTime().In(datetime.IST).Add(24 * time.Hour).Add(-1 * time.Second)),
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        200,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        200,
		},
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
		TxnSource:   modelPb.TxnSource_TXN_SOURCE_FITTT,
		Metadata: &pb.TransactionMetadata{
			Type: pb.TransactionMetadataType_TRANSACTION_METADATA_TYPE_FIT,
			Metadata: &pb.TransactionMetadata_FitTxnMetadata{
				FitTxnMetadata: &pb.FitTxnMetadata{
					SubscriptionId: "sub-2",
					RuleSubscription: &manager.RuleSubscription{
						Id:      "sub-2",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_MutualFundVal{
										MutualFundVal: &manager.MutualFundValue{
											MfId: "mf-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY,
					},
				},
			},
		},
	}
	fitUpTxn3 = &pb.UpcomingTransaction{
		EntityType:      modelPb.EntityType_ENTITY_TYPE_ACTOR,
		DerivedEntityId: "actor-2",
		MinTime:         feb12,
		MaxTime:         timestamppb.New(feb12.AsTime().In(datetime.IST).Add(24 * time.Hour).Add(-1 * time.Second)),
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        300,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        300,
		},
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
		TxnSource:   modelPb.TxnSource_TXN_SOURCE_FITTT,
		Metadata: &pb.TransactionMetadata{
			Type: pb.TransactionMetadataType_TRANSACTION_METADATA_TYPE_FIT,
			Metadata: &pb.TransactionMetadata_FitTxnMetadata{
				FitTxnMetadata: &pb.FitTxnMetadata{
					SubscriptionId: "sub-3",
					RuleSubscription: &manager.RuleSubscription{
						Id:      "sub-3",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				},
			},
		},
	}
	fitUpTxn4 = &pb.UpcomingTransaction{
		EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		DerivedEntityId: "entity-1",
		MinTime:         feb15,
		MaxTime:         timestamppb.New(feb15.AsTime().In(datetime.IST).Add(24 * time.Hour).Add(-1 * time.Second)),
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        400,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        400,
		},
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
		TxnSource:   modelPb.TxnSource_TXN_SOURCE_FITTT,
		Metadata: &pb.TransactionMetadata{
			Type: pb.TransactionMetadataType_TRANSACTION_METADATA_TYPE_FIT,
			Metadata: &pb.TransactionMetadata_FitTxnMetadata{
				FitTxnMetadata: &pb.FitTxnMetadata{
					SubscriptionId: "sub-4",
					RuleSubscription: &manager.RuleSubscription{
						Id:      "sub-4",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-2",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				},
			},
		},
	}
)

type fields struct {
	fitTxnHandler         *mock_transactionhandler.MockIFitTransactionHandler
	dsPredictedTxnHandler *mock_transactionhandler.MockIDsPredictedTransactionHandler
}

func initFields(ctrl *gomock.Controller) *fields {
	return &fields{
		fitTxnHandler:         mock_transactionhandler.NewMockIFitTransactionHandler(ctrl),
		dsPredictedTxnHandler: mock_transactionhandler.NewMockIDsPredictedTransactionHandler(ctrl),
	}
}

func TestService_GetUpcomingTxnsForActor(t *testing.T) {
	gconf, _ := genconf.Load()
	logger.Init(cfg.TestEnv)
	type args struct {
		ctx context.Context
		req *pb.GetUpcomingTxnsForActorRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       *pb.GetUpcomingTxnsForActorResponse
		wantErr    bool
	}{
		{
			name: "unexpected txn source in req",
			args: args{
				ctx: context.Background(),
				req: &pb.GetUpcomingTxnsForActorRequest{
					ActorId:    "actor-1",
					FromTime:   feb5,
					ToTime:     feb15,
					TxnSources: []modelPb.TxnSource{modelPb.TxnSource_TXN_SOURCE_UNSPECIFIED},
				},
			},
			setupMocks: func(f *fields) {

			},
			want: &pb.GetUpcomingTxnsForActorResponse{
				Status:       rpc.StatusInternal(),
				Transactions: nil,
			},
			wantErr: false,
		},
		{
			name: "failure in ds predicted txn for actor",
			args: args{
				ctx: context.Background(),
				req: &pb.GetUpcomingTxnsForActorRequest{
					ActorId:  "actor-1",
					FromTime: feb5,
					ToTime:   feb15,
					TxnSources: []modelPb.TxnSource{
						modelPb.TxnSource_TXN_SOURCE_DS,
					},
					AccountingEntry: paymentPb.AccountingEntryType_DEBIT,
				},
			},
			setupMocks: func(f *fields) {
				f.dsPredictedTxnHandler.EXPECT().GetDsSuggestedUpcomingTxns(gomock.Any(), "actor-1", feb5, feb15, paymentPb.AccountingEntryType_DEBIT).Return(
					nil, fmt.Errorf("err"))
			},
			want: &pb.GetUpcomingTxnsForActorResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "success fetch DS upcoming txns for actor",
			args: args{
				ctx: context.Background(),
				req: &pb.GetUpcomingTxnsForActorRequest{
					ActorId:  "actor-1",
					FromTime: feb5,
					ToTime:   feb15,
					TxnSources: []modelPb.TxnSource{
						modelPb.TxnSource_TXN_SOURCE_DS,
					},
					AccountingEntry: paymentPb.AccountingEntryType_DEBIT,
				},
			},
			setupMocks: func(f *fields) {
				f.dsPredictedTxnHandler.EXPECT().GetDsSuggestedUpcomingTxns(gomock.Any(), "actor-1", feb5, feb15, paymentPb.AccountingEntryType_DEBIT).Return([]*pb.UpcomingTransaction{
					{
						DerivedEntityId: "swiggy-entity",
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
						MinTime:         feb5,
						MaxTime:         feb10,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        250,
							Nanos:        *********,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        300,
							Nanos:        *********,
						},
						CreditDebit: paymentPb.AccountingEntryType_DEBIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_ACTOR,
						DerivedEntityId: "actor-2",
						MinTime:         feb5,
						MaxTime:         feb12,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        150,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        250,
							Nanos:        0,
						},
						CreditDebit: paymentPb.AccountingEntryType_CREDIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
					{
						DerivedEntityId: "zomato-entity",
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
						MinTime:         feb10,
						MaxTime:         feb12,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        400,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        500,
							Nanos:        0,
						},
						CreditDebit: paymentPb.AccountingEntryType_DEBIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
				}, nil)
			},
			want: &pb.GetUpcomingTxnsForActorResponse{
				Status: rpc.StatusOk(),
				Transactions: []*pb.UpcomingTransaction{
					{
						DerivedEntityId: "swiggy-entity",
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
						MinTime:         feb5,
						MaxTime:         feb10,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        250,
							Nanos:        *********,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        300,
							Nanos:        *********,
						},
						CreditDebit: paymentPb.AccountingEntryType_DEBIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_ACTOR,
						DerivedEntityId: "actor-2",
						MinTime:         feb5,
						MaxTime:         feb12,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        150,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        250,
							Nanos:        0,
						},
						CreditDebit: paymentPb.AccountingEntryType_CREDIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
					{
						DerivedEntityId: "zomato-entity",
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
						MinTime:         feb10,
						MaxTime:         feb12,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        400,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        500,
							Nanos:        0,
						},
						CreditDebit: paymentPb.AccountingEntryType_DEBIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success fetch Fittt upcoming txns for actor",
			args: args{
				ctx: context.Background(),
				req: &pb.GetUpcomingTxnsForActorRequest{
					ActorId:  "actor-1",
					FromTime: feb5,
					ToTime:   feb15,
					TxnSources: []modelPb.TxnSource{
						modelPb.TxnSource_TXN_SOURCE_FITTT,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.fitTxnHandler.EXPECT().GetFitUpcomingTxnsForActor(gomock.Any(), "actor-1", feb5, feb15).Return([]*pb.UpcomingTransaction{
					fitUpTxn1,
					fitUpTxn2,
					fitUpTxn3,
					fitUpTxn4,
				}, nil)
			},
			want: &pb.GetUpcomingTxnsForActorResponse{
				Status: rpc.StatusOk(),
				Transactions: []*pb.UpcomingTransaction{
					fitUpTxn1,
					fitUpTxn2,
					fitUpTxn3,
					fitUpTxn4,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to get fittt upcoming txns for actor",
			args: args{
				ctx: context.Background(),
				req: &pb.GetUpcomingTxnsForActorRequest{
					ActorId:  "actor-1",
					FromTime: feb5,
					ToTime:   feb15,
					TxnSources: []modelPb.TxnSource{
						modelPb.TxnSource_TXN_SOURCE_FITTT,
					},
					AccountingEntry: paymentPb.AccountingEntryType_DEBIT,
				},
			},
			setupMocks: func(f *fields) {
				f.fitTxnHandler.EXPECT().GetFitUpcomingTxnsForActor(gomock.Any(), "actor-1", feb5, feb15).Return(nil, fmt.Errorf("err"))
			},
			want: &pb.GetUpcomingTxnsForActorResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "success fetch both DS and fittt upcoming txns for actor",
			args: args{
				ctx: context.Background(),
				req: &pb.GetUpcomingTxnsForActorRequest{
					ActorId:  "actor-1",
					FromTime: feb5,
					ToTime:   feb15,
					TxnSources: []modelPb.TxnSource{
						modelPb.TxnSource_TXN_SOURCE_DS,
						modelPb.TxnSource_TXN_SOURCE_FITTT,
					},
					AccountingEntry: paymentPb.AccountingEntryType_DEBIT,
				},
			},
			setupMocks: func(f *fields) {
				f.dsPredictedTxnHandler.EXPECT().GetDsSuggestedUpcomingTxns(gomock.Any(), "actor-1", feb5, feb15, paymentPb.AccountingEntryType_DEBIT).Return([]*pb.UpcomingTransaction{
					{
						DerivedEntityId: "swiggy-entity",
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
						MinTime:         feb5,
						MaxTime:         feb10,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        250,
							Nanos:        *********,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        300,
							Nanos:        *********,
						},
						CreditDebit: paymentPb.AccountingEntryType_DEBIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_ACTOR,
						DerivedEntityId: "actor-2",
						MinTime:         feb5,
						MaxTime:         feb12,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        150,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        250,
							Nanos:        0,
						},
						CreditDebit: paymentPb.AccountingEntryType_CREDIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
					{
						DerivedEntityId: "zomato-entity",
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
						MinTime:         feb10,
						MaxTime:         feb12,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        400,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        500,
							Nanos:        0,
						},
						CreditDebit: paymentPb.AccountingEntryType_DEBIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
				}, nil)
				f.fitTxnHandler.EXPECT().GetFitUpcomingTxnsForActor(gomock.Any(), "actor-1", feb5, feb15).Return([]*pb.UpcomingTransaction{
					fitUpTxn1,
					fitUpTxn2,
					fitUpTxn3,
					fitUpTxn4,
				}, nil)
			},
			want: &pb.GetUpcomingTxnsForActorResponse{
				Status: rpc.StatusOk(),
				Transactions: []*pb.UpcomingTransaction{
					fitUpTxn1,
					{
						DerivedEntityId: "swiggy-entity",
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
						MinTime:         feb5,
						MaxTime:         feb10,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        250,
							Nanos:        *********,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        300,
							Nanos:        *********,
						},
						CreditDebit: paymentPb.AccountingEntryType_DEBIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
					{
						EntityType:      modelPb.EntityType_ENTITY_TYPE_ACTOR,
						DerivedEntityId: "actor-2",
						MinTime:         feb5,
						MaxTime:         feb12,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        150,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        250,
							Nanos:        0,
						},
						CreditDebit: paymentPb.AccountingEntryType_CREDIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
					fitUpTxn2,
					{
						DerivedEntityId: "zomato-entity",
						EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
						MinTime:         feb10,
						MaxTime:         feb12,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        400,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        500,
							Nanos:        0,
						},
						CreditDebit: paymentPb.AccountingEntryType_DEBIT,
						TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
					},
					fitUpTxn3,
					fitUpTxn4,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			tt.setupMocks(f)
			s := NewService(f.fitTxnHandler, f.dsPredictedTxnHandler, gconf)
			got, err := s.GetUpcomingTxnsForActor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUpcomingTxnsForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("GetUpcomingTxnsForActor() expected status = %v, got %v", tt.want.GetStatus(), got.GetStatus())
				return
			}
			if got.Status.Code != 0 {
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetUpcomingTxnsForActor() \ngot = %v,\n want %v \ndiff=%s", got, tt.want, diff)
			}
		})
	}
}
