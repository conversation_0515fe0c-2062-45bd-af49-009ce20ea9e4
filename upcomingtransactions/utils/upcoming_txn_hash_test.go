package utils

import (
	"testing"
	"time"

	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/be-common/pkg/datetime"
)

func TestGetUpcomingTransactionComputedHash(t *testing.T) {
	type args struct {
		txn *modelPb.UpcomingTransaction
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "actor_id is empty, hash calc failure",
			args: args{
				txn: &modelPb.UpcomingTransaction{
					ActorId:        "",
					WithEntityId:   "actor-2",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        150,
						Nanos:        10,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        250,
						Nanos:        10,
					},
					MinDate:           timestamppb.New(time.Date(2022, 9, 04, 0, 0, 0, 0, datetime.IST)),
					MaxDate:           timestamppb.New(time.Date(2022, 9, 10, 1, 0, 0, 10, datetime.IST)),
					SubscriptionId:    "sub-100",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-8",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "with_entity_id empty, hash calc failure",
			args: args{
				txn: &modelPb.UpcomingTransaction{
					ActorId:        "actor-1",
					WithEntityId:   "",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        150,
						Nanos:        10,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        250,
						Nanos:        10,
					},
					MinDate:           timestamppb.New(time.Date(2022, 9, 04, 0, 0, 0, 0, datetime.IST)),
					MaxDate:           timestamppb.New(time.Date(2022, 9, 10, 1, 0, 0, 10, datetime.IST)),
					SubscriptionId:    "sub-100",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-8",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "min amt negative, hash calc failure",
			args: args{
				txn: &modelPb.UpcomingTransaction{
					ActorId:        "actor-1",
					WithEntityId:   "actor-2",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        -150,
						Nanos:        10,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        250,
						Nanos:        10,
					},
					MinDate:           timestamppb.New(time.Date(2022, 9, 04, 0, 0, 0, 0, datetime.IST)),
					MaxDate:           timestamppb.New(time.Date(2022, 9, 10, 1, 0, 0, 10, datetime.IST)),
					SubscriptionId:    "sub-100",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-8",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "max amount negative, hash calc failure",
			args: args{
				txn: &modelPb.UpcomingTransaction{
					ActorId:        "actor-1",
					WithEntityId:   "actor-2",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        150,
						Nanos:        10,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        -250,
						Nanos:        10,
					},
					MinDate:           timestamppb.New(time.Date(2022, 9, 04, 0, 0, 0, 0, datetime.IST)),
					MaxDate:           timestamppb.New(time.Date(2022, 9, 10, 1, 0, 0, 10, datetime.IST)),
					SubscriptionId:    "sub-100",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-8",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "min date not valid, hash calc failure",
			args: args{
				txn: &modelPb.UpcomingTransaction{
					ActorId:        "actor-1",
					WithEntityId:   "actor-2",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        150,
						Nanos:        10,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        250,
						Nanos:        10,
					},
					MinDate:           nil,
					MaxDate:           timestamppb.New(time.Date(2022, 9, 10, 1, 0, 0, 10, datetime.IST)),
					SubscriptionId:    "sub-100",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-8",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "max date invalid, hash calc failure",
			args: args{
				txn: &modelPb.UpcomingTransaction{
					ActorId:        "actor-1",
					WithEntityId:   "actor-2",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        150,
						Nanos:        10,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        250,
						Nanos:        10,
					},
					MinDate:           timestamppb.New(time.Date(2022, 9, 04, 0, 0, 0, 0, datetime.IST)),
					MaxDate:           nil,
					SubscriptionId:    "sub-100",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-8",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "credit debit unspecified, hash calc failure",
			args: args{
				txn: &modelPb.UpcomingTransaction{
					ActorId:        "actor-1",
					WithEntityId:   "actor-2",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        150,
						Nanos:        10,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        250,
						Nanos:        10,
					},
					MinDate:           timestamppb.New(time.Date(2022, 9, 04, 0, 0, 0, 0, datetime.IST)),
					MaxDate:           timestamppb.New(time.Date(2022, 9, 10, 1, 0, 0, 10, datetime.IST)),
					SubscriptionId:    "sub-100",
					LastTransactionId: "txn-8",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "hash calc success",
			args: args{
				txn: &modelPb.UpcomingTransaction{
					ActorId:        "actor-1",
					WithEntityId:   "actor-2",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        150,
						Nanos:        10,
					},
					MaxAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        250,
						Nanos:        10,
					},
					MinDate:           timestamppb.New(time.Date(2022, 9, 04, 0, 0, 0, 0, datetime.IST)),
					MaxDate:           timestamppb.New(time.Date(2022, 9, 10, 1, 0, 0, 10, datetime.IST)),
					SubscriptionId:    "sub-100",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-8",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			want:    "HcWGcLLBQ7d6OgAr842TUudM4GSxiymb8OF84UTQRqY=",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetUpcomingTransactionComputedHash(tt.args.txn)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUpcomingTransactionComputedHash() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetUpcomingTransactionComputedHash() got = %v, want %v", got, tt.want)
			}
		})
	}
}
