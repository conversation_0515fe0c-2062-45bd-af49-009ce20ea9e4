package utils

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
)

func GetUpcomingTransactionComputedHash(txn *modelPb.UpcomingTransaction) (string, error) {

	switch {
	case txn.GetActorId() == "":
		return "", fmt.Errorf("actor_id is empty, cannot gen computed_hash")
	case txn.GetWithEntityId() == "":
		return "", fmt.Errorf("withEntityId is empty, cannot gen computed_hash")
	case txn.GetMinAmount().GetUnits() <= 0:
		return "", fmt.Errorf("minAmount is non-positive %v, cannot gen computed_hash", txn.GetMinAmount().GetUnits())
	case txn.GetMaxAmount().GetUnits() <= 0:
		return "", fmt.<PERSON><PERSON><PERSON>("maxAmount is non-positive %v, cannot gen computed_hash", txn.GetMaxAmount().GetUnits())
	case !txn.GetMinDate().IsValid():
		return "", fmt.Errorf("minDate %s is not valid, cannot gen computed_hash", txn.GetMinDate())
	case !txn.GetMaxDate().IsValid():
		return "", fmt.Errorf("maxDate %s is not valid, cannot gen computed_hash", txn.GetMaxDate())
	case txn.GetCreditDebit() == paymentPb.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED:
		return "", fmt.Errorf("accounting entry is unspecified, cannot gen computed_hash")
	}

	hashString := fmt.Sprintf("%s<>%s<>%d<>%d<>%d<>%d<>%d<>%d<>%s", txn.GetActorId(), txn.GetWithEntityId(), txn.GetMinAmount().GetUnits(), txn.GetMinAmount().GetNanos(),
		txn.GetMaxAmount().GetUnits(), txn.GetMaxAmount().GetNanos(), txn.GetMinDate().GetSeconds(), txn.GetMaxDate().GetSeconds(), txn.GetCreditDebit().String())
	hashByte := sha256.Sum256([]byte(hashString))
	return base64.URLEncoding.EncodeToString(hashByte[:]), nil
}
