// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync/atomic"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/upcomingtransactions/config"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DisableDSSuggestions             uint32
	_ProcessRecurringTxnSubscriber    *gencfg.SqsSubscriber
	_UpdateUpcomingTxnStateSubscriber *gencfg.SqsSubscriber
	_DsSubscriptionsIngestionParams   *DsSubscriptionsIngestionParams
	_Application                      *config.Application
	_Server                           *config.Server
	_Logging                          *cfg.Logging
	_AWS                              *cfg.AWS
	_BudgetingDb                      *cfg.DB
	_Secrets                          *cfg.Secrets
}

func (obj *Config) DisableDSSuggestions() bool {
	if atomic.LoadUint32(&obj._DisableDSSuggestions) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) ProcessRecurringTxnSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessRecurringTxnSubscriber
}
func (obj *Config) UpdateUpcomingTxnStateSubscriber() *gencfg.SqsSubscriber {
	return obj._UpdateUpcomingTxnStateSubscriber
}
func (obj *Config) DsSubscriptionsIngestionParams() *DsSubscriptionsIngestionParams {
	return obj._DsSubscriptionsIngestionParams
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) BudgetingDb() *cfg.DB {
	return obj._BudgetingDb
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}

type DsSubscriptionsIngestionParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_NumOfGoroutinesToUpsert  int64
	_S3BucketName             string
	_AllowedFailurePercentage float64
	_FilePathInBucket         string
}

func (obj *DsSubscriptionsIngestionParams) NumOfGoroutinesToUpsert() int {
	return int(atomic.LoadInt64(&obj._NumOfGoroutinesToUpsert))
}
func (obj *DsSubscriptionsIngestionParams) S3BucketName() string {
	return obj._S3BucketName
}
func (obj *DsSubscriptionsIngestionParams) AllowedFailurePercentage() float64 {
	return obj._AllowedFailurePercentage
}
func (obj *DsSubscriptionsIngestionParams) FilePathInBucket() string {
	return obj._FilePathInBucket
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disabledssuggestions"] = _obj.SetDisableDSSuggestions
	_ProcessRecurringTxnSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRecurringTxnSubscriber = _ProcessRecurringTxnSubscriber
	helper.AddFieldSetters("processrecurringtxnsubscriber", _fieldSetters, _setters)
	_UpdateUpcomingTxnStateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UpdateUpcomingTxnStateSubscriber = _UpdateUpcomingTxnStateSubscriber
	helper.AddFieldSetters("updateupcomingtxnstatesubscriber", _fieldSetters, _setters)
	_DsSubscriptionsIngestionParams, _fieldSetters := NewDsSubscriptionsIngestionParams()
	_obj._DsSubscriptionsIngestionParams = _DsSubscriptionsIngestionParams
	helper.AddFieldSetters("dssubscriptionsingestionparams", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disabledssuggestions"] = _obj.SetDisableDSSuggestions
	_ProcessRecurringTxnSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRecurringTxnSubscriber = _ProcessRecurringTxnSubscriber
	helper.AddFieldSetters("processrecurringtxnsubscriber", _fieldSetters, _setters)
	_UpdateUpcomingTxnStateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UpdateUpcomingTxnStateSubscriber = _UpdateUpcomingTxnStateSubscriber
	helper.AddFieldSetters("updateupcomingtxnstatesubscriber", _fieldSetters, _setters)
	_DsSubscriptionsIngestionParams, _fieldSetters := NewDsSubscriptionsIngestionParams()
	_obj._DsSubscriptionsIngestionParams = _DsSubscriptionsIngestionParams
	helper.AddFieldSetters("dssubscriptionsingestionparams", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "disabledssuggestions":
		return obj.SetDisableDSSuggestions(v.DisableDSSuggestions, true, nil)
	case "processrecurringtxnsubscriber":
		return obj._ProcessRecurringTxnSubscriber.Set(v.ProcessRecurringTxnSubscriber, true, path)
	case "updateupcomingtxnstatesubscriber":
		return obj._UpdateUpcomingTxnStateSubscriber.Set(v.UpdateUpcomingTxnStateSubscriber, true, path)
	case "dssubscriptionsingestionparams":
		return obj._DsSubscriptionsIngestionParams.Set(v.DsSubscriptionsIngestionParams, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetDisableDSSuggestions(v.DisableDSSuggestions, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._ProcessRecurringTxnSubscriber.Set(v.ProcessRecurringTxnSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UpdateUpcomingTxnStateSubscriber.Set(v.UpdateUpcomingTxnStateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DsSubscriptionsIngestionParams.Set(v.DsSubscriptionsIngestionParams, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._AWS = v.AWS
	obj._BudgetingDb = v.BudgetingDb
	obj._Secrets = v.Secrets
	return nil
}

func (obj *Config) SetDisableDSSuggestions(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.DisableDSSuggestions", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableDSSuggestions, 1)
	} else {
		atomic.StoreUint32(&obj._DisableDSSuggestions, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableDSSuggestions")
	}
	return nil
}

func NewDsSubscriptionsIngestionParams() (_obj *DsSubscriptionsIngestionParams, _setters map[string]dynconf.SetFunc) {
	_obj = &DsSubscriptionsIngestionParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["numofgoroutinestoupsert"] = _obj.SetNumOfGoroutinesToUpsert
	return _obj, _setters
}

func (obj *DsSubscriptionsIngestionParams) Init() {
	newObj, _ := NewDsSubscriptionsIngestionParams()
	*obj = *newObj
}

func (obj *DsSubscriptionsIngestionParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DsSubscriptionsIngestionParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DsSubscriptionsIngestionParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *DsSubscriptionsIngestionParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DsSubscriptionsIngestionParams) setDynamicField(v *config.DsSubscriptionsIngestionParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "numofgoroutinestoupsert":
		return obj.SetNumOfGoroutinesToUpsert(v.NumOfGoroutinesToUpsert, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DsSubscriptionsIngestionParams) setDynamicFields(v *config.DsSubscriptionsIngestionParams, dynamic bool, path []string) (err error) {

	err = obj.SetNumOfGoroutinesToUpsert(v.NumOfGoroutinesToUpsert, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DsSubscriptionsIngestionParams) setStaticFields(v *config.DsSubscriptionsIngestionParams) error {

	obj._S3BucketName = v.S3BucketName
	obj._AllowedFailurePercentage = v.AllowedFailurePercentage
	obj._FilePathInBucket = v.FilePathInBucket
	return nil
}

func (obj *DsSubscriptionsIngestionParams) SetNumOfGoroutinesToUpsert(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *DsSubscriptionsIngestionParams.NumOfGoroutinesToUpsert", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._NumOfGoroutinesToUpsert, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NumOfGoroutinesToUpsert")
	}
	return nil
}
