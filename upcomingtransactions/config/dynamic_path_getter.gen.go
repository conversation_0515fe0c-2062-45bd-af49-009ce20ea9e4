// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "disabledssuggestions":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableDSSuggestions\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableDSSuggestions, nil
	case "processrecurringtxnsubscriber":
		return obj.ProcessRecurringTxnSubscriber.Get(dynamicFieldPath[1:])
	case "updateupcomingtxnstatesubscriber":
		return obj.UpdateUpcomingTxnStateSubscriber.Get(dynamicFieldPath[1:])
	case "dssubscriptionsingestionparams":
		return obj.DsSubscriptionsIngestionParams.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.<PERSON><PERSON><PERSON>("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DsSubscriptionsIngestionParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "numofgoroutinestoupsert":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NumOfGoroutinesToUpsert\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NumOfGoroutinesToUpsert, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DsSubscriptionsIngestionParams", strings.Join(dynamicFieldPath, "."))
	}
}
