package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
)

const dbCredentials = "DbCredentials"

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.UPCOMING_TRANSACTIONS_SERVICE)

	if err != nil {
		return nil, fmt.Errorf("failed to load dynamic config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}

	err = readAndSetEnv(conf)
	if err != nil {
		return nil, errors.Wrap(err, "failed to read and set env")
	}

	keyToIdMap := cfg.AddPgdbSslCertSecretIds(conf.BudgetingDb, conf.Secrets.Ids)

	keyToSecret, err := cfg.LoadSecrets(keyToIdMap, conf.Application.Environment, conf.AWS.Region)

	if err != nil {
		return nil, err
	}

	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}
	return conf, nil
}

// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
// update db endpoint
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.BudgetingDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdatePGDBSecretValues(c.BudgetingDb, c.Secrets, keyToSecret)
	// update db username and password in db config
	// if env is development keys are not fetched from SM hence update from yml file itself
	if c.Application.Environment == cfg.TestEnv || c.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c.BudgetingDb, c.Secrets.Ids[dbCredentials])
		return nil
	}
	if _, ok := keyToSecret[dbCredentials]; !ok {
		return fmt.Errorf("db username password not fetched from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.BudgetingDb, keyToSecret[dbCredentials])
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		c.BudgetingDb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

// Constraints with respect to dynamic config generation tool:
// 1. Struct names have to start with uppercase letters
// 2. Struct variables need to be pointers
//
//go:generate conf_gen github.com/epifi/gamma/upcomingtransactions/config Config
type Config struct {
	Application                      *Application
	Server                           *Server
	Logging                          *cfg.Logging
	AWS                              *cfg.AWS
	BudgetingDb                      *cfg.DB
	Secrets                          *cfg.Secrets
	ProcessRecurringTxnSubscriber    *cfg.SqsSubscriber              `dynamic:"true"`
	UpdateUpcomingTxnStateSubscriber *cfg.SqsSubscriber              `dynamic:"true"`
	DsSubscriptionsIngestionParams   *DsSubscriptionsIngestionParams `dynamic:"true"`
	DisableDSSuggestions             bool                            `dynamic:"true"`
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type DsSubscriptionsIngestionParams struct {
	S3BucketName            string
	NumOfGoroutinesToUpsert int `dynamic:"true"`
	// if failure percentage is less than AllowedFailurePercentage we mark the process as success, otherwise retry it
	AllowedFailurePercentage float64
	FilePathInBucket         string
}
