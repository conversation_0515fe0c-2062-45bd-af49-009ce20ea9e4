Application:
  Environment: "development"
  Name: "upcomingtransactions"

Aws:
  Region: "ap-south-1"

Server:
  Ports:
    GrpcPort: 8096
    GrpcSecurePort: 9511
    HttpPort: 9999

BudgetingDb:
  AppName: "upcomingtransactions"
  StatementTimeout: 5m
  Name: "budgeting"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Secrets:
  Ids:
    DbCredentials: "{\"username\": \"root\", \"password\": \"\"}"


ProcessRecurringTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 1
  QueueName: "recurring-txns-ds-file-upload-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 6
      TimeUnit: "Minute"

UpdateUpcomingTxnStateSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "upcoming-transactions-update-order-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 8
      TimeUnit: "Second"
