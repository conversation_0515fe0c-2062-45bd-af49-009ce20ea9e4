package consumer_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"

	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/upcomingtransactions/consumer"
	mock_consumer "github.com/epifi/gamma/upcomingtransactions/consumer/mocks"
	"github.com/epifi/gamma/upcomingtransactions/consumer/types"
	mock_subscriptionmanager "github.com/epifi/gamma/upcomingtransactions/subscriptionmanager/mocks"
)

var (
	subData1 = &types.SubscriptionData{
		Subscription: &modelPb.Subscription{
			Id: "sub-1",
		},
		Transactions: nil,
	}
	subData2 = &types.SubscriptionData{
		Subscription: &modelPb.Subscription{
			Id: "sub-2",
		},
		Transactions: nil,
	}
	subData3 = &types.SubscriptionData{
		Subscription: &modelPb.Subscription{
			Id: "sub-3",
		},
		Transactions: nil,
	}
	subData4 = &types.SubscriptionData{
		Subscription: &modelPb.Subscription{
			Id: "sub-4",
		},
		Transactions: nil,
	}
	subData5 = &types.SubscriptionData{
		Subscription: &modelPb.Subscription{
			Id: "sub-5",
		},
		Transactions: nil,
	}
)

type fields struct {
	parseSubscriptions *mock_consumer.MockSubscriptionParser
	subsManager        *mock_subscriptionmanager.MockSubscriptionManager
}

func initFields(ctrl *gomock.Controller) *fields {
	return &fields{
		parseSubscriptions: mock_consumer.NewMockSubscriptionParser(ctrl),
		subsManager:        mock_subscriptionmanager.NewMockSubscriptionManager(ctrl),
	}
}
func TestRecurringTxnsProcessorImpl_ProcessRecurringTxns(t *testing.T) {
	logger.Init(cfg.TestEnv)
	type args struct {
		ctx             context.Context
		fileContent     []byte
		numOfGoroutines int
	}
	tests := []struct {
		name               string
		args               args
		setupMocks         func(f *fields)
		wantFailurePercent float64
		wantErr            bool
	}{
		{
			name: "failed to parse read file",
			args: args{
				ctx:             nil,
				fileContent:     []byte(content1),
				numOfGoroutines: 3,
			},
			setupMocks: func(f *fields) {
				f.parseSubscriptions.EXPECT().Parse([]byte(content1)).Return(nil, fmt.Errorf("err"))
			},
			wantFailurePercent: 100.0,
			wantErr:            true,
		},
		{
			name: "failed to process one or more subscription",
			args: args{
				ctx:             nil,
				fileContent:     []byte(content1),
				numOfGoroutines: 3,
			},
			setupMocks: func(f *fields) {
				f.parseSubscriptions.EXPECT().Parse([]byte(content1)).Return([]*types.SubscriptionData{
					subData1,
					subData2,
					subData3,
					subData4,
					subData5,
				}, nil)
				f.subsManager.EXPECT().UpsertSubscriptionData(gomock.Any(), subData1).Return(fmt.Errorf("err"))
				f.subsManager.EXPECT().UpsertSubscriptionData(gomock.Any(), subData2).Return(nil).MaxTimes(1)
				f.subsManager.EXPECT().UpsertSubscriptionData(gomock.Any(), subData3).Return(nil).MaxTimes(1)
				f.subsManager.EXPECT().UpsertSubscriptionData(gomock.Any(), subData4).Return(nil).MaxTimes(1)
				f.subsManager.EXPECT().UpsertSubscriptionData(gomock.Any(), subData5).Return(nil).MaxTimes(1)
			},
			wantFailurePercent: 20.0,
			wantErr:            true,
		},
		{
			name: "successfully processed file",
			args: args{
				ctx:             nil,
				fileContent:     []byte(content1),
				numOfGoroutines: 3,
			},
			setupMocks: func(f *fields) {
				f.parseSubscriptions.EXPECT().Parse([]byte(content1)).Return([]*types.SubscriptionData{
					subData1,
					subData2,
					subData3,
					subData4,
					subData5,
				}, nil)
				f.subsManager.EXPECT().UpsertSubscriptionData(gomock.Any(), subData1).Return(nil)
				f.subsManager.EXPECT().UpsertSubscriptionData(gomock.Any(), subData2).Return(nil)
				f.subsManager.EXPECT().UpsertSubscriptionData(gomock.Any(), subData3).Return(nil)
				f.subsManager.EXPECT().UpsertSubscriptionData(gomock.Any(), subData4).Return(nil)
				f.subsManager.EXPECT().UpsertSubscriptionData(gomock.Any(), subData5).Return(nil)
			},
			wantFailurePercent: 0,
			wantErr:            false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			tt.setupMocks(f)
			p := consumer.NewRecurringTxnsProcessorImpl(f.parseSubscriptions, f.subsManager)
			gotFailPercentage, gotErr := p.ProcessRecurringTxns(tt.args.ctx, tt.args.fileContent, tt.args.numOfGoroutines)
			if (gotErr != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringTxns() got error = %v, wantErr %v", gotErr, tt.wantErr)
			}
			if gotFailPercentage != tt.wantFailurePercent {
				t.Errorf("ProcessRecurringTxns() got failPercent = %v, want %v", gotFailPercentage, tt.wantFailurePercent)
			}
		})
	}
}
