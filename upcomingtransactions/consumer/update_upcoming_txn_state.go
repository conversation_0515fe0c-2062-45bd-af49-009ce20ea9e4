package consumer

import (
	"context"
	"fmt"
	"sort"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/order"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/upcomingtransactions/consumer"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/gamma/categorizer/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
)

const (
	minPrecision = 1e-6
)

func (s *ConsumerService) UpdateUpcomingTxnState(ctx context.Context, orderEvent *orderPb.OrderUpdate) (*consumer.UpdateUpcomingTxnStateResponse, error) {
	resp := &consumer.UpdateUpcomingTxnStateResponse{}

	order := orderEvent.GetOrderWithTransactions().GetOrder()

	// process order only when it is successful
	if order.GetStatus() != orderPb.OrderStatus_PAID {
		resp.ResponseHeader = genSuccess()
		return resp, nil
	}

	fromActorId := order.GetFromActorId()
	toActorId := order.GetToActorId()
	amount := order.GetAmount()

	executionTime, err := getExecutionTime(order.GetWorkflow(), orderEvent.GetOrderWithTransactions().GetTransactions())
	if err != nil {
		logger.Error(ctx, "failed to get execution time from order event", zap.Error(err), zap.String(logger.ORDER_ID, order.GetId()))
		resp.ResponseHeader = genTransientFailure()
		return resp, nil
	}

	fromMerchantId, toMerchantId, err := s.getMerchantIds(ctx, fromActorId, toActorId)
	if err != nil {
		logger.Error(ctx, "failed to get from and to merchant ids", zap.Error(err), zap.String(logger.ORDER_ID, order.GetId()))
		resp.ResponseHeader = genTransientFailure()
		return resp, nil
	}

	errGrp, gCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		return s.processStateUpdate(gCtx, executionTime, amount, fromActorId, toActorId, toMerchantId)
	})
	errGrp.Go(func() error {
		return s.processStateUpdate(gCtx, executionTime, amount, toActorId, fromActorId, fromMerchantId)
	})

	if updateErr := errGrp.Wait(); updateErr != nil {
		logger.Error(ctx, "failed to process state update", zap.Error(updateErr))
		resp.ResponseHeader = genTransientFailure()
		return resp, nil
	}

	resp.ResponseHeader = genSuccess()
	return resp, nil
}

// getMerchantIds: invokes GetActorById api for both fromActorId and toActorId and return corr entity_id (if entity is a merchant) otherwise return empty string
func (s *ConsumerService) getMerchantIds(ctx context.Context, fromActorId string, toActorId string) (string, string, error) {
	var (
		fromMerchantId, toMerchantId string
		fromActorResp, toActorResp   *actor.GetActorByIdResponse
	)

	errGrp, gCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		var err error
		fromActorResp, err = s.actorClient.GetActorById(gCtx, &actor.GetActorByIdRequest{
			Id: fromActorId,
		})
		if rpcErr := epifigrpc.RPCError(fromActorResp, err); rpcErr != nil {
			return fmt.Errorf("failed to get from_actor details using GetActorById : %w", err)
		}
		return nil
	})

	errGrp.Go(func() error {
		var err error
		toActorResp, err = s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{
			Id: toActorId,
		})
		if rpcErr := epifigrpc.RPCError(toActorResp, err); rpcErr != nil {
			return fmt.Errorf("failed to get to_actor details using GetActorById : %w", err)
		}
		return nil
	})

	if actorFetchErr := errGrp.Wait(); actorFetchErr != nil {
		return "", "", actorFetchErr
	}
	if fromActorResp.GetActor().GetType() == types.Actor_EXTERNAL_MERCHANT {
		fromMerchantId = fromActorResp.GetActor().GetEntityId()
	}
	if toActorResp.GetActor().GetType() == types.Actor_EXTERNAL_MERCHANT {
		toMerchantId = toActorResp.GetActor().GetEntityId()
	}
	return fromMerchantId, toMerchantId, nil
}

func (s *ConsumerService) processStateUpdate(ctx context.Context, executionTime *timestamppb.Timestamp, amount *money.Money, actorId, withActorId, withMerchantId string) error {
	withEntityId := GetEntityId(withActorId, withMerchantId)
	txns, err := s.subsManager.GetUpcomingTransactionsWithEntityAfter(ctx, actorId, withEntityId, executionTime)
	if err != nil {
		return fmt.Errorf("failed to get upcoming txns for actor : %w", err)
	}
	if len(txns) == 0 {
		return nil
	}
	// sort upcoming transactions primarily by max_date (asc order) and tie-brakes by min_date (asc)
	sort.Slice(txns, func(i, j int) bool {
		if txns[i].GetMaxDate().AsTime().Sub(txns[i].GetMaxDate().AsTime()) == 0 {
			return isTime1AfterTime2(txns[j].GetMinDate(), txns[i].GetMaxDate())
		}
		return isTime1AfterTime2(txns[j].GetMaxDate(), txns[i].GetMaxDate())
	})
	for _, txn := range txns {
		amountInRange, err := isAmountInRange(amount, txn.GetMinAmount(), txn.GetMaxAmount())
		if err != nil {
			return fmt.Errorf("amount range match failed : %w", err)
		}
		// if execution time is in [txn_min_date, txn_max_date], txn_amount in range [min_amount, max_amount] + txn is not yet executed
		if isTimestampInRange(executionTime, txn.GetMinDate(), txn.GetMaxDate()) && amountInRange &&
			txn.GetExecutionStatus() == modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET {
			updateErr := s.subsManager.UpdateTxnStatusByActorAndTxnId(ctx, actorId, txn.GetId(), modelPb.TxnStatus_TXN_STATUS_EXECUTED)
			if updateErr != nil {
				return fmt.Errorf("failed to update txn status to executed : %w", updateErr)
			}
			break
		}
	}
	return nil
}

func GetEntityId(actorId, merchantId string) string {
	switch {
	case len(merchantId) > 0:
		return merchantId
	case len(actorId) > 0:
		return actorId
	default:
		return ""
	}
}

// isTime1AfterTime2 returns true if Time1 >= Time2
func isTime1AfterTime2(ts1, ts2 *timestamppb.Timestamp) bool {
	return ts1.AsTime().Sub(ts2.AsTime()) >= 0
}

// getExecutionTime returns the txn (of interest) execution time
func getExecutionTime(workflow order.OrderWorkflow, txns []*payment.Transaction) (*timestamppb.Timestamp, error) {
	txnOfInterest, found := common.WorkFlowtoIndexOfTxnOfInterestMap[workflow]
	if !found {
		return nil, fmt.Errorf("unable to find index in workflow map : %s", workflow.String())
	}
	if txnOfInterest < 0 || txnOfInterest >= len(txns) {
		return nil, fmt.Errorf("txn of interest %d out of bound : %d", txnOfInterest, len(txns))
	}
	return txns[txnOfInterest].GetExecutionTS(), nil
}

func isTimestampInRange(ts, l, r *timestamppb.Timestamp) bool {
	return isTime1AfterTime2(ts, l) && isTime1AfterTime2(r, ts)
}

// isAmountInRange returns true if l<=amount<=r
func isAmountInRange(amount, l, r *money.Money) (bool, error) {
	// leftCondition is 0 if l == amount, -1 if l < amount and +1 if l > amount
	leftCondition, err := moneyPb.CompareV2(l, amount)
	if err != nil {
		return false, fmt.Errorf("failed to evaluated l <= amt : %w", err)
	}
	// rightCondition is 0 if amount == r, -1 if amount < r and +1 if amount > r
	rightCondition, err := moneyPb.CompareV2(amount, r)
	if err != nil {
		return false, fmt.Errorf("failed to evaluated amt <= r : %w", err)
	}
	return (leftCondition < 1) && (rightCondition < 1), nil
}
