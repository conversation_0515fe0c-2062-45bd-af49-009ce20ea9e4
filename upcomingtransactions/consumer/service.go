package consumer

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/aws/v2/s3"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/upcomingtransactions/consumer"
	"github.com/epifi/gamma/upcomingtransactions/config/genconf"
	"github.com/epifi/gamma/upcomingtransactions/subscriptionmanager"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	genTransientFailure = func() *queuePb.ConsumerResponseHeader {
		return &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
		}
	}
	genFinalFailure = func() *queuePb.ConsumerResponseHeader {
		return &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
		}
	}
	genSuccess = func() *queuePb.ConsumerResponseHeader {
		return &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		}
	}
)

type ConsumerService struct {
	ingestionParams *genconf.DsSubscriptionsIngestionParams
	s3Client        s3.S3Client
	txnsProcessor   RecurringTxnsProcessor
	subsManager     subscriptionmanager.SubscriptionManager
	actorClient     actor.ActorClient
}

func NewConsumerService(genconf *genconf.Config, s3Client s3.S3Client, txnProcessor RecurringTxnsProcessor, subsManager subscriptionmanager.SubscriptionManager, actorClient actor.ActorClient) *ConsumerService {
	return &ConsumerService{
		ingestionParams: genconf.DsSubscriptionsIngestionParams(),
		s3Client:        s3Client,
		txnsProcessor:   txnProcessor,
		subsManager:     subsManager,
		actorClient:     actorClient,
	}
}

func (s *ConsumerService) ProcessRecurringTxnsFile(ctx context.Context, req *consumer.ProcessRecurringTxnsFileRequest) (*consumer.ProcessRecurringTxnsFileResponse, error) {
	res := &consumer.ProcessRecurringTxnsFileResponse{}
	records := req.GetRecords()
	if len(records) == 0 {
		logger.Error(ctx, "No record present in ProcessUpcomingTxnFile request")
		res.ResponseHeader = genFinalFailure()
		return res, nil
	}
	for _, record := range records {
		filePath := record.GetS3().GetObject().GetKey()
		bucketName := record.GetS3().GetBucket().GetName()

		if err := s.isFileValidForProcessing(filePath, bucketName); err != nil {
			logger.Error(ctx, "file validation failed", zap.String(logger.FILE_NAME, filePath), zap.Error(err))
			res.ResponseHeader = genFinalFailure()
			return res, nil
		}

		fileContent, err := s.s3Client.Read(ctx, filePath)
		if err != nil {
			logger.Error(ctx, "error while reading file from s3", zap.String(logger.FILE_NAME, filePath), zap.Error(err))
			res.ResponseHeader = genTransientFailure()
			return res, nil
		}
		if len(fileContent) == 0 {
			logger.Info(ctx, "no entries to process in file, success", zap.String(logger.FILE_NAME, filePath))
			res.ResponseHeader = genSuccess()
			return res, nil
		}

		logger.Info(ctx, "Processing recurring transactions file", zap.String(logger.FILE_NAME, filePath))

		failurePercent, err := s.txnsProcessor.ProcessRecurringTxns(ctx, fileContent, s.ingestionParams.NumOfGoroutinesToUpsert())
		if errors.Is(err, DsResultParseErr) {
			logger.Error(ctx, "failure in parsing ds output", zap.Error(err))
			res.ResponseHeader = genFinalFailure()
			return res, nil
		}

		if failurePercent > s.ingestionParams.AllowedFailurePercentage() {
			logger.Error(ctx, "failed to process upcoming txn ds input", zap.Error(err), zap.String(logger.FILE_NAME, filePath))
			res.ResponseHeader = genTransientFailure()
			return res, nil
		}
		logger.Info(ctx, fmt.Sprintf("event success with fail_percent : %f", failurePercent), zap.Error(err))
	}

	res.ResponseHeader = genSuccess()
	return res, nil
}

// isFileValidForProcessing: validates the file path and bucket name
// file should  be present at path: s3://epifi-data-services/subscriptions_recurring_txns/active_subscriptions.json/*
func (s *ConsumerService) isFileValidForProcessing(filePath string, bucketName string) error {
	pathArray := strings.Split(filePath, "/")
	if len(pathArray) == 0 {
		return fmt.Errorf("file path is empty")
	}
	if !strings.Contains(filePath, s.ingestionParams.FilePathInBucket()) {
		return fmt.Errorf("file path does not match the expected path: %s", filePath)
	}
	// bucket name check
	if bucketName != s.ingestionParams.S3BucketName() {
		return fmt.Errorf("bucket name does not match: %s", bucketName)
	}
	return nil
}
