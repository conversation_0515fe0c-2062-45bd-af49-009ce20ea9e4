package consumer_test

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	mockActor "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/aws/s3"
	"github.com/epifi/gamma/api/upcomingtransactions/consumer"
	"github.com/epifi/gamma/upcomingtransactions/config"
	"github.com/epifi/gamma/upcomingtransactions/config/genconf"
	consumer2 "github.com/epifi/gamma/upcomingtransactions/consumer"
	mock_consumer "github.com/epifi/gamma/upcomingtransactions/consumer/mocks"
	mock_subscriptionmanager "github.com/epifi/gamma/upcomingtransactions/subscriptionmanager/mocks"
)

var (
	conf  *config.Config
	gconf *genconf.Config
)

type serviceFields struct {
	s3Client      *mocks.MockS3Client
	txnsProcessor *mock_consumer.MockRecurringTxnsProcessor
	subsManager   *mock_subscriptionmanager.MockSubscriptionManager
	actorClient   *mockActor.MockActorClient
}

func initServiceFields(ctrl *gomock.Controller) *serviceFields {
	return &serviceFields{
		s3Client:      mocks.NewMockS3Client(ctrl),
		txnsProcessor: mock_consumer.NewMockRecurringTxnsProcessor(ctrl),
		subsManager:   mock_subscriptionmanager.NewMockSubscriptionManager(ctrl),
		actorClient:   mockActor.NewMockActorClient(ctrl),
	}
}

func TestConsumerService_ProcessRecurringTxnsFile(t *testing.T) {
	logger.Init(cfg.TestEnv)
	type args struct {
		ctx context.Context
		req *consumer.ProcessRecurringTxnsFileRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *serviceFields)
		want       *consumer.ProcessRecurringTxnsFileResponse
		wantErr    bool
	}{
		{
			name: "no record in req, permanent failure",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessRecurringTxnsFileRequest{
					Records: nil,
				},
			},
			setupMocks: func(f *serviceFields) {
			},
			want: &consumer.ProcessRecurringTxnsFileResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "file validation failed (invalid bucked name), permanent failure",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessRecurringTxnsFileRequest{
					Records: []*s3.Record{
						{
							S3: &s3.S3{
								Bucket: &s3.Bucket{
									Name: "abc",
									Arn:  "",
								},
								Object: &s3.Object{
									Key: "subscriptions_recurring_txns/active_subscriptions.json/*",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {

			},
			want: &consumer.ProcessRecurringTxnsFileResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "file validation failed (invalid file path), permanent failure",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessRecurringTxnsFileRequest{
					Records: []*s3.Record{
						{
							S3: &s3.S3{
								Bucket: &s3.Bucket{
									Name: "epifi-data-services-dev",
								},
								Object: &s3.Object{
									Key: "abc/active_subscriptions.json/*",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {

			},
			want: &consumer.ProcessRecurringTxnsFileResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to read file from s3, transient failure",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessRecurringTxnsFileRequest{
					Records: []*s3.Record{
						{
							S3: &s3.S3{
								Bucket: &s3.Bucket{
									Name: "epifi-data-services-dev",
									Arn:  "",
								},
								Object: &s3.Object{
									Key: "subscriptions_recurring_txns/active_subscriptions.json/abc.json",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.s3Client.EXPECT().Read(gomock.Any(), "subscriptions_recurring_txns/active_subscriptions.json/abc.json").Return(nil, fmt.Errorf("err"))
			},
			want: &consumer.ProcessRecurringTxnsFileResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "no data in file read from s3, transient failure",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessRecurringTxnsFileRequest{
					Records: []*s3.Record{
						{
							S3: &s3.S3{
								Bucket: &s3.Bucket{
									Name: "epifi-data-services-dev",
									Arn:  "",
								},
								Object: &s3.Object{
									Key: "subscriptions_recurring_txns/active_subscriptions.json/abc.json",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.s3Client.EXPECT().Read(gomock.Any(), "subscriptions_recurring_txns/active_subscriptions.json/abc.json").Return(nil, nil)
			},
			want: &consumer.ProcessRecurringTxnsFileResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to process file, transient failure",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessRecurringTxnsFileRequest{
					Records: []*s3.Record{
						{
							S3: &s3.S3{
								Bucket: &s3.Bucket{
									Name: "epifi-data-services-dev",
								},
								Object: &s3.Object{
									Key: "subscriptions_recurring_txns/active_subscriptions.json/abc.json",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.s3Client.EXPECT().Read(gomock.Any(), "subscriptions_recurring_txns/active_subscriptions.json/abc.json").Return([]byte(content1), nil)
				f.txnsProcessor.EXPECT().ProcessRecurringTxns(gomock.Any(), []byte(content1), 3).Return(100.0, fmt.Errorf("err"))
			},
			want: &consumer.ProcessRecurringTxnsFileResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failure percentage > thresh, transient failure",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessRecurringTxnsFileRequest{
					Records: []*s3.Record{
						{
							S3: &s3.S3{
								Bucket: &s3.Bucket{
									Name: "epifi-data-services-dev",
								},
								Object: &s3.Object{
									Key: "subscriptions_recurring_txns/active_subscriptions.json/abc.json",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.s3Client.EXPECT().Read(gomock.Any(), "subscriptions_recurring_txns/active_subscriptions.json/abc.json").Return([]byte(content1), nil)
				f.txnsProcessor.EXPECT().ProcessRecurringTxns(gomock.Any(), []byte(content1), 3).Return(0.5, nil)
			},
			want: &consumer.ProcessRecurringTxnsFileResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failure percentage < thresh, success",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessRecurringTxnsFileRequest{
					Records: []*s3.Record{
						{
							S3: &s3.S3{
								Bucket: &s3.Bucket{
									Name: "epifi-data-services-dev",
								},
								Object: &s3.Object{
									Key: "subscriptions_recurring_txns/active_subscriptions.json/abc.json",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.s3Client.EXPECT().Read(gomock.Any(), "subscriptions_recurring_txns/active_subscriptions.json/abc.json").Return([]byte(content1), nil)
				f.txnsProcessor.EXPECT().ProcessRecurringTxns(gomock.Any(), []byte(content1), 3).Return(0.1, nil)
			},
			want: &consumer.ProcessRecurringTxnsFileResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "parsing error, permanent failure",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessRecurringTxnsFileRequest{
					Records: []*s3.Record{
						{
							S3: &s3.S3{
								Bucket: &s3.Bucket{
									Name: "epifi-data-services-dev",
								},
								Object: &s3.Object{
									Key: "subscriptions_recurring_txns/active_subscriptions.json/abc.json",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.s3Client.EXPECT().Read(gomock.Any(), "subscriptions_recurring_txns/active_subscriptions.json/abc.json").Return([]byte(content1), nil)
				f.txnsProcessor.EXPECT().ProcessRecurringTxns(gomock.Any(), []byte(content1), 3).Return(100.0, fmt.Errorf("%w : err", consumer2.DsResultParseErr))
			},
			want: &consumer.ProcessRecurringTxnsFileResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "success processing file",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessRecurringTxnsFileRequest{
					Records: []*s3.Record{
						{
							S3: &s3.S3{
								Bucket: &s3.Bucket{
									Name: "epifi-data-services-dev",
								},
								Object: &s3.Object{
									Key: "subscriptions_recurring_txns/active_subscriptions.json/abc.json",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.s3Client.EXPECT().Read(gomock.Any(), "subscriptions_recurring_txns/active_subscriptions.json/abc.json").Return([]byte(content1), nil)
				f.txnsProcessor.EXPECT().ProcessRecurringTxns(gomock.Any(), []byte(content1), 3).Return(0.0, nil)
			},
			want: &consumer.ProcessRecurringTxnsFileResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initServiceFields(ctrl)
			tt.setupMocks(f)

			s := consumer2.NewConsumerService(gconf, f.s3Client, f.txnsProcessor, nil, nil)
			got, err := s.ProcessRecurringTxnsFile(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRecurringTxnsFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessRecurringTxnsFile() got = %v, want %v", got, tt.want)
			}
		})
	}
}
