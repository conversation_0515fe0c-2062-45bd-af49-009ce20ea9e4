//go:generate mockgen -source=processor.go -destination=./mocks/mock_processor.go package=mocks
package consumer

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/wire"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/upcomingtransactions/consumer/types"
	"github.com/epifi/gamma/upcomingtransactions/subscriptionmanager"
)

var RecurringTxnsProcessorWireSet = wire.NewSet(NewRecurringTxnsProcessorImpl, wire.Bind(new(RecurringTxnsProcessor), new(*RecurringTxnsProcessorImpl)))

var (
	DsResultParseErr = fmt.Errorf("failed to parse subscriptions and transactions")
)

type RecurringTxnsProcessor interface {
	// ProcessRecurringTxns takes ds recurring txn output in form of byte array and processes each subscription entry
	// and upsert corr subscription and transactions from each entry
	// It return the percentage of failed subscription data entries and the error
	ProcessRecurringTxns(ctx context.Context, fileContent []byte, numOfGoroutines int) (float64, error)
}

type RecurringTxnsProcessorImpl struct {
	subscriptionParser SubscriptionParser
	subsManager        subscriptionmanager.SubscriptionManager
}

func NewRecurringTxnsProcessorImpl(subsParser SubscriptionParser, subsManager subscriptionmanager.SubscriptionManager) *RecurringTxnsProcessorImpl {
	return &RecurringTxnsProcessorImpl{
		subscriptionParser: subsParser,
		subsManager:        subsManager,
	}
}

func (p *RecurringTxnsProcessorImpl) ProcessRecurringTxns(ctx context.Context, fileContent []byte, numOfGoroutines int) (float64, error) {
	subsData, err := p.subscriptionParser.Parse(fileContent)
	if err != nil {
		return 100.0, fmt.Errorf("%w : %s", DsResultParseErr, err.Error())
	}
	totalRecords := len(subsData)
	if totalRecords == 0 {
		logger.Info(ctx, "total records after parsing is 0")
		return 0, nil
	}
	logger.Info(ctx, fmt.Sprintf("initiating processing of %d subscription data records", totalRecords))

	ch := make(chan int, len(subsData))
	errCh := make(chan error, len(subsData))
	wg := sync.WaitGroup{}

	for t := 0; t < numOfGoroutines; t++ {
		wg.Add(1)
		goroutine.Run(ctx, 25*time.Minute, func(ctx context.Context) {
			p.upsertSubscriptionData(ctx, subsData, &wg, ch, errCh)
		})
	}

	for idx := range subsData {
		ch <- idx
	}

	// this will cause the workers to stop and exit the receive loop
	close(ch)
	wg.Wait()
	close(errCh)

	if len(errCh) > 0 {
		errLen := len(errCh)
		logger.Error(ctx, fmt.Sprintf("%d subscription data out of %d failed to process", errLen, totalRecords))
		for i := 0; i < errLen; i++ {
			err = <-errCh
			logger.Error(ctx, "failed to upsert sub_data", zap.Error(err))
		}
		return 100.0 * float64(errLen) / float64(totalRecords), fmt.Errorf("failure while upserting sub_data")
	}
	return 0, nil
}

func (p *RecurringTxnsProcessorImpl) upsertSubscriptionData(ctx context.Context, subsData []*types.SubscriptionData,
	wg *sync.WaitGroup, ch chan int, errCh chan error) {
	defer logger.RecoverPanicAndError(ctx)
	defer wg.Done()

	for {
		idx, ok := <-ch
		if !ok {
			break
		}
		err := p.subsManager.UpsertSubscriptionData(ctx, subsData[idx])
		if err != nil {
			errCh <- fmt.Errorf("failed to upsert subscription data for sub_id %s : %w", subsData[idx].Subscription.GetId(), err)
			break
		}
	}
}
