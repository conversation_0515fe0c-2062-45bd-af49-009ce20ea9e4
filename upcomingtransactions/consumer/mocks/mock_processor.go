// Code generated by MockGen. DO NOT EDIT.
// Source: processor.go

// Package mock_consumer is a generated GoMock package.
package mock_consumer

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockRecurringTxnsProcessor is a mock of RecurringTxnsProcessor interface.
type MockRecurringTxnsProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockRecurringTxnsProcessorMockRecorder
}

// MockRecurringTxnsProcessorMockRecorder is the mock recorder for MockRecurringTxnsProcessor.
type MockRecurringTxnsProcessorMockRecorder struct {
	mock *MockRecurringTxnsProcessor
}

// NewMockRecurringTxnsProcessor creates a new mock instance.
func NewMockRecurringTxnsProcessor(ctrl *gomock.Controller) *MockRecurringTxnsProcessor {
	mock := &MockRecurringTxnsProcessor{ctrl: ctrl}
	mock.recorder = &MockRecurringTxnsProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRecurringTxnsProcessor) EXPECT() *MockRecurringTxnsProcessorMockRecorder {
	return m.recorder
}

// ProcessRecurringTxns mocks base method.
func (m *MockRecurringTxnsProcessor) ProcessRecurringTxns(ctx context.Context, fileContent []byte, numOfGoroutines int) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessRecurringTxns", ctx, fileContent, numOfGoroutines)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessRecurringTxns indicates an expected call of ProcessRecurringTxns.
func (mr *MockRecurringTxnsProcessorMockRecorder) ProcessRecurringTxns(ctx, fileContent, numOfGoroutines interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessRecurringTxns", reflect.TypeOf((*MockRecurringTxnsProcessor)(nil).ProcessRecurringTxns), ctx, fileContent, numOfGoroutines)
}
