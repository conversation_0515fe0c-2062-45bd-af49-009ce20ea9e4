// Code generated by MockGen. DO NOT EDIT.
// Source: parser.go

// Package mock_consumer is a generated GoMock package.
package mock_consumer

import (
	reflect "reflect"

	types "github.com/epifi/gamma/upcomingtransactions/consumer/types"
	gomock "github.com/golang/mock/gomock"
)

// MockSubscriptionParser is a mock of SubscriptionParser interface.
type MockSubscriptionParser struct {
	ctrl     *gomock.Controller
	recorder *MockSubscriptionParserMockRecorder
}

// MockSubscriptionParserMockRecorder is the mock recorder for MockSubscriptionParser.
type MockSubscriptionParserMockRecorder struct {
	mock *MockSubscriptionParser
}

// NewMockSubscriptionParser creates a new mock instance.
func NewMockSubscriptionParser(ctrl *gomock.Controller) *MockSubscriptionParser {
	mock := &MockSubscriptionParser{ctrl: ctrl}
	mock.recorder = &MockSubscriptionParserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSubscriptionParser) EXPECT() *MockSubscriptionParserMockRecorder {
	return m.recorder
}

// Parse mocks base method.
func (m *MockSubscriptionParser) Parse(fileContent []byte) ([]*types.SubscriptionData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Parse", fileContent)
	ret0, _ := ret[0].([]*types.SubscriptionData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Parse indicates an expected call of Parse.
func (mr *MockSubscriptionParserMockRecorder) Parse(fileContent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Parse", reflect.TypeOf((*MockSubscriptionParser)(nil).Parse), fileContent)
}
