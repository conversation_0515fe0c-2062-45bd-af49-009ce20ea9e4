package consumer_test

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/upcomingtransactions/test"
)

// nolint:dogsled
// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, gconf, _, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
