package consumer_test

import (
	"fmt"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/order/payment"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/upcomingtransactions/consumer"
	"github.com/epifi/gamma/upcomingtransactions/consumer/types"
)

var (
	content1                            = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"weekly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}\n{\"last_transaction_id\":\"txn2\",\"actor_from_id\":\"c9bfb02a-38d9-48b2-9050-4dd17a892d37\",\"merchant_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"monthly\",\"p2p_p2m\":\"P2M\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b45bf68a-e413-11ed-8d34-029740d48c40\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"649.0\",\"next_predicted_transaction_date_max\":\"2023-04-27\",\"next_predicted_transaction_date_min\":\"2023-04-25\",\"next_predicted_transaction_amount_min\":\"649.0\"}]}\n{\"last_transaction_id\":\"txn3\",\"actor_from_id\":\"ff8002e4-0b91-4ad5-8a8b-38f8cb99f2ef\",\"merchant_id\":\"********-8343-42a0-a7d7-54053c0825e7\",\"frequency\":\"monthly\",\"p2p_p2m\":\"P2M\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b39a3b6c-e413-11ed-bc17-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"130.0\",\"next_predicted_transaction_date_max\":\"2023-05-11\",\"next_predicted_transaction_date_min\":\"2023-05-09\",\"next_predicted_transaction_amount_min\":\"130.0\"}]}"
	content2                            = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"weekly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"expired\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}\n{\"last_transaction_id\":\"txn2\",\"actor_from_id\":\"c9bfb02a-38d9-48b2-9050-4dd17a892d37\",\"merchant_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"monthly\",\"p2p_p2m\":\"P2M\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b45bf68a-e413-11ed-8d34-029740d48c40\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"649.0\",\"next_predicted_transaction_date_max\":\"2023-04-27\",\"next_predicted_transaction_date_min\":\"2023-04-25\",\"next_predicted_transaction_amount_min\":\"649.0\"}]}\n{\"last_transaction_id\":\"txn3\",\"actor_from_id\":\"ff8002e4-0b91-4ad5-8a8b-38f8cb99f2ef\",\"merchant_id\":\"********-8343-42a0-a7d7-54053c0825e7\",\"frequency\":\"monthly\",\"p2p_p2m\":\"P2M\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b39a3b6c-e413-11ed-bc17-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"130.0\",\"next_predicted_transaction_date_max\":\"2023-05-11\",\"next_predicted_transaction_date_min\":\"2023-05-09\",\"next_predicted_transaction_amount_min\":\"130.0\"}]}"
	invalidFreqContent                  = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"abc\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}"
	invalidSubStateContent              = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"Monthly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"temp\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}"
	bothActorAndMerchantFoundContent    = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"merchant_id\":\"merchant\",\"frequency\":\"Monthly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}"
	neitherActorAndMerchantFoundContent = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"frequency\":\"Monthly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}"
	minAmountInvalid                    = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"weekly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"abcd\"}]}"
	maxAmountInvalid                    = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"weekly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"abc\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}"
	minDateInvalid                      = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"weekly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"abc\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}"
	maxDateInvalid                      = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"weekly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"abc\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}"
	fromActorIdEmpty                    = "{\"last_transaction_id\":\"txn1\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"weekly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"Debit\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}"
	accountingEntryInvalid              = "{\"last_transaction_id\":\"txn1\",\"actor_from_id\":\"a0d57e91-2b4b-462c-8e65-01acd5c3c28c\",\"actor_to_id\":\"0053144f-da9b-4ac1-8ed8-577e2d677f69\",\"frequency\":\"weekly\",\"p2p_p2m\":\"P2P\",\"credit_debit\":\"abc\",\"subscription_type\":\"merchant_template\",\"subscription_id\":\"SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee\",\"subscription_status\":\"active\",\"created_at\":\"04/26/2023 09:03:41.520\",\"last_updated_at\":\"04/26/2023 09:03:41.520\",\"Transactions\":[{\"next_predicted_transaction_amount_max\":\"199.0\",\"next_predicted_transaction_date_max\":\"2023-05-03\",\"next_predicted_transaction_date_min\":\"2023-05-01\",\"next_predicted_transaction_amount_min\":\"199.0\"}]}"
)

func TestSubscriptionParserImpl_Parse(t *testing.T) {
	type args struct {
		fileContent []byte
	}
	tests := []struct {
		name    string
		args    args
		want    []*types.SubscriptionData
		wantErr bool
	}{
		{
			name: "invalid frequency value, parse failure",
			args: args{
				fileContent: []byte(invalidFreqContent),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "invalid Subscription state value, parse failure",
			args: args{
				fileContent: []byte(invalidSubStateContent),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "both merchant and to_actor present, parse failure",
			args: args{
				fileContent: []byte(bothActorAndMerchantFoundContent),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "neither merchant nor to_actor present, parse failure",
			args: args{
				fileContent: []byte(neitherActorAndMerchantFoundContent),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "min amount is invalid, parse failure",
			args: args{
				fileContent: []byte(minAmountInvalid),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "max amount is invalid, parse failure",
			args: args{
				fileContent: []byte(maxAmountInvalid),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "min date is invalid, parse failure",
			args: args{
				fileContent: []byte(minDateInvalid),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "max date is invalid, parse failure",
			args: args{
				fileContent: []byte(maxDateInvalid),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "actor_from_id is empty, parse failure",
			args: args{
				fileContent: []byte(fromActorIdEmpty),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "credit_debit is invalid, parse failure",
			args: args{
				fileContent: []byte(accountingEntryInvalid),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "parse success - 1",
			args: args{
				fileContent: []byte(content1),
			},
			want: []*types.SubscriptionData{
				{
					Subscription: &modelPb.Subscription{
						Id:             "SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee",
						ActorId:        "a0d57e91-2b4b-462c-8e65-01acd5c3c28c",
						Frequency:      modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_WEEKLY,
						State:          modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
						WithEntityId:   "0053144f-da9b-4ac1-8ed8-577e2d677f69",
						WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					},
					Transactions: []*modelPb.UpcomingTransaction{
						{
							ComputedHash:   "YWLhf5T-jLFrpwm1xx7nhv8LK78xsX-D5m9wmL3p7Uw=",
							ActorId:        "a0d57e91-2b4b-462c-8e65-01acd5c3c28c",
							WithEntityId:   "0053144f-da9b-4ac1-8ed8-577e2d677f69",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        199,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        199,
								Nanos:        0,
							},
							MinDate:           timestamppb.New(time.Date(2023, 05, 1, 0, 0, 0, 0, datetime.IST)),
							MaxDate:           timestamppb.New(time.Date(2023, 05, 3, 23, 59, 59, 0, datetime.IST)),
							SubscriptionId:    "SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee",
							CreditDebit:       payment.AccountingEntryType_DEBIT,
							LastTransactionId: "txn1",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2P,
						},
					},
				},
				{
					Subscription: &modelPb.Subscription{
						Id:             "SUB-b45bf68a-e413-11ed-8d34-029740d48c40",
						ActorId:        "c9bfb02a-38d9-48b2-9050-4dd17a892d37",
						Frequency:      modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
						State:          modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
						WithEntityId:   "0053144f-da9b-4ac1-8ed8-577e2d677f69",
						WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
					},
					Transactions: []*modelPb.UpcomingTransaction{
						{
							ComputedHash:   "LQQKbmOIr4cm2gchLlzY8EiSTn73IhzAaZamEf7Kp5c=",
							ActorId:        "c9bfb02a-38d9-48b2-9050-4dd17a892d37",
							WithEntityId:   "0053144f-da9b-4ac1-8ed8-577e2d677f69",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        649,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        649,
								Nanos:        0,
							},
							MinDate:           timestamppb.New(time.Date(2023, 04, 25, 0, 0, 0, 0, datetime.IST)),
							MaxDate:           timestamppb.New(time.Date(2023, 04, 27, 23, 59, 59, 0, datetime.IST)),
							SubscriptionId:    "SUB-b45bf68a-e413-11ed-8d34-029740d48c40",
							CreditDebit:       payment.AccountingEntryType_DEBIT,
							LastTransactionId: "txn2",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2M,
						},
					},
				},
				{
					Subscription: &modelPb.Subscription{
						Id:             "SUB-b39a3b6c-e413-11ed-bc17-02e97811c8ee",
						ActorId:        "ff8002e4-0b91-4ad5-8a8b-38f8cb99f2ef",
						Frequency:      modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
						State:          modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
						WithEntityId:   "********-8343-42a0-a7d7-54053c0825e7",
						WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
					},
					Transactions: []*modelPb.UpcomingTransaction{
						{
							ComputedHash:   "F-cV3oSSMAXVfmyLzHkm9gi8ESgdoEXuBw8PNx4l5Xs=",
							ActorId:        "ff8002e4-0b91-4ad5-8a8b-38f8cb99f2ef",
							WithEntityId:   "********-8343-42a0-a7d7-54053c0825e7",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        130,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        130,
								Nanos:        0,
							},
							MinDate:           timestamppb.New(time.Date(2023, 05, 9, 0, 0, 0, 0, datetime.IST)),
							MaxDate:           timestamppb.New(time.Date(2023, 05, 11, 23, 59, 59, 0, datetime.IST)),
							SubscriptionId:    "SUB-b39a3b6c-e413-11ed-bc17-02e97811c8ee",
							CreditDebit:       payment.AccountingEntryType_DEBIT,
							LastTransactionId: "txn3",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2M,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "parse success - 2",
			args: args{
				fileContent: []byte(content2),
			},
			want: []*types.SubscriptionData{
				{
					Subscription: &modelPb.Subscription{
						Id:             "SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee",
						ActorId:        "a0d57e91-2b4b-462c-8e65-01acd5c3c28c",
						Frequency:      modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_WEEKLY,
						State:          modelPb.SubscriptionState_SUBSCRIPTION_STATE_EXPIRED,
						WithEntityId:   "0053144f-da9b-4ac1-8ed8-577e2d677f69",
						WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					},
					Transactions: []*modelPb.UpcomingTransaction{
						{
							ComputedHash:   "YWLhf5T-jLFrpwm1xx7nhv8LK78xsX-D5m9wmL3p7Uw=",
							ActorId:        "a0d57e91-2b4b-462c-8e65-01acd5c3c28c",
							WithEntityId:   "0053144f-da9b-4ac1-8ed8-577e2d677f69",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        199,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        199,
								Nanos:        0,
							},
							MinDate:           timestamppb.New(time.Date(2023, 05, 1, 0, 0, 0, 0, datetime.IST)),
							MaxDate:           timestamppb.New(time.Date(2023, 05, 3, 23, 59, 59, 0, datetime.IST)),
							SubscriptionId:    "SUB-b419b8c4-e413-11ed-b3c1-02e97811c8ee",
							CreditDebit:       payment.AccountingEntryType_DEBIT,
							LastTransactionId: "txn1",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_DS_INVALIDATED,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2P,
						},
					},
				},
				{
					Subscription: &modelPb.Subscription{
						Id:             "SUB-b45bf68a-e413-11ed-8d34-029740d48c40",
						ActorId:        "c9bfb02a-38d9-48b2-9050-4dd17a892d37",
						Frequency:      modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
						State:          modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
						WithEntityId:   "0053144f-da9b-4ac1-8ed8-577e2d677f69",
						WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
					},
					Transactions: []*modelPb.UpcomingTransaction{
						{
							ComputedHash:   "LQQKbmOIr4cm2gchLlzY8EiSTn73IhzAaZamEf7Kp5c=",
							ActorId:        "c9bfb02a-38d9-48b2-9050-4dd17a892d37",
							WithEntityId:   "0053144f-da9b-4ac1-8ed8-577e2d677f69",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        649,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        649,
								Nanos:        0,
							},
							MinDate:           timestamppb.New(time.Date(2023, 04, 25, 0, 0, 0, 0, datetime.IST)),
							MaxDate:           timestamppb.New(time.Date(2023, 04, 27, 23, 59, 59, 0, datetime.IST)),
							SubscriptionId:    "SUB-b45bf68a-e413-11ed-8d34-029740d48c40",
							CreditDebit:       payment.AccountingEntryType_DEBIT,
							LastTransactionId: "txn2",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2M,
						},
					},
				},
				{
					Subscription: &modelPb.Subscription{
						Id:             "SUB-b39a3b6c-e413-11ed-bc17-02e97811c8ee",
						ActorId:        "ff8002e4-0b91-4ad5-8a8b-38f8cb99f2ef",
						Frequency:      modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
						State:          modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
						WithEntityId:   "********-8343-42a0-a7d7-54053c0825e7",
						WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
					},
					Transactions: []*modelPb.UpcomingTransaction{
						{
							ComputedHash:   "F-cV3oSSMAXVfmyLzHkm9gi8ESgdoEXuBw8PNx4l5Xs=",
							ActorId:        "ff8002e4-0b91-4ad5-8a8b-38f8cb99f2ef",
							WithEntityId:   "********-8343-42a0-a7d7-54053c0825e7",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        130,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        130,
								Nanos:        0,
							},
							MinDate:           timestamppb.New(time.Date(2023, 05, 9, 0, 0, 0, 0, datetime.IST)),
							MaxDate:           timestamppb.New(time.Date(2023, 05, 11, 23, 59, 59, 0, datetime.IST)),
							SubscriptionId:    "SUB-b39a3b6c-e413-11ed-bc17-02e97811c8ee",
							CreditDebit:       payment.AccountingEntryType_DEBIT,
							LastTransactionId: "txn3",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2M,
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := consumer.NewSubscriptionParserImpl()
			got, err := p.Parse(tt.args.fileContent)
			if (err != nil) != tt.wantErr {
				t.Errorf("Parse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				return
			}
			if matchErr := isSubscriptionDataArrEqual(got, tt.want); matchErr != nil {
				t.Errorf("Parse() got : %s\n and want : %s\n differ : %v", getSubDataArrString(got), getSubDataArrString(tt.want), matchErr)
			}
		})
	}
}

func isSubscriptionDataArrEqual(actualArr, expectedArr []*types.SubscriptionData) error {
	if len(actualArr) != len(expectedArr) {
		return fmt.Errorf("len of actual %d is different from %d", len(actualArr), len(expectedArr))
	}
	for _, expectedEntry := range expectedArr {
		found := false
		for _, actualEntry := range actualArr {
			if isSubscriptionDataEqual(actualEntry, expectedEntry) {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("expected entry not found in got result : %v %v", expectedEntry.Subscription, expectedEntry.Transactions)
		}
	}
	return nil
}

func isSubscriptionDataEqual(actual, want *types.SubscriptionData) bool {
	if diff := cmp.Diff(actual.Subscription, want.Subscription, protocmp.Transform()); (actual.Subscription != nil || want.Subscription != nil) && diff != "" {
		return false
	}
	if diff := cmp.Diff(actual.Transactions, want.Transactions, protocmp.Transform()); (actual.Transactions != nil || want.Transactions != nil) && diff != "" {
		return false
	}
	return true
}

func getSubDataArrString(subs []*types.SubscriptionData) string {
	str := ""
	for _, sub := range subs {
		str += fmt.Sprintf("%s : ", sub.Subscription.String())
		for _, txn := range sub.Transactions {
			str += fmt.Sprintf("%s,\t", txn.String())
		}
		str += "\n"
	}
	return str
}
