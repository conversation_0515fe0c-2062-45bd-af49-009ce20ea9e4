package consumer_test

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/actor"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	types "github.com/epifi/gamma/api/typesv2"
	consumerPb "github.com/epifi/gamma/api/upcomingtransactions/consumer"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mock"
	"github.com/epifi/gamma/upcomingtransactions/consumer"
)

var (
	feb10 = timestamppb.New(time.Date(2023, 02, 10, 0, 0, 0, 0, datetime.IST))
	feb15 = timestamppb.New(time.Date(2023, 02, 15, 0, 0, 0, 0, datetime.IST))
	feb16 = timestamppb.New(time.Date(2023, 02, 16, 0, 0, 0, 0, datetime.IST))
	feb20 = timestamppb.New(time.Date(2023, 02, 20, 0, 0, 0, 0, datetime.IST))

	actor1 = "actor-1"
	actor2 = "actor-2"
)

func TestConsumerService_UpdateUpcomingTxnState(t *testing.T) {
	logger.Init(cfg.TestEnv)
	type args struct {
		ctx        context.Context
		orderEvent *orderPb.OrderUpdate
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *serviceFields)
		want       *consumerPb.UpdateUpcomingTxnStateResponse
		wantErr    bool
	}{
		{
			name: "failed to get actor details, transient failure",
			args: args{
				ctx: context.Background(),
				orderEvent: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: actor1,
							ToActorId:   actor2,
							Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								DebitedAt: feb15,
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.actorClient.EXPECT().GetActorById(gomock.Any(), mock.NewProtoMatcher(&actor.GetActorByIdRequest{
					Id: actor1,
				})).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "actor-1-entity",
					},
				}, nil).MaxTimes(1)
				f.actorClient.EXPECT().GetActorById(gomock.Any(), mock.NewProtoMatcher(&actor.GetActorByIdRequest{
					Id: actor2,
				})).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &consumerPb.UpdateUpcomingTxnStateResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to get actor upcoming transactions",
			args: args{
				ctx: context.Background(),
				orderEvent: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: actor1,
							ToActorId:   actor2,
							Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
							Status:      orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								DebitedAt: feb15,
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.actorClient.EXPECT().GetActorById(gomock.Any(), mock.NewProtoMatcher(&actor.GetActorByIdRequest{
					Id: actor1,
				})).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "actor-1-entity",
					},
				}, nil)
				f.actorClient.EXPECT().GetActorById(gomock.Any(), mock.NewProtoMatcher(&actor.GetActorByIdRequest{
					Id: actor2,
				})).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_EXTERNAL_MERCHANT,
						EntityId: "actor-2-entity",
					},
				}, nil)
				f.subsManager.EXPECT().GetUpcomingTransactionsWithEntityAfter(gomock.Any(), actor1, "actor-2-entity", mock.NewProtoMatcher(feb15)).Return([]*modelPb.UpcomingTransaction{
					{
						Id:              "txn-1",
						MinDate:         feb10,
						MaxDate:         feb15,
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_USER_INVALIDATED,
					},
					{
						Id:              "txn-2",
						MinDate:         feb10,
						MaxDate:         feb20,
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_EXECUTED,
					},
					{
						Id:              "txn-3",
						MinDate:         feb15,
						MaxDate:         feb20,
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
				}, nil).MaxTimes(1)
				f.subsManager.EXPECT().GetUpcomingTransactionsWithEntityAfter(gomock.Any(), actor2, actor1, mock.NewProtoMatcher(feb15)).Return(nil, fmt.Errorf("err"))

				f.subsManager.EXPECT().UpdateTxnStatusByActorAndTxnId(gomock.Any(), actor1, "txn-3", modelPb.TxnStatus_TXN_STATUS_EXECUTED).Return(nil).MaxTimes(1)
			},
			want: &consumerPb.UpdateUpcomingTxnStateResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to update txn state, transient failure",
			args: args{
				ctx: context.Background(),
				orderEvent: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: actor1,
							ToActorId:   actor2,
							Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
							Status:      orderPb.OrderStatus_PAID,
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        150,
								Nanos:        250000000,
							},
						},
						Transactions: []*paymentPb.Transaction{
							{
								DebitedAt: feb15,
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.actorClient.EXPECT().GetActorById(gomock.Any(), mock.NewProtoMatcher(&actor.GetActorByIdRequest{
					Id: actor1,
				})).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "actor-1-entity",
					},
				}, nil)
				f.actorClient.EXPECT().GetActorById(gomock.Any(), mock.NewProtoMatcher(&actor.GetActorByIdRequest{
					Id: actor2,
				})).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_EXTERNAL_MERCHANT,
						EntityId: "actor-2-entity",
					},
				}, nil)
				f.subsManager.EXPECT().GetUpcomingTransactionsWithEntityAfter(gomock.Any(), actor1, "actor-2-entity", mock.NewProtoMatcher(feb15)).Return([]*modelPb.UpcomingTransaction{
					{
						Id:      "txn-1",
						MinDate: feb10,
						MaxDate: feb15,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_USER_INVALIDATED,
					},
					{
						Id:      "txn-2",
						MinDate: feb10,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_EXECUTED,
					},
					{
						Id:      "txn-3",
						MinDate: feb15,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
				}, nil)
				f.subsManager.EXPECT().GetUpcomingTransactionsWithEntityAfter(gomock.Any(), actor2, actor1, mock.NewProtoMatcher(feb15)).Return([]*modelPb.UpcomingTransaction{
					{
						Id:      "txn-4",
						MinDate: feb10,
						MaxDate: feb15,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_DS_INVALIDATED,
					},
					{
						Id:      "txn-5",
						MinDate: feb10,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
					{
						Id:      "txn-6",
						MinDate: feb15,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
				}, nil).MaxTimes(1)
				f.subsManager.EXPECT().UpdateTxnStatusByActorAndTxnId(gomock.Any(), actor1, "txn-3", modelPb.TxnStatus_TXN_STATUS_EXECUTED).Return(fmt.Errorf("err"))
				f.subsManager.EXPECT().UpdateTxnStatusByActorAndTxnId(gomock.Any(), actor2, "txn-5", modelPb.TxnStatus_TXN_STATUS_EXECUTED).Return(nil).MaxTimes(1)
			},
			want: &consumerPb.UpdateUpcomingTxnStateResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "no txn to update state, success",
			args: args{
				ctx: context.Background(),
				orderEvent: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: actor1,
							ToActorId:   actor2,
							Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        150,
							},
							Status: orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								DebitedAt: feb15,
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.actorClient.EXPECT().GetActorById(gomock.Any(), mock.NewProtoMatcher(&actor.GetActorByIdRequest{
					Id: actor1,
				})).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "actor-1-entity",
					},
				}, nil)
				f.actorClient.EXPECT().GetActorById(gomock.Any(), mock.NewProtoMatcher(&actor.GetActorByIdRequest{
					Id: actor2,
				})).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_EXTERNAL_MERCHANT,
						EntityId: "actor-2-entity",
					},
				}, nil)
				f.subsManager.EXPECT().GetUpcomingTransactionsWithEntityAfter(gomock.Any(), actor1, "actor-2-entity", mock.NewProtoMatcher(feb15)).Return([]*modelPb.UpcomingTransaction{
					{
						Id:      "txn-1",
						MinDate: feb10,
						MaxDate: feb15,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_EXECUTED,
					},
					{
						Id:      "txn-2",
						MinDate: feb16,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
					{
						Id:      "txn-3",
						MinDate: feb15,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_USER_INVALIDATED,
					},
					{
						Id:      "txn-4",
						MinDate: feb10,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        160,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
				}, nil)
				f.subsManager.EXPECT().GetUpcomingTransactionsWithEntityAfter(gomock.Any(), actor2, actor1, mock.NewProtoMatcher(feb15)).Return([]*modelPb.UpcomingTransaction{}, nil)
			},
			want: &consumerPb.UpdateUpcomingTxnStateResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "successfully updated upcoming txns state",
			args: args{
				ctx: context.Background(),
				orderEvent: &orderPb.OrderUpdate{
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: actor1,
							ToActorId:   actor2,
							Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        150,
							},
							Status: orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								DebitedAt: feb15,
							},
						},
					},
				},
			},
			setupMocks: func(f *serviceFields) {
				f.actorClient.EXPECT().GetActorById(gomock.Any(), mock.NewProtoMatcher(&actor.GetActorByIdRequest{
					Id: actor1,
				})).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "actor-1-entity",
					},
				}, nil)
				f.actorClient.EXPECT().GetActorById(gomock.Any(), mock.NewProtoMatcher(&actor.GetActorByIdRequest{
					Id: actor2,
				})).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_EXTERNAL_MERCHANT,
						EntityId: "actor-2-entity",
					},
				}, nil)
				f.subsManager.EXPECT().GetUpcomingTransactionsWithEntityAfter(gomock.Any(), actor1, "actor-2-entity", mock.NewProtoMatcher(feb15)).Return([]*modelPb.UpcomingTransaction{
					{
						Id:      "txn-1",
						MinDate: feb10,
						MaxDate: feb15,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_USER_INVALIDATED,
					},
					{
						Id:      "txn-2",
						MinDate: feb10,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_EXECUTED,
					},
					{
						Id:      "txn-5",
						MinDate: feb10,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        160,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
					{
						Id:      "txn-3",
						MinDate: feb15,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
					{
						Id:      "txn-4",
						MinDate: feb16,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
				}, nil)
				f.subsManager.EXPECT().GetUpcomingTransactionsWithEntityAfter(gomock.Any(), actor2, actor1, mock.NewProtoMatcher(feb15)).Return([]*modelPb.UpcomingTransaction{
					{
						Id:      "txn-4",
						MinDate: feb10,
						MaxDate: feb15,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_DS_INVALIDATED,
					},
					{
						Id:      "txn-5",
						MinDate: feb10,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
					{
						Id:      "txn-6",
						MinDate: feb15,
						MaxDate: feb20,
						MinAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						MaxAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        200,
							Nanos:        0,
						},
						ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					},
				}, nil)
				f.subsManager.EXPECT().UpdateTxnStatusByActorAndTxnId(gomock.Any(), actor1, "txn-3", modelPb.TxnStatus_TXN_STATUS_EXECUTED).Return(nil)
				f.subsManager.EXPECT().UpdateTxnStatusByActorAndTxnId(gomock.Any(), actor2, "txn-5", modelPb.TxnStatus_TXN_STATUS_EXECUTED).Return(nil)
			},
			want: &consumerPb.UpdateUpcomingTxnStateResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initServiceFields(ctrl)
			tt.setupMocks(f)

			s := consumer.NewConsumerService(nil, nil, f.txnsProcessor, f.subsManager, f.actorClient)
			got, err := s.UpdateUpcomingTxnState(tt.args.ctx, tt.args.orderEvent)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateUpcomingTxnState() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateUpcomingTxnState() got = %v, want %v", got, tt.want)
			}
		})
	}
}
