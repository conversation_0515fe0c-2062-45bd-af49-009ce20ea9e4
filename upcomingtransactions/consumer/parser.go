//go:generate mockgen -source=parser.go -destination=./mocks/mock_parser.go package=mocks
package consumer

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/wire"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/upcomingtransactions"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/upcomingtransactions/consumer/types"
	"github.com/epifi/gamma/upcomingtransactions/utils"
)

const DateLayout = "2006-01-02"

var SubscriptionParserWireSet = wire.NewSet(NewSubscriptionParserImpl, wire.Bind(new(SubscriptionParser), new(*SubscriptionParserImpl)))

type SubscriptionParser interface {
	Parse(fileContent []byte) ([]*types.SubscriptionData, error)
}

type SubscriptionParserImpl struct {
}

func NewSubscriptionParserImpl() *SubscriptionParserImpl {
	return &SubscriptionParserImpl{}
}

// Parse method parses the raw text ds input into SubscriptionData type
func (p *SubscriptionParserImpl) Parse(fileContent []byte) ([]*types.SubscriptionData, error) {
	var subsRes []*types.SubscriptionData
	rows := strings.Split(string(fileContent), "\n")
	for idx, row := range rows {
		if row == "" {
			continue
		}
		dsData := &upcomingtransactions.DsSubscriptionData{}
		err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(row), dsData)
		if err != nil {
			return nil, fmt.Errorf("failed to parse json : %w", err)
		}
		subData, err := p.getSubscriptionData(dsData)
		if err != nil {
			return nil, fmt.Errorf("failed to get subscriptionData from ds sub data for sub_id %s idx = %d err : %w", dsData.GetSubscriptionId(), idx, err)
		}
		subsRes = append(subsRes, subData)
	}
	return subsRes, nil
}

// nolint:funlen
func (p *SubscriptionParserImpl) getSubscriptionData(dsData *upcomingtransactions.DsSubscriptionData) (*types.SubscriptionData, error) {
	var (
		txns []*modelPb.UpcomingTransaction
	)
	subFreq, err := convertStringToFreq(dsData.GetFrequency())
	if err != nil {
		return nil, err
	}
	subState, err := convertStringToState(dsData.GetSubStatus())
	if err != nil {
		return nil, err
	}
	withEntityId, withEntityType, txnType, err := getWithEntityIdAndType(dsData.GetWithActorId(), dsData.GetWithMerchantId())
	if err != nil {
		return nil, err
	}
	sub := &modelPb.Subscription{
		Id:             dsData.GetSubscriptionId(),
		ActorId:        dsData.GetActorId(),
		Frequency:      subFreq,
		State:          subState,
		WithEntityId:   withEntityId,
		WithEntityType: withEntityType,
	}
	txnExecutionState, err := getTxnExecutionStateBySubState(sub.GetState())
	if err != nil {
		return nil, err
	}
	for idx, dsTxn := range dsData.GetTransactions() {
		minAmount, maxAmount, minDate, maxDate, err := getAmountsAndDates(dsTxn)
		if err != nil {
			return nil, fmt.Errorf("failed to get amounts and dates for txn with idx %d for sub_id %s : %w", idx, dsData.GetSubscriptionId(), err)
		}
		creditDebit, err := parseCreditDebit(dsData.GetCreditDebit())
		if err != nil {
			return nil, fmt.Errorf("failed to parse credit/debit for txn with idx %d for sub_id %s : %w", idx, dsData.GetSubscriptionId(), err)
		}
		newTxn := &modelPb.UpcomingTransaction{
			ActorId:           dsData.GetActorId(),
			WithEntityId:      withEntityId,
			WithEntityType:    withEntityType,
			MinAmount:         minAmount,
			MaxAmount:         maxAmount,
			MinDate:           minDate,
			MaxDate:           maxDate,
			SubscriptionId:    dsData.GetSubscriptionId(),
			CreditDebit:       creditDebit,
			LastTransactionId: dsData.GetLastTxnId(),
			ExecutionStatus:   txnExecutionState,
			Source:            modelPb.TxnSource_TXN_SOURCE_DS,
			Type:              txnType,
		}
		computedHash, err := utils.GetUpcomingTransactionComputedHash(newTxn)
		if err != nil {
			return nil, fmt.Errorf("failed to get computed hash for txn with idx %d for sub_id %s : %w", idx, dsData.GetSubscriptionId(), err)
		}
		newTxn.ComputedHash = computedHash
		txns = append(txns, newTxn)
	}
	return &types.SubscriptionData{
		Subscription: sub,
		Transactions: txns,
	}, nil
}

func getAmountsAndDates(dsTxn *upcomingtransactions.DsSubTransaction) (*moneyPb.Money, *moneyPb.Money, *timestamppb.Timestamp, *timestamppb.Timestamp, error) {
	minAmount, err := parseFloatAmount(dsTxn.GetMinAmount())
	if err != nil {
		return nil, nil, nil, nil, err
	}
	maxAmount, err := parseFloatAmount(dsTxn.GetMaxAmount())
	if err != nil {
		return nil, nil, nil, nil, err
	}
	minDate, err := getTimeFromString(dsTxn.GetMinDate())
	if err != nil {
		return nil, nil, nil, nil, err
	}
	maxDate, err := getTimeFromString(dsTxn.GetMaxDate())
	if err != nil {
		return nil, nil, nil, nil, err
	}
	return money.ParseFloat(minAmount, "INR"), money.ParseFloat(maxAmount, "INR"),
		timestamppb.New(datetime.StartOfDay(minDate)), timestamppb.New(datetime.EndOfDay(maxDate)), nil
}

func parseFloatAmount(str string) (float64, error) {
	return strconv.ParseFloat(str, 32)
}

func parseCreditDebit(str string) (payment.AccountingEntryType, error) {
	switch strings.ToUpper(str) {
	case "DEBIT":
		return payment.AccountingEntryType_DEBIT, nil
	case "CREDIT":
		return payment.AccountingEntryType_CREDIT, nil
	default:
		return 0, fmt.Errorf("unhandled credit_debit %s in ds input", str)
	}
}

func convertStringToFreq(str string) (modelPb.SubscriptionFrequency, error) {
	if val, found := modelPb.SubscriptionFrequency_value["SUBSCRIPTION_FREQUENCY_"+strings.ToUpper(str)]; found {
		return modelPb.SubscriptionFrequency(val), nil
	}
	return 0, fmt.Errorf("invalid sub_freq : %s", str)
}

func convertStringToState(str string) (modelPb.SubscriptionState, error) {
	if val, found := modelPb.SubscriptionState_value["SUBSCRIPTION_STATE_"+strings.ToUpper(str)]; found {
		return modelPb.SubscriptionState(val), nil
	}
	return 0, fmt.Errorf("invalid sub_state : %s", str)
}

func getWithEntityIdAndType(actorId, merchantId string) (string, modelPb.EntityType, modelPb.TxnType, error) {
	switch {
	case len(actorId) > 0 && len(merchantId) > 0:
		return "", 0, 0, fmt.Errorf("both to_actor and to_merchnat present in ds input, failure")
	case len(actorId) > 0:
		return actorId, modelPb.EntityType_ENTITY_TYPE_ACTOR, modelPb.TxnType_TXN_TYPE_P2P, nil
	case len(merchantId) > 0:
		return merchantId, modelPb.EntityType_ENTITY_TYPE_MERCHANT, modelPb.TxnType_TXN_TYPE_P2M, nil
	default:
		return "", 0, 0, fmt.Errorf("neither to_actor nor to_merchant present in ds input, failure")
	}
}

// given a date string in DateLayout format, it then returns the parsed date converted to timestamp
func getTimeFromString(dateStr string) (time.Time, error) {
	layout := DateLayout
	parsedDate, dateParseErr := time.ParseInLocation(layout, dateStr, datetime.IST)
	if dateParseErr != nil {
		return time.Time{}, fmt.Errorf("failed to parse date to given DateLayout format : %w", dateParseErr)
	}
	return parsedDate, nil
}

func getTxnExecutionStateBySubState(subState modelPb.SubscriptionState) (modelPb.TxnStatus, error) {
	switch subState {
	case modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE:
		return modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET, nil
	case modelPb.SubscriptionState_SUBSCRIPTION_STATE_EXPIRED:
		return modelPb.TxnStatus_TXN_STATUS_DS_INVALIDATED, nil
	default:
		return 0, fmt.Errorf("ds sub state is not active/active : %s", subState.String())
	}
}
