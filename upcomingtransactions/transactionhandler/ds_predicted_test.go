package transactionhandler

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	upcomingTransactionsPb "github.com/epifi/gamma/api/upcomingtransactions"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	mock_dao "github.com/epifi/gamma/upcomingtransactions/dao/mocks"
)

var (
	txn1 = &modelPb.UpcomingTransaction{
		WithEntityId:   "swiggy-entity",
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        250,
			Nanos:        *********,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        300,
			Nanos:        *********,
		},
		MinDate:     feb5,
		MaxDate:     feb10,
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
	}
	txn2 = &modelPb.UpcomingTransaction{
		WithEntityId:   "zomato-entity",
		WithEntityType: modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        400,
			Nanos:        0,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        500,
			Nanos:        0,
		},
		MinDate:     feb10,
		MaxDate:     feb12,
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
	}
)

func TestDsPredictedTransactionHandlerImpl_ConvertedToUpcomingTxns(t *testing.T) {
	type args struct {
		ctx             context.Context
		actorId         string
		minTime         *timestamppb.Timestamp
		maxTime         *timestamppb.Timestamp
		accountingEntry paymentPb.AccountingEntryType
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *dsPredictedTxnHandlerFields)
		want       []*upcomingTransactionsPb.UpcomingTransaction
		wantErr    bool
	}{
		{
			name: "failure in getting upcoming txns from dao",
			args: args{
				ctx:             context.Background(),
				actorId:         "actor-1",
				minTime:         feb5,
				maxTime:         feb12,
				accountingEntry: paymentPb.AccountingEntryType_DEBIT,
			},
			setupMocks: func(f *dsPredictedTxnHandlerFields) {
				f.upcomingTxnsDao.EXPECT().GetByActor(gomock.Any(), "actor-1", gomock.Any()).Return(nil, fmt.Errorf("err"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "successfully convert to upcoming txns",
			args: args{
				ctx:             context.Background(),
				actorId:         "actor-1",
				minTime:         feb5,
				maxTime:         feb12,
				accountingEntry: paymentPb.AccountingEntryType_DEBIT,
			},
			setupMocks: func(f *dsPredictedTxnHandlerFields) {
				f.upcomingTxnsDao.EXPECT().GetByActor(gomock.Any(), "actor-1", gomock.Any()).Return(
					[]*modelPb.UpcomingTransaction{
						txn1,
						txn2,
					}, nil)
			},
			want: []*upcomingTransactionsPb.UpcomingTransaction{
				{
					DerivedEntityId: "swiggy-entity",
					EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        250,
						Nanos:        *********,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        300,
						Nanos:        *********,
					},
					MinTime:     feb5,
					MaxTime:     feb10,
					CreditDebit: paymentPb.AccountingEntryType_DEBIT,
					TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
				},
				{
					DerivedEntityId: "zomato-entity",
					EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        400,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
						Nanos:        0,
					},
					MinTime:     feb10,
					MaxTime:     feb12,
					CreditDebit: paymentPb.AccountingEntryType_DEBIT,
					TxnSource:   modelPb.TxnSource_TXN_SOURCE_DS,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initDsPredictedTxnHandlerFields(ctrl)
			tt.setupMocks(f)
			h := NewDsPredictedTransactionHandlerImpl(f.upcomingTxnsDao)
			got, err := h.GetDsSuggestedUpcomingTxns(tt.args.ctx, tt.args.actorId, tt.args.minTime, tt.args.maxTime, tt.args.accountingEntry)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConvertedToUpcomingTxns() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("ConvertedToUpcomingTxns value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}

type dsPredictedTxnHandlerFields struct {
	upcomingTxnsDao *mock_dao.MockUpcomingTransactionDao
}

func initDsPredictedTxnHandlerFields(ctrl *gomock.Controller) *dsPredictedTxnHandlerFields {
	return &dsPredictedTxnHandlerFields{
		upcomingTxnsDao: mock_dao.NewMockUpcomingTransactionDao(ctrl),
	}
}
