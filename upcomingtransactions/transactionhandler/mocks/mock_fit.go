// Code generated by MockGen. DO NOT EDIT.
// Source: fit.go

// Package mock_transactionhandler is a generated GoMock package.
package mock_transactionhandler

import (
	context "context"
	reflect "reflect"

	upcomingtransactions "github.com/epifi/gamma/api/upcomingtransactions"
	gomock "github.com/golang/mock/gomock"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// MockIFitTransactionHandler is a mock of IFitTransactionHandler interface.
type MockIFitTransactionHandler struct {
	ctrl     *gomock.Controller
	recorder *MockIFitTransactionHandlerMockRecorder
}

// MockIFitTransactionHandlerMockRecorder is the mock recorder for MockIFitTransactionHandler.
type MockIFitTransactionHandlerMockRecorder struct {
	mock *MockIFitTransactionHandler
}

// NewMockIFitTransactionHandler creates a new mock instance.
func NewMockIFitTransactionHandler(ctrl *gomock.Controller) *MockIFitTransactionHandler {
	mock := &MockIFitTransactionHandler{ctrl: ctrl}
	mock.recorder = &MockIFitTransactionHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIFitTransactionHandler) EXPECT() *MockIFitTransactionHandlerMockRecorder {
	return m.recorder
}

// GetFitUpcomingTxnsForActor mocks base method.
func (m *MockIFitTransactionHandler) GetFitUpcomingTxnsForActor(ctx context.Context, actorId string, fromTime, toTime *timestamppb.Timestamp) ([]*upcomingtransactions.UpcomingTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFitUpcomingTxnsForActor", ctx, actorId, fromTime, toTime)
	ret0, _ := ret[0].([]*upcomingtransactions.UpcomingTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFitUpcomingTxnsForActor indicates an expected call of GetFitUpcomingTxnsForActor.
func (mr *MockIFitTransactionHandlerMockRecorder) GetFitUpcomingTxnsForActor(ctx, actorId, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFitUpcomingTxnsForActor", reflect.TypeOf((*MockIFitTransactionHandler)(nil).GetFitUpcomingTxnsForActor), ctx, actorId, fromTime, toTime)
}
