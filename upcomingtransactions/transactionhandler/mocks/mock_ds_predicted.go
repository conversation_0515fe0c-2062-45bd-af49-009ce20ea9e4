// Code generated by MockGen. DO NOT EDIT.
// Source: ds_predicted.go

// Package mock_transactionhandler is a generated GoMock package.
package mock_transactionhandler

import (
	context "context"
	reflect "reflect"

	payment "github.com/epifi/gamma/api/order/payment"
	upcomingtransactions "github.com/epifi/gamma/api/upcomingtransactions"
	gomock "github.com/golang/mock/gomock"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// MockIDsPredictedTransactionHandler is a mock of IDsPredictedTransactionHandler interface.
type MockIDsPredictedTransactionHandler struct {
	ctrl     *gomock.Controller
	recorder *MockIDsPredictedTransactionHandlerMockRecorder
}

// MockIDsPredictedTransactionHandlerMockRecorder is the mock recorder for MockIDsPredictedTransactionHandler.
type MockIDsPredictedTransactionHandlerMockRecorder struct {
	mock *MockIDsPredictedTransactionHandler
}

// NewMockIDsPredictedTransactionHandler creates a new mock instance.
func NewMockIDsPredictedTransactionHandler(ctrl *gomock.Controller) *MockIDsPredictedTransactionHandler {
	mock := &MockIDsPredictedTransactionHandler{ctrl: ctrl}
	mock.recorder = &MockIDsPredictedTransactionHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDsPredictedTransactionHandler) EXPECT() *MockIDsPredictedTransactionHandlerMockRecorder {
	return m.recorder
}

// GetDsSuggestedUpcomingTxns mocks base method.
func (m *MockIDsPredictedTransactionHandler) GetDsSuggestedUpcomingTxns(ctx context.Context, actorId string, minTime, maxTime *timestamppb.Timestamp, accountingEntry payment.AccountingEntryType) ([]*upcomingtransactions.UpcomingTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDsSuggestedUpcomingTxns", ctx, actorId, minTime, maxTime, accountingEntry)
	ret0, _ := ret[0].([]*upcomingtransactions.UpcomingTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDsSuggestedUpcomingTxns indicates an expected call of GetDsSuggestedUpcomingTxns.
func (mr *MockIDsPredictedTransactionHandlerMockRecorder) GetDsSuggestedUpcomingTxns(ctx, actorId, minTime, maxTime, accountingEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDsSuggestedUpcomingTxns", reflect.TypeOf((*MockIDsPredictedTransactionHandler)(nil).GetDsSuggestedUpcomingTxns), ctx, actorId, minTime, maxTime, accountingEntry)
}
