package transactionhandler

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	actorPb "github.com/epifi/gamma/api/actor"
	mockActor "github.com/epifi/gamma/api/actor/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/recurringpayment"
	mockRecurPaymentClient "github.com/epifi/gamma/api/recurringpayment/mocks"
	"github.com/epifi/gamma/api/rms/manager"
	rmsManagerPb "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/rms/manager/mocks"
	typesPb "github.com/epifi/gamma/api/typesv2"
	upcomingTransactionsPb "github.com/epifi/gamma/api/upcomingtransactions"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/mock"
)

type fields struct {
	rmsClient              *mocks.MockRuleManagerClient
	actorClient            *mockActor.MockActorClient
	recurringPaymentClient *mockRecurPaymentClient.MockRecurringPaymentServiceClient
}

func initFields(ctrl *gomock.Controller) *fields {
	return &fields{
		rmsClient:              mocks.NewMockRuleManagerClient(ctrl),
		actorClient:            mockActor.NewMockActorClient(ctrl),
		recurringPaymentClient: mockRecurPaymentClient.NewMockRecurringPaymentServiceClient(ctrl),
	}
}

var (
	feb15      = timestamppb.New(time.Date(2023, 02, 15, 0, 0, 0, 0, datetime.IST))
	feb5       = timestamppb.New(time.Date(2023, 02, 5, 0, 0, 0, 0, datetime.IST))
	feb10      = timestamppb.New(time.Date(2023, 02, 10, 0, 0, 0, 0, datetime.IST))
	feb12      = timestamppb.New(time.Date(2023, 02, 12, 0, 0, 0, 0, datetime.IST))
	fitttExec1 = &rmsManagerPb.UpcomingExecution{
		SubId:  "sub-1",
		RuleId: "rule-1",
		DebitAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		ExecutionDate: &date.Date{
			Year:  2023,
			Month: 2,
			Day:   5,
		},
		DestinationEntityName: "ABC",
	}

	fitttExec2 = &rmsManagerPb.UpcomingExecution{
		SubId:  "sub-2",
		RuleId: "rule-2",
		DebitAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        200,
		},
		ExecutionDate: &date.Date{
			Year:  2023,
			Month: 2,
			Day:   10,
		},
		DestinationEntityName: "ABC",
	}

	fitttExec3 = &rmsManagerPb.UpcomingExecution{
		SubId:  "sub-3",
		RuleId: "rule-3",
		DebitAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        300,
		},
		ExecutionDate: &date.Date{
			Year:  2023,
			Month: 2,
			Day:   12,
		},
		DestinationEntityName: "ABC",
	}

	fitttExec4 = &rmsManagerPb.UpcomingExecution{
		SubId:  "sub-4",
		RuleId: "rule-5",
		DebitAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        400,
		},
		ExecutionDate: &date.Date{
			Year:  2023,
			Month: 2,
			Day:   15,
		},
		DestinationEntityName: "ABC",
	}

	fitUpTxn1 = &upcomingTransactionsPb.UpcomingTransaction{
		EntityType:      modelPb.EntityType_ENTITY_TYPE_SD_DEPOSIT_ACCOUNT,
		DerivedEntityId: "acc-1",
		MinTime:         feb5,
		MaxTime:         timestamppb.New(feb5.AsTime().In(datetime.IST).Add(24 * time.Hour).Add(-1 * time.Second)),
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
		TxnSource:   modelPb.TxnSource_TXN_SOURCE_FITTT,
		Metadata: &upcomingTransactionsPb.TransactionMetadata{
			Type: upcomingTransactionsPb.TransactionMetadataType_TRANSACTION_METADATA_TYPE_FIT,
			Metadata: &upcomingTransactionsPb.TransactionMetadata_FitTxnMetadata{
				FitTxnMetadata: &upcomingTransactionsPb.FitTxnMetadata{
					SubscriptionId: "sub-1",
					RuleSubscription: &manager.RuleSubscription{
						Id:      "sub-1",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_SdValue{
										SdValue: &manager.SdParamValue{
											AccountId: "acc-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_DAILY_RULE,
					},
				},
			},
		},
	}
	fitUpTxn2 = &upcomingTransactionsPb.UpcomingTransaction{
		EntityType:      modelPb.EntityType_ENTITY_TYPE_MUTUAL_FUND,
		DerivedEntityId: "mf-1",
		MinTime:         feb10,
		MaxTime:         timestamppb.New(feb10.AsTime().In(datetime.IST).Add(24 * time.Hour).Add(-1 * time.Second)),
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        200,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        200,
		},
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
		TxnSource:   modelPb.TxnSource_TXN_SOURCE_FITTT,
		Metadata: &upcomingTransactionsPb.TransactionMetadata{
			Type: upcomingTransactionsPb.TransactionMetadataType_TRANSACTION_METADATA_TYPE_FIT,
			Metadata: &upcomingTransactionsPb.TransactionMetadata_FitTxnMetadata{
				FitTxnMetadata: &upcomingTransactionsPb.FitTxnMetadata{
					SubscriptionId: "sub-2",
					RuleSubscription: &manager.RuleSubscription{
						Id:      "sub-2",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_MutualFundVal{
										MutualFundVal: &manager.MutualFundValue{
											MfId: "mf-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY,
					},
				},
			},
		},
	}
	fitUpTxn3 = &upcomingTransactionsPb.UpcomingTransaction{
		EntityType:      modelPb.EntityType_ENTITY_TYPE_ACTOR,
		DerivedEntityId: "actor-2",
		MinTime:         feb12,
		MaxTime:         timestamppb.New(feb12.AsTime().In(datetime.IST).Add(24 * time.Hour).Add(-1 * time.Second)),
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        300,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        300,
		},
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
		TxnSource:   modelPb.TxnSource_TXN_SOURCE_FITTT,
		Metadata: &upcomingTransactionsPb.TransactionMetadata{
			Type: upcomingTransactionsPb.TransactionMetadataType_TRANSACTION_METADATA_TYPE_FIT,
			Metadata: &upcomingTransactionsPb.TransactionMetadata_FitTxnMetadata{
				FitTxnMetadata: &upcomingTransactionsPb.FitTxnMetadata{
					SubscriptionId: "sub-3",
					RuleSubscription: &manager.RuleSubscription{
						Id:      "sub-3",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				},
			},
		},
	}
	fitUpTxn4 = &upcomingTransactionsPb.UpcomingTransaction{
		EntityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
		DerivedEntityId: "entity-1",
		MinTime:         feb15,
		MaxTime:         timestamppb.New(feb15.AsTime().In(datetime.IST).Add(24 * time.Hour).Add(-1 * time.Second)),
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        400,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        400,
		},
		CreditDebit: paymentPb.AccountingEntryType_DEBIT,
		TxnSource:   modelPb.TxnSource_TXN_SOURCE_FITTT,
		Metadata: &upcomingTransactionsPb.TransactionMetadata{
			Type: upcomingTransactionsPb.TransactionMetadataType_TRANSACTION_METADATA_TYPE_FIT,
			Metadata: &upcomingTransactionsPb.TransactionMetadata_FitTxnMetadata{
				FitTxnMetadata: &upcomingTransactionsPb.FitTxnMetadata{
					SubscriptionId: "sub-4",
					RuleSubscription: &manager.RuleSubscription{
						Id:      "sub-4",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-2",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				},
			},
		},
	}
)

func TestFitTransactionHandlerImpl_GetUpcomingTxnsFromExecutions(t *testing.T) {
	type args struct {
		ctx      context.Context
		actorId  string
		fromTime *timestamppb.Timestamp
		toTime   *timestamppb.Timestamp
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       []*upcomingTransactionsPb.UpcomingTransaction
		wantErr    bool
	}{
		{
			name: "failed in getting upcoming executions",
			args: args{
				ctx:      context.Background(),
				actorId:  "actor-1",
				fromTime: feb5,
				toTime:   feb15,
			},
			setupMocks: func(f *fields) {
				f.rmsClient.EXPECT().GetUpcomingExecutions(gomock.Any(), &manager.GetUpcomingExecutionsRequest{
					ActorId: "actor-1",
					StartDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   5,
					},
					EndDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   15,
					},
					RuleTypes: getApplicableFitttRules(),
				}).Return(&manager.GetUpcomingExecutionsResponse{
					Status: rpc.StatusInternal(),
				}, nil)

			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failed to get subscription details by id",
			args: args{
				ctx:      context.Background(),
				actorId:  "actor-1",
				fromTime: feb5,
				toTime:   feb15,
			},
			setupMocks: func(f *fields) {
				f.rmsClient.EXPECT().GetUpcomingExecutions(gomock.Any(), &manager.GetUpcomingExecutionsRequest{
					ActorId: "actor-1",
					StartDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   5,
					},
					EndDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   15,
					},
					RuleTypes: getApplicableFitttRules(),
				}).Return(&manager.GetUpcomingExecutionsResponse{
					Status: rpc.StatusOk(),
					UpcomingExecutions: []*manager.UpcomingExecution{
						fitttExec4,
						fitttExec3,
						fitttExec2,
						fitttExec1,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-1",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-2",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-2",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_MutualFundVal{
										MutualFundVal: &manager.MutualFundValue{
											MfId: "mf-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY,
					},
				}, nil).MaxTimes(1)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-3",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-3",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				}, nil).MaxTimes(1)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-4",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-4",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-2",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				}, nil).MaxTimes(1)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failed to entity details (get actor by id)",
			args: args{
				ctx:      context.Background(),
				actorId:  "actor-1",
				fromTime: feb5,
				toTime:   feb15,
			},
			setupMocks: func(f *fields) {
				f.rmsClient.EXPECT().GetUpcomingExecutions(gomock.Any(), &manager.GetUpcomingExecutionsRequest{
					ActorId: "actor-1",
					StartDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   5,
					},
					EndDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   15,
					},
					RuleTypes: getApplicableFitttRules(),
				}).Return(&manager.GetUpcomingExecutionsResponse{
					Status: rpc.StatusOk(),
					UpcomingExecutions: []*manager.UpcomingExecution{
						fitttExec4,
						fitttExec3,
						fitttExec2,
						fitttExec1,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-1",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-1",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_SdValue{
										SdValue: &manager.SdParamValue{
											AccountId: "acc-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_DAILY_RULE,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-2",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-2",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_MutualFundVal{
										MutualFundVal: &manager.MutualFundValue{
											MfId: "mf-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-3",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-3",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-4",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-4",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-2",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				}, nil)

				f.recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), mock.NewProtoMatcher(&recurringpayment.GetRecurringPaymentByIdRequest{
					Id: "rec-pay-id-1",
				})).Return(&recurringpayment.GetRecurringPaymentByIdResponse{
					Status: rpc.StatusOk(),
					RecurringPayment: &recurringpayment.RecurringPayment{
						FromActorId: "actor-1",
						ToActorId:   "actor-2",
					},
				}, nil).MaxTimes(1)
				f.recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), mock.NewProtoMatcher(&recurringpayment.GetRecurringPaymentByIdRequest{
					Id: "rec-pay-id-2",
				})).Return(&recurringpayment.GetRecurringPaymentByIdResponse{
					Status: rpc.StatusOk(),
					RecurringPayment: &recurringpayment.RecurringPayment{
						FromActorId: "actor-1",
						ToActorId:   "actor-3",
					},
				}, nil).MaxTimes(1)

				f.actorClient.EXPECT().GetEntityDetails(gomock.Any(), mock.NewProtoMatcher(&actorPb.GetEntityDetailsRequest{
					ActorIds: []string{"actor-2"},
				})).Return(&actorPb.GetEntityDetailsResponse{
					Status: rpc.StatusInternal(),
				}, nil)

				f.actorClient.EXPECT().GetEntityDetails(gomock.Any(), mock.NewProtoMatcher(&actorPb.GetEntityDetailsRequest{
					ActorIds: []string{"actor-3"},
				})).Return(&actorPb.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
						{
							EntityId:   "entity-1",
							EntityType: typesPb.ActorType_EXTERNAL_MERCHANT,
						},
					},
				}, nil).MaxTimes(1)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "upcoming transaction successfully generated from executions",
			args: args{
				ctx:      context.Background(),
				actorId:  "actor-1",
				fromTime: feb5,
				toTime:   feb15,
			},
			setupMocks: func(f *fields) {
				f.rmsClient.EXPECT().GetUpcomingExecutions(gomock.Any(), &manager.GetUpcomingExecutionsRequest{
					ActorId: "actor-1",
					StartDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   5,
					},
					EndDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   15,
					},
					RuleTypes: getApplicableFitttRules(),
				}).Return(&manager.GetUpcomingExecutionsResponse{
					Status: rpc.StatusOk(),
					UpcomingExecutions: []*manager.UpcomingExecution{
						fitttExec4,
						fitttExec3,
						fitttExec2,
						fitttExec1,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-1",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-1",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_SdValue{
										SdValue: &manager.SdParamValue{
											AccountId: "acc-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_DAILY_RULE,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-2",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-2",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_MutualFundVal{
										MutualFundVal: &manager.MutualFundValue{
											MfId: "mf-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-3",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-3",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-4",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-4",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-2",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				}, nil)

				f.recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), mock.NewProtoMatcher(&recurringpayment.GetRecurringPaymentByIdRequest{
					Id: "rec-pay-id-1",
				})).Return(&recurringpayment.GetRecurringPaymentByIdResponse{
					Status: rpc.StatusOk(),
					RecurringPayment: &recurringpayment.RecurringPayment{
						FromActorId: "actor-1",
						ToActorId:   "actor-2",
					},
				}, nil)
				f.recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), mock.NewProtoMatcher(&recurringpayment.GetRecurringPaymentByIdRequest{
					Id: "rec-pay-id-2",
				})).Return(&recurringpayment.GetRecurringPaymentByIdResponse{
					Status: rpc.StatusOk(),
					RecurringPayment: &recurringpayment.RecurringPayment{
						FromActorId: "actor-1",
						ToActorId:   "actor-3",
					},
				}, nil)

				f.actorClient.EXPECT().GetEntityDetails(gomock.Any(), mock.NewProtoMatcher(&actorPb.GetEntityDetailsRequest{
					ActorIds: []string{"actor-2"},
				})).Return(&actorPb.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
						{
							EntityType: typesPb.ActorType_USER,
						},
					},
				}, nil)

				f.actorClient.EXPECT().GetEntityDetails(gomock.Any(), mock.NewProtoMatcher(&actorPb.GetEntityDetailsRequest{
					ActorIds: []string{"actor-3"},
				})).Return(&actorPb.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
						{
							EntityId:   "entity-1",
							EntityType: typesPb.ActorType_EXTERNAL_MERCHANT,
						},
					},
				}, nil)
			},
			want: []*upcomingTransactionsPb.UpcomingTransaction{
				fitUpTxn1,
				fitUpTxn2,
				fitUpTxn3,
				fitUpTxn4,
			},
			wantErr: false,
		},
		{
			name: "failed to get recurring pay info",
			args: args{
				ctx:      context.Background(),
				actorId:  "actor-1",
				fromTime: feb5,
				toTime:   feb15,
			},
			setupMocks: func(f *fields) {
				f.rmsClient.EXPECT().GetUpcomingExecutions(gomock.Any(), &manager.GetUpcomingExecutionsRequest{
					ActorId: "actor-1",
					StartDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   5,
					},
					EndDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   15,
					},
					RuleTypes: getApplicableFitttRules(),
				}).Return(&manager.GetUpcomingExecutionsResponse{
					Status: rpc.StatusOk(),
					UpcomingExecutions: []*manager.UpcomingExecution{
						fitttExec4,
						fitttExec3,
						fitttExec2,
						fitttExec1,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-1",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-1",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_SdValue{
										SdValue: &manager.SdParamValue{
											AccountId: "acc-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_DAILY_RULE,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-2",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-2",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_MutualFundVal{
										MutualFundVal: &manager.MutualFundValue{
											MfId: "mf-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-3",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-3",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-4",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-4",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-2",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				}, nil)

				f.recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), mock.NewProtoMatcher(&recurringpayment.GetRecurringPaymentByIdRequest{
					Id: "rec-pay-id-1",
				})).Return(&recurringpayment.GetRecurringPaymentByIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				f.recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), mock.NewProtoMatcher(&recurringpayment.GetRecurringPaymentByIdRequest{
					Id: "rec-pay-id-2",
				})).Return(&recurringpayment.GetRecurringPaymentByIdResponse{
					Status: rpc.StatusOk(),
					RecurringPayment: &recurringpayment.RecurringPayment{
						FromActorId: "actor-1",
						ToActorId:   "actor-3",
					},
				}, nil).MaxTimes(1)

				f.actorClient.EXPECT().GetEntityDetails(gomock.Any(), mock.NewProtoMatcher(&actorPb.GetEntityDetailsRequest{
					ActorIds: []string{"actor-3"},
				})).Return(&actorPb.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
						{
							EntityId:   "entity-1",
							EntityType: typesPb.ActorType_EXTERNAL_MERCHANT,
						},
					},
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "upcoming transaction successfully generated from executions",
			args: args{
				ctx:      context.Background(),
				actorId:  "actor-1",
				fromTime: feb5,
				toTime:   feb15,
			},
			setupMocks: func(f *fields) {
				f.rmsClient.EXPECT().GetUpcomingExecutions(gomock.Any(), &manager.GetUpcomingExecutionsRequest{
					ActorId: "actor-1",
					StartDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   5,
					},
					EndDate: &date.Date{
						Year:  2023,
						Month: 2,
						Day:   15,
					},
					RuleTypes: getApplicableFitttRules(),
				}).Return(&manager.GetUpcomingExecutionsResponse{
					Status: rpc.StatusOk(),
					UpcomingExecutions: []*manager.UpcomingExecution{
						fitttExec4,
						fitttExec3,
						fitttExec2,
						fitttExec1,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-1",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-1",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_SdValue{
										SdValue: &manager.SdParamValue{
											AccountId: "acc-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_DAILY_RULE,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-2",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-2",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_MutualFundVal{
										MutualFundVal: &manager.MutualFundValue{
											MfId: "mf-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-3",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-3",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-1",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				}, nil)
				f.rmsClient.EXPECT().GetSubscriptionById(gomock.Any(), mock.NewProtoMatcher(&manager.GetSubscriptionByIdRequest{
					SubscriptionId: "sub-4",
				})).Return(&manager.GetSubscriptionByIdResponse{
					Status: rpc.StatusOk(),
					SubscriptionData: &manager.RuleSubscription{
						Id:      "sub-4",
						ActorId: "actor-1",
						RuleParamValues: &manager.RuleParamValues{
							RuleParamValues: map[string]*manager.Value{
								"abc": {
									Value: &manager.Value_RecurringPaymentInfo{
										RecurringPaymentInfo: &manager.RecurringPaymentInfo{
											RecurringPaymentId: "rec-pay-id-2",
										},
									},
								},
							},
						},
					},
					Rule: &manager.Rule{
						RuleTypeForSpecialHandling: manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
					},
				}, nil)

				f.recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), mock.NewProtoMatcher(&recurringpayment.GetRecurringPaymentByIdRequest{
					Id: "rec-pay-id-1",
				})).Return(&recurringpayment.GetRecurringPaymentByIdResponse{
					Status: rpc.StatusOk(),
					RecurringPayment: &recurringpayment.RecurringPayment{
						FromActorId: "actor-1",
						ToActorId:   "actor-2",
					},
				}, nil)
				f.recurringPaymentClient.EXPECT().GetRecurringPaymentById(gomock.Any(), mock.NewProtoMatcher(&recurringpayment.GetRecurringPaymentByIdRequest{
					Id: "rec-pay-id-2",
				})).Return(&recurringpayment.GetRecurringPaymentByIdResponse{
					Status: rpc.StatusOk(),
					RecurringPayment: &recurringpayment.RecurringPayment{
						FromActorId: "actor-1",
						ToActorId:   "actor-3",
					},
				}, nil)

				f.actorClient.EXPECT().GetEntityDetails(gomock.Any(), mock.NewProtoMatcher(&actorPb.GetEntityDetailsRequest{
					ActorIds: []string{"actor-2"},
				})).Return(&actorPb.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
						{
							EntityType: typesPb.ActorType_USER,
						},
					},
				}, nil)

				f.actorClient.EXPECT().GetEntityDetails(gomock.Any(), mock.NewProtoMatcher(&actorPb.GetEntityDetailsRequest{
					ActorIds: []string{"actor-3"},
				})).Return(&actorPb.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
						{
							EntityId:   "entity-1",
							EntityType: typesPb.ActorType_EXTERNAL_MERCHANT,
						},
					},
				}, nil)
			},
			want: []*upcomingTransactionsPb.UpcomingTransaction{
				fitUpTxn1,
				fitUpTxn2,
				fitUpTxn3,
				fitUpTxn4,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			tt.setupMocks(f)
			h := NewFitTransactionHandlerImpl(f.rmsClient, f.actorClient, f.recurringPaymentClient)
			got, err := h.GetFitUpcomingTxnsForActor(tt.args.ctx, tt.args.actorId, tt.args.fromTime, tt.args.toTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUpcomingTxnsFromExecutions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetUpcomingTxnsFromExecutions value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}

func getApplicableFitttRules() []manager.RuleTypeForSpecialHandling {
	return []manager.RuleTypeForSpecialHandling{
		manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY,
		manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_WEEKLY,
		manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_MONTHLY,
		manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_DAILY_RULE,
		manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_WEEKLY_RULE,
		manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_MONTHLY_RULE,
		manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_ONE_TIME,
		manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
	}
}
