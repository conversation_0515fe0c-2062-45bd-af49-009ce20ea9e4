//go:generate mockgen -source=ds_predicted.go -destination=./mocks/mock_ds_predicted.go package=mocks
package transactionhandler

import (
	"context"
	"fmt"
	"time"

	"github.com/google/wire"
	"google.golang.org/protobuf/types/known/timestamppb"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	upcomingTransactionsPb "github.com/epifi/gamma/api/upcomingtransactions"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/upcomingtransactions/dao"
)

var DsPredictedTransactionHandlerWireSet = wire.NewSet(NewDsPredictedTransactionHandlerImpl, wire.Bind(new(IDsPredictedTransactionHandler), new(*DsPredictedTransactionHandlerImpl)))

type IDsPredictedTransactionHandler interface {
	GetDsSuggestedUpcomingTxns(ctx context.Context, actorId string, minTime *timestamppb.Timestamp, maxTime *timestamppb.Timestamp, accountingEntry paymentPb.AccountingEntryType) ([]*upcomingTransactionsPb.UpcomingTransaction, error)
}

type DsPredictedTransactionHandlerImpl struct {
	upcomingTxnsDao dao.UpcomingTransactionDao
}

func NewDsPredictedTransactionHandlerImpl(upcomingTxnsDao dao.UpcomingTransactionDao) *DsPredictedTransactionHandlerImpl {
	return &DsPredictedTransactionHandlerImpl{
		upcomingTxnsDao: upcomingTxnsDao,
	}
}

// nolint:gocritic
func (h *DsPredictedTransactionHandlerImpl) GetDsSuggestedUpcomingTxns(ctx context.Context, actorId string, minTime *timestamppb.Timestamp, maxTime *timestamppb.Timestamp, accountingEntry paymentPb.AccountingEntryType) ([]*upcomingTransactionsPb.UpcomingTransaction, error) {
	var filterOptions []storagev2.FilterOption
	if minTime == nil {
		minTime = timestamppb.New(time.Now())
	}

	// where expected min max time has a non-zero overlap with [min, max] of upcoming txn
	// e.g. in integer interval terms if [l, r] in input is [5, 10] then the match results can include
	// [1, 5], [1, 10], [6, 15], [6, 8], etc
	filterOptions = append(filterOptions, dao.WhereTxnInDateRange(minTime.AsTime(), maxTime.AsTime()))
	// txn is still in not executed state
	// This can change in future based on if the requirement e.g. all txns or executed + not_executed, etc
	filterOptions = append(filterOptions, dao.WhereExecutionStatusIs(modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET))
	// txn should be in specified accounting_entry (credit/debit), if specified
	if accountingEntry != paymentPb.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED {
		filterOptions = append(filterOptions, dao.WhereCreditDebitIs(accountingEntry))
	}
	// txn source is DS
	filterOptions = append(filterOptions, dao.WhereTxnSourceIs(modelPb.TxnSource_TXN_SOURCE_DS))

	txns, err := h.upcomingTxnsDao.GetByActor(ctx, actorId, filterOptions...)
	if err != nil {
		return nil, fmt.Errorf("failed to get upcoming txns for actor : %w", err)
	}
	if len(txns) == 0 {
		return nil, nil
	}
	return h.convertToUpcomingTxns(ctx, txns)
}

func (h *DsPredictedTransactionHandlerImpl) convertToUpcomingTxns(_ context.Context, txns []*modelPb.UpcomingTransaction) ([]*upcomingTransactionsPb.UpcomingTransaction, error) {
	var resUpcomingTxns []*upcomingTransactionsPb.UpcomingTransaction
	for _, txn := range txns {
		resUpcomingTxns = append(resUpcomingTxns, &upcomingTransactionsPb.UpcomingTransaction{
			EntityType:      txn.GetWithEntityType(),
			DerivedEntityId: txn.GetWithEntityId(),
			MinTime:         txn.GetMinDate(),
			MaxTime:         txn.GetMaxDate(),
			MinAmount:       txn.GetMinAmount(),
			MaxAmount:       txn.GetMaxAmount(),
			CreditDebit:     txn.GetCreditDebit(),
			TxnSource:       modelPb.TxnSource_TXN_SOURCE_DS,
		})
	}
	return resUpcomingTxns, nil
}
