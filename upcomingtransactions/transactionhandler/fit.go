//go:generate mockgen -source=fit.go -destination=./mocks/mock_fit.go package=mocks
package transactionhandler

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/google/wire"
	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"

	actorPb "github.com/epifi/gamma/api/actor"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/rms/manager"
	rmsManagerPb "github.com/epifi/gamma/api/rms/manager"
	typesPb "github.com/epifi/gamma/api/typesv2"
	upcomingTransactionsPb "github.com/epifi/gamma/api/upcomingtransactions"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
)

var FitTransactionHandlerWireSet = wire.NewSet(NewFitTransactionHandlerImpl, wire.Bind(new(IFitTransactionHandler), new(*FitTransactionHandlerImpl)))

type IFitTransactionHandler interface {
	GetFitUpcomingTxnsForActor(ctx context.Context, actorId string, fromTime *timestamppb.Timestamp, toTime *timestamppb.Timestamp) ([]*upcomingTransactionsPb.UpcomingTransaction, error)
}

type FitTransactionHandlerImpl struct {
	rmsClient              manager.RuleManagerClient
	actorClient            actorPb.ActorClient
	recurringPaymentClient rpPb.RecurringPaymentServiceClient
}

func NewFitTransactionHandlerImpl(rmsClient manager.RuleManagerClient, actorClient actorPb.ActorClient, recurringPaymentClient rpPb.RecurringPaymentServiceClient) *FitTransactionHandlerImpl {
	return &FitTransactionHandlerImpl{
		rmsClient:              rmsClient,
		actorClient:            actorClient,
		recurringPaymentClient: recurringPaymentClient,
	}
}

func (h *FitTransactionHandlerImpl) GetFitUpcomingTxnsForActor(ctx context.Context, actorId string, fromTime *timestamppb.Timestamp,
	toTime *timestamppb.Timestamp) ([]*upcomingTransactionsPb.UpcomingTransaction, error) {
	toTime = getToTimeForFitttTxns(fromTime, toTime)
	resp, err := h.rmsClient.GetUpcomingExecutions(ctx, &manager.GetUpcomingExecutionsRequest{
		ActorId:   actorId,
		StartDate: datetime.TimestampToDateInLoc(fromTime, datetime.IST),
		EndDate:   datetime.TimestampToDateInLoc(toTime, datetime.IST),
		RuleTypes: []manager.RuleTypeForSpecialHandling{
			manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY,
			manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_WEEKLY,
			manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_MONTHLY,
			manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_DAILY_RULE,
			manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_WEEKLY_RULE,
			manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_MONTHLY_RULE,
			manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_ONE_TIME,
			manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING,
			manager.RuleTypeForSpecialHandling_RULE_TYPE_US_STOCKS_MONTHLY_SIP,
		},
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return nil, rpcErr
	}
	txns, err := h.getUpcomingTxnsFromExecutions(ctx, resp.GetUpcomingExecutions())
	if err != nil {
		return nil, fmt.Errorf("failed to get fit upcoming transactions from executions : %w", err)
	}
	// sort all txns primarily on min time (increasing order) and secondarily on max time
	sort.Slice(txns, func(i, j int) bool {
		if txns[i].GetMinTime().AsTime().Equal(txns[j].GetMinTime().AsTime()) {
			return txns[i].GetMaxTime().AsTime().Before(txns[j].GetMaxTime().AsTime())
		}
		return txns[i].GetMinTime().AsTime().Before(txns[j].GetMinTime().AsTime())
	})
	return txns, nil
}

func (h *FitTransactionHandlerImpl) getUpcomingTxnsFromExecutions(ctx context.Context, executions []*rmsManagerPb.UpcomingExecution) ([]*upcomingTransactionsPb.UpcomingTransaction, error) {
	var txns []*upcomingTransactionsPb.UpcomingTransaction

	uniqueSubIds := getUniqueSubIdsFromExecutions(executions)
	subIdToRuleAndSubMap, err := h.getFitttSubscriptionsRulesByIds(ctx, uniqueSubIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get fittt subscription rule types by ids : %w", err)
	}

	for _, exec := range executions {
		if _, found := subIdToRuleAndSubMap[exec.GetSubId()]; !found {
			return nil, fmt.Errorf("%s sub_id not found in subIdToRuleAndSubMap map", exec.GetSubId())
		}
		t := getTimeFromDate(exec.GetExecutionDate())
		fromTime := timestamppb.New(t)
		toTime := timestamppb.New(datetime.EndOfDay(t))
		entityTypeAndIdData, err2 := h.getEntityTypeAndIdForFitttSubscription(ctx, subIdToRuleAndSubMap[exec.GetSubId()].rule.GetRuleTypeForSpecialHandling(),
			subIdToRuleAndSubMap[exec.GetSubId()].subscriptionData)
		if err2 != nil {
			return nil, fmt.Errorf("failed to get entity type and id for fittt subscriptions : %w subId : %s", err2, exec.GetSubId())
		}
		txns = append(txns, &upcomingTransactionsPb.UpcomingTransaction{
			EntityType:      entityTypeAndIdData.entityType,
			DerivedEntityId: entityTypeAndIdData.derivedEntityId,
			MinTime:         fromTime,
			MaxTime:         toTime,
			MinAmount:       exec.GetDebitAmount(),
			MaxAmount:       exec.GetDebitAmount(),
			CreditDebit:     paymentPb.AccountingEntryType_DEBIT,
			TxnSource:       modelPb.TxnSource_TXN_SOURCE_FITTT,
			Metadata: &upcomingTransactionsPb.TransactionMetadata{
				Type: upcomingTransactionsPb.TransactionMetadataType_TRANSACTION_METADATA_TYPE_FIT,
				Metadata: &upcomingTransactionsPb.TransactionMetadata_FitTxnMetadata{
					FitTxnMetadata: &upcomingTransactionsPb.FitTxnMetadata{
						SubscriptionId:   exec.GetSubId(),
						RuleSubscription: subIdToRuleAndSubMap[exec.GetSubId()].subscriptionData,
						Rule:             subIdToRuleAndSubMap[exec.GetSubId()].rule,
					},
				},
			},
		})
	}
	return txns, nil
}

func getUniqueSubIdsFromExecutions(executions []*manager.UpcomingExecution) []string {
	var subIds []string
	uniqueSubIdsMap := make(map[string]bool)
	for _, exec := range executions {
		uniqueSubIdsMap[exec.GetSubId()] = true
	}
	for subId, _ := range uniqueSubIdsMap {
		subIds = append(subIds, subId)
	}
	return subIds
}

func getTimeFromDate(d *date.Date) time.Time {
	return time.Date(int(d.GetYear()), time.Month(d.GetMonth()), int(d.GetDay()), 0, 0, 0, 0, datetime.IST)
}

func (h *FitTransactionHandlerImpl) getFitttSubscriptionsRulesByIds(ctx context.Context, subIds []string) (map[string]*ruleAndSubscription, error) {
	errGrp, gCtx := errgroup.WithContext(ctx)
	subIdToRuleAndSubMap := make(map[string]*ruleAndSubscription)

	ch := make(chan *ruleAndSubscription, len(subIds))
	for _, subId := range lo.Uniq(subIds) {
		subId_ := subId
		errGrp.Go(func() error {
			res, err := h.rmsClient.GetSubscriptionById(gCtx, &manager.GetSubscriptionByIdRequest{
				SubscriptionId: subId_,
			})
			if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
				return fmt.Errorf("failed to get subscription resp for id %s : %w", subId, rpcErr)
			}
			ch <- &ruleAndSubscription{
				subscriptionId:   subId_,
				rule:             res.GetRule(),
				subscriptionData: res.GetSubscriptionData(),
			}
			return nil
		})
	}
	if err := errGrp.Wait(); err != nil {
		return nil, fmt.Errorf("failure in GetSubscriptionById : %w", err)
	}
	close(ch)

	for data := range ch {
		if data == nil {
			return nil, fmt.Errorf("ruleAndSubscription data should not be nil")
		}
		subIdToRuleAndSubMap[data.subscriptionId] = data
	}

	return subIdToRuleAndSubMap, nil
}

func (h *FitTransactionHandlerImpl) getEntityTypeAndIdForFitttSubscription(ctx context.Context, ruleType manager.RuleTypeForSpecialHandling,
	subData *manager.RuleSubscription) (*subIdToEntityTypeAndId, error) {
	switch ruleType {
	case manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY, manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_WEEKLY, manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_MONTHLY:
		return h.getInvestSubIdToEntityTypeAndIdMapping(subData)
	case manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_DAILY_RULE, manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_WEEKLY_RULE, manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_MONTHLY_RULE:
		return h.getSaveSubIdToEntityTypeAndIdMapping(subData)
	case manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_ONE_TIME, manager.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_PAY_RECURRING:
		return h.getSubIdToEntityTypeAndIdForAutoPay(ctx, subData)
	case manager.RuleTypeForSpecialHandling_RULE_TYPE_US_STOCKS_MONTHLY_SIP:
		return h.getSipSubIdToEntityTypeAndIdMapping(subData)
	default:
		return nil, fmt.Errorf("subscription rule type not handled : %s", ruleType.String())
	}
}

func (h *FitTransactionHandlerImpl) getInvestSubIdToEntityTypeAndIdMapping(subscription *manager.RuleSubscription) (*subIdToEntityTypeAndId, error) {
	var (
		mfId string
	)
	for _, paramValue := range subscription.GetRuleParamValues().GetRuleParamValues() {
		if paramValue.GetMutualFundVal() != nil {
			mfId = paramValue.GetMutualFundVal().GetMfId()
			break
		}
	}
	if mfId == "" {
		return nil, fmt.Errorf("empty mf id in param values for sub_id : %s", subscription.GetId())
	}
	return &subIdToEntityTypeAndId{
		subscriptionId:  subscription.GetId(),
		entityType:      modelPb.EntityType_ENTITY_TYPE_MUTUAL_FUND,
		derivedEntityId: mfId,
	}, nil
}

func (h *FitTransactionHandlerImpl) getSipSubIdToEntityTypeAndIdMapping(subscription *manager.RuleSubscription) (*subIdToEntityTypeAndId, error) {
	var (
		stockId string
	)
	for _, paramValue := range subscription.GetRuleParamValues().GetRuleParamValues() {
		if paramValue.GetUsStockValue() != nil {
			stockId = paramValue.GetUsStockValue().GetStockId()
			break
		}
	}
	if stockId == "" {
		return nil, fmt.Errorf("empty stock id in param values for sub_id : %s", subscription.GetId())
	}
	return &subIdToEntityTypeAndId{
		subscriptionId:  subscription.GetId(),
		entityType:      modelPb.EntityType_ENTITY_TYPE_USSTOCKS,
		derivedEntityId: stockId,
	}, nil
}

func (h *FitTransactionHandlerImpl) getSaveSubIdToEntityTypeAndIdMapping(subscription *manager.RuleSubscription) (*subIdToEntityTypeAndId, error) {
	var (
		accId string
	)
	for _, paramValue := range subscription.GetRuleParamValues().GetRuleParamValues() {
		if paramValue.GetSdValue() != nil {
			accId = paramValue.GetSdValue().GetAccountId()
		}
	}
	if accId == "" {
		return nil, fmt.Errorf("empty account id in param values for sub_id : %s", subscription.GetId())
	}
	return &subIdToEntityTypeAndId{
		subscriptionId:  subscription.GetId(),
		entityType:      modelPb.EntityType_ENTITY_TYPE_SD_DEPOSIT_ACCOUNT,
		derivedEntityId: accId,
	}, nil
}

func (h *FitTransactionHandlerImpl) getSubIdToEntityTypeAndIdForAutoPay(ctx context.Context, subscription *manager.RuleSubscription) (*subIdToEntityTypeAndId, error) {
	otherActorId, err := h.getRecipientActorIdForAutoPay(ctx, subscription)
	if err != nil {
		return nil, fmt.Errorf("failed to get recipient actor_id for auto pay subscription : %w", err)
	}
	if otherActorId == "" {
		return nil, fmt.Errorf("recipient actor_id is empty for sub_id : %s", subscription.GetId())
	}
	actorResp, err := h.actorClient.GetEntityDetails(ctx, &actorPb.GetEntityDetailsRequest{
		ActorIds: []string{otherActorId},
	})
	if rpcErr := epifigrpc.RPCError(actorResp, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get entity details by actor : %w", rpcErr)
	}
	if len(actorResp.GetEntityDetails()) != 1 {
		return nil, fmt.Errorf("got %d entity details expected 1", len(actorResp.GetEntityDetails()))
	}
	switch actorResp.GetEntityDetails()[0].GetEntityType() {
	case typesPb.ActorType_USER, typesPb.ActorType_EXTERNAL_USER:
		return &subIdToEntityTypeAndId{
			subscriptionId:  subscription.GetId(),
			entityType:      modelPb.EntityType_ENTITY_TYPE_ACTOR,
			derivedEntityId: otherActorId,
		}, nil
	case typesPb.ActorType_MERCHANT, typesPb.ActorType_EXTERNAL_MERCHANT:
		return &subIdToEntityTypeAndId{
			subscriptionId:  subscription.GetId(),
			entityType:      modelPb.EntityType_ENTITY_TYPE_MERCHANT,
			derivedEntityId: actorResp.GetEntityDetails()[0].GetEntityId(),
		}, nil
	default:
		return nil, fmt.Errorf("entity type for other actor is not handled : %s for subId : %s", actorResp.GetEntityDetails()[0].GetEntityType().String(), subscription.GetId())
	}
}

type subIdToEntityTypeAndId struct {
	subscriptionId  string
	entityType      modelPb.EntityType
	derivedEntityId string
}

func (h *FitTransactionHandlerImpl) getRecipientActorIdForAutoPay(ctx context.Context, subscription *manager.RuleSubscription) (string, error) {
	var rpVal *manager.RecurringPaymentInfo
	for _, paramValue := range subscription.GetRuleParamValues().GetRuleParamValues() {
		if paramValue.GetRecurringPaymentInfo() != nil {
			rpVal = paramValue.GetRecurringPaymentInfo()
			break
		}
	}
	if rpVal.GetRecurringPaymentId() == "" {
		return "", fmt.Errorf("empty recurring payment id in fittt rule params, failure")
	}
	rpRes, err := h.recurringPaymentClient.GetRecurringPaymentById(ctx, &rpPb.GetRecurringPaymentByIdRequest{
		Id: rpVal.GetRecurringPaymentId(),
	})
	if rpcErr := epifigrpc.RPCError(rpRes, err); rpcErr != nil {
		return "", fmt.Errorf("error getting recurring payment by id for subscription : %w", rpcErr)
	}
	var recipientActorId string
	if rpRes.GetRecurringPayment().GetFromActorId() != subscription.GetActorId() {
		recipientActorId = rpRes.GetRecurringPayment().GetFromActorId()
	} else {
		recipientActorId = rpRes.GetRecurringPayment().GetToActorId()
	}
	return recipientActorId, nil
}

type ruleAndSubscription struct {
	subscriptionId   string
	subscriptionData *manager.RuleSubscription
	rule             *manager.Rule
}

// fittt rpc only allows max 60 days diff in toTime and fromTime time
// getToTimeForFitttTxns limits the toTime in case it is more than 45 days ahead of fromTime
func getToTimeForFitttTxns(fromTime, toTime *timestamppb.Timestamp) *timestamppb.Timestamp {
	maxAllowedToTime := fromTime.AsTime().Add(45 * 24 * time.Hour)
	if toTime.AsTime().After(maxAllowedToTime) {
		return timestamppb.New(maxAllowedToTime)
	}
	return toTime
}
