// Code generated by MockGen. DO NOT EDIT.
// Source: subscription_manager.go

// Package mock_subscriptionmanager is a generated GoMock package.
package mock_subscriptionmanager

import (
	context "context"
	reflect "reflect"

	model "github.com/epifi/gamma/api/upcomingtransactions/model"
	types "github.com/epifi/gamma/upcomingtransactions/consumer/types"
	gomock "github.com/golang/mock/gomock"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// MockSubscriptionManager is a mock of SubscriptionManager interface.
type MockSubscriptionManager struct {
	ctrl     *gomock.Controller
	recorder *MockSubscriptionManagerMockRecorder
}

// MockSubscriptionManagerMockRecorder is the mock recorder for MockSubscriptionManager.
type MockSubscriptionManagerMockRecorder struct {
	mock *MockSubscriptionManager
}

// NewMockSubscriptionManager creates a new mock instance.
func NewMockSubscriptionManager(ctrl *gomock.Controller) *MockSubscriptionManager {
	mock := &MockSubscriptionManager{ctrl: ctrl}
	mock.recorder = &MockSubscriptionManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSubscriptionManager) EXPECT() *MockSubscriptionManagerMockRecorder {
	return m.recorder
}

// GetUpcomingTransactionsWithEntityAfter mocks base method.
func (m *MockSubscriptionManager) GetUpcomingTransactionsWithEntityAfter(ctx context.Context, actorId, withEntityId string, maxDateAfter *timestamppb.Timestamp) ([]*model.UpcomingTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpcomingTransactionsWithEntityAfter", ctx, actorId, withEntityId, maxDateAfter)
	ret0, _ := ret[0].([]*model.UpcomingTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpcomingTransactionsWithEntityAfter indicates an expected call of GetUpcomingTransactionsWithEntityAfter.
func (mr *MockSubscriptionManagerMockRecorder) GetUpcomingTransactionsWithEntityAfter(ctx, actorId, withEntityId, maxDateAfter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpcomingTransactionsWithEntityAfter", reflect.TypeOf((*MockSubscriptionManager)(nil).GetUpcomingTransactionsWithEntityAfter), ctx, actorId, withEntityId, maxDateAfter)
}

// UpdateTxnStatusByActorAndTxnId mocks base method.
func (m *MockSubscriptionManager) UpdateTxnStatusByActorAndTxnId(ctx context.Context, actorId, txnId string, txnStatus model.TxnStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTxnStatusByActorAndTxnId", ctx, actorId, txnId, txnStatus)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTxnStatusByActorAndTxnId indicates an expected call of UpdateTxnStatusByActorAndTxnId.
func (mr *MockSubscriptionManagerMockRecorder) UpdateTxnStatusByActorAndTxnId(ctx, actorId, txnId, txnStatus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTxnStatusByActorAndTxnId", reflect.TypeOf((*MockSubscriptionManager)(nil).UpdateTxnStatusByActorAndTxnId), ctx, actorId, txnId, txnStatus)
}

// UpsertSubscriptionData mocks base method.
func (m *MockSubscriptionManager) UpsertSubscriptionData(ctx context.Context, subData *types.SubscriptionData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertSubscriptionData", ctx, subData)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertSubscriptionData indicates an expected call of UpsertSubscriptionData.
func (mr *MockSubscriptionManagerMockRecorder) UpsertSubscriptionData(ctx, subData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertSubscriptionData", reflect.TypeOf((*MockSubscriptionManager)(nil).UpsertSubscriptionData), ctx, subData)
}
