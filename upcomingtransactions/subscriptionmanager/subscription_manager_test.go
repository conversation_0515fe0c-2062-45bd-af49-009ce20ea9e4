package subscriptionmanager_test

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	mock_utils "github.com/epifi/gamma/insights/utils/mocks"
	"github.com/epifi/be-common/pkg/datetime"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/upcomingtransactions/config"
	"github.com/epifi/gamma/upcomingtransactions/consumer/types"
	"github.com/epifi/gamma/upcomingtransactions/dao"
	"github.com/epifi/gamma/upcomingtransactions/subscriptionmanager"
)

type SubsManagerTestSuite struct {
	db   *gorm.DB
	conf *config.Config
}

var (
	subsManagerTS *SubsManagerTestSuite
	may5          = timestamppb.New(time.Date(2023, 05, 05, 0, 0, 0, 0, datetime.IST))
	may6          = timestamppb.New(time.Date(2023, 05, 06, 0, 0, 0, 0, datetime.IST))
	may10         = timestamppb.New(time.Date(2023, 05, 10, 0, 0, 0, 0, datetime.IST))
	may11         = timestamppb.New(time.Date(2023, 05, 11, 0, 0, 0, 0, datetime.IST))
	may12         = timestamppb.New(time.Date(2023, 05, 12, 0, 0, 0, 0, datetime.IST))
)

func TestSubscriptionManagerImpl_UpsertSubscriptionData(t *testing.T) {
	t.Parallel()
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(subsManagerTS.conf.BudgetingDb, true)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}
	subDao := dao.NewSubscriptionDaoPgdb(db)
	upcomingTxnDao := dao.NewUpcomingTransactionDaoPgdb(db)
	type args struct {
		ctx     context.Context
		subData *types.SubscriptionData
	}
	tests := []struct {
		name             string
		args             args
		wantSubscription *modelPb.Subscription
		wantTxns         []*modelPb.UpcomingTransaction
		setupMocks       func(curTimeGenerator *mock_utils.MockCurrentTimeGenerator)
		wantErr          bool
	}{
		{
			name: "new subscription and txns",
			args: args{
				ctx: context.Background(),
				subData: &types.SubscriptionData{
					Subscription: &modelPb.Subscription{
						Id:                "sub-100",
						ActorId:           "actor-10",
						Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
						State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
						WithEntityId:      "actor-20",
						WithEntityType:    modelPb.EntityType_ENTITY_TYPE_ACTOR,
						LastTransactionId: "txn-50",
					},
					Transactions: []*modelPb.UpcomingTransaction{
						{
							ComputedHash:   "hash-123",
							ActorId:        "actor-10",
							WithEntityId:   "actor-20",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        300,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        400,
								Nanos:        0,
							},
							MinDate:           may5,
							MaxDate:           may10,
							SubscriptionId:    "sub-100",
							CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
							LastTransactionId: "txn-50",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2P,
						},
					},
				},
			},
			wantSubscription: &modelPb.Subscription{
				Id:                "sub-100",
				ActorId:           "actor-10",
				Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
				State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
				WithEntityId:      "actor-20",
				WithEntityType:    modelPb.EntityType_ENTITY_TYPE_ACTOR,
				LastTransactionId: "txn-50",
			},
			wantTxns: []*modelPb.UpcomingTransaction{
				{
					ComputedHash:   "hash-123",
					ActorId:        "actor-10",
					WithEntityId:   "actor-20",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        300,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        400,
						Nanos:        0,
					},
					MinDate:           may5,
					MaxDate:           may10,
					SubscriptionId:    "sub-100",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-50",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			setupMocks: func(curTimeGenerator *mock_utils.MockCurrentTimeGenerator) {
				curTimeGenerator.EXPECT().GetCurrentTimeInIST().Return(may6.AsTime())
			},
			wantErr: false,
		},
		{
			name: "upsert subscription (inactive in DB) and txns",
			args: args{
				ctx: context.Background(),
				subData: &types.SubscriptionData{
					Subscription: &modelPb.Subscription{
						Id:                "sub-1",
						ActorId:           "actor-5",
						Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
						State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
						WithEntityId:      "actor-6",
						WithEntityType:    modelPb.EntityType_ENTITY_TYPE_ACTOR,
						LastTransactionId: "txn-2",
					},
					Transactions: []*modelPb.UpcomingTransaction{
						{
							ComputedHash:   "hash-2",
							ActorId:        "actor-5",
							WithEntityId:   "actor-6",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        600,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        700,
								Nanos:        0,
							},
							MinDate:           may5,
							MaxDate:           may10,
							SubscriptionId:    "sub-1",
							CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
							LastTransactionId: "txn-2",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2P,
						},
					},
				},
			},
			wantSubscription: &modelPb.Subscription{
				Id:                "sub-1",
				ActorId:           "actor-5",
				Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
				State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
				WithEntityId:      "actor-6",
				WithEntityType:    modelPb.EntityType_ENTITY_TYPE_ACTOR,
				LastTransactionId: "txn-2",
			},
			wantTxns: []*modelPb.UpcomingTransaction{
				{
					ComputedHash:   "hash-2",
					ActorId:        "actor-5",
					WithEntityId:   "actor-6",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        600,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        700,
						Nanos:        0,
					},
					MinDate:           may5,
					MaxDate:           may10,
					SubscriptionId:    "sub-1",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-2",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			setupMocks: func(curTimeGenerator *mock_utils.MockCurrentTimeGenerator) {
				curTimeGenerator.EXPECT().GetCurrentTimeInIST().Return(may6.AsTime())
			},
			wantErr: false,
		},
		{
			name: "subscription is already disabled by user, no action",
			args: args{
				ctx: context.Background(),
				subData: &types.SubscriptionData{
					Subscription: &modelPb.Subscription{
						Id:                "sub-2",
						ActorId:           "actor-6",
						Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
						State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
						WithEntityId:      "actor-7",
						WithEntityType:    modelPb.EntityType_ENTITY_TYPE_ACTOR,
						LastTransactionId: "txn-2",
					},
					Transactions: []*modelPb.UpcomingTransaction{
						{
							ComputedHash:   "hash-3",
							ActorId:        "actor-6",
							WithEntityId:   "actor-7",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        600,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        700,
								Nanos:        0,
							},
							MinDate:           may5,
							MaxDate:           may10,
							SubscriptionId:    "sub-2",
							CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
							LastTransactionId: "txn-2",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2P,
						},
					},
				},
			},
			wantSubscription: &modelPb.Subscription{
				Id:                "sub-2",
				ActorId:           "actor-6",
				Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
				State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_DISABLED_BY_USER,
				WithEntityId:      "actor-7",
				WithEntityType:    modelPb.EntityType_ENTITY_TYPE_ACTOR,
				LastTransactionId: "txn-2",
			},
			wantTxns: nil,
			setupMocks: func(curTimeGenerator *mock_utils.MockCurrentTimeGenerator) {
			},
			wantErr: false,
		},
		{
			name: "upsert subs (1 upsert txn, 1 new addition, 1 no action since already executed , 1 ds invalidate)",
			args: args{
				ctx: context.Background(),
				subData: &types.SubscriptionData{
					Subscription: &modelPb.Subscription{
						Id:                "sub-3",
						ActorId:           "actor-7",
						Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
						State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
						WithEntityId:      "actor-8",
						WithEntityType:    modelPb.EntityType_ENTITY_TYPE_ACTOR,
						LastTransactionId: "txn-2",
					},
					Transactions: []*modelPb.UpcomingTransaction{
						{
							ComputedHash:   "hash-3",
							ActorId:        "actor-7",
							WithEntityId:   "actor-8",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        900,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        1200,
								Nanos:        0,
							},
							MinDate:           may5,
							MaxDate:           may10,
							SubscriptionId:    "sub-3",
							CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
							LastTransactionId: "txn-2",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2P,
						},
						{
							ComputedHash:   "hash-4",
							ActorId:        "actor-7",
							WithEntityId:   "actor-8",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        1000,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        1200,
								Nanos:        0,
							},
							MinDate:           may5,
							MaxDate:           may11,
							SubscriptionId:    "sub-3",
							CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
							LastTransactionId: "txn-2",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2P,
						},
						{
							ComputedHash:   "hash-100",
							ActorId:        "actor-7",
							WithEntityId:   "actor-8",
							WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
							MinAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        1900,
								Nanos:        0,
							},
							MaxAmount: &money.Money{
								CurrencyCode: "INR",
								Units:        2000,
								Nanos:        0,
							},
							MinDate:           may10,
							MaxDate:           may12,
							SubscriptionId:    "sub-3",
							CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
							LastTransactionId: "txn-2",
							ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
							Source:            modelPb.TxnSource_TXN_SOURCE_DS,
							Type:              modelPb.TxnType_TXN_TYPE_P2P,
						},
					},
				},
			},
			wantSubscription: &modelPb.Subscription{
				Id:                "sub-3",
				ActorId:           "actor-7",
				Frequency:         modelPb.SubscriptionFrequency_SUBSCRIPTION_FREQUENCY_MONTHLY,
				State:             modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE,
				WithEntityId:      "actor-8",
				WithEntityType:    modelPb.EntityType_ENTITY_TYPE_ACTOR,
				LastTransactionId: "txn-2",
			},
			wantTxns: []*modelPb.UpcomingTransaction{
				// upsert (update amount) since still not executed
				{
					ComputedHash:   "hash-4",
					ActorId:        "actor-7",
					WithEntityId:   "actor-8",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        1200,
						Nanos:        0,
					},
					MinDate:           may5,
					MaxDate:           may11,
					SubscriptionId:    "sub-3",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-2",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
				// no change since already executed
				{
					ComputedHash:   "hash-3",
					ActorId:        "actor-7",
					WithEntityId:   "actor-8",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        600,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        700,
						Nanos:        0,
					},
					MinDate:           may5,
					MaxDate:           may10,
					SubscriptionId:    "sub-3",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-2",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_EXECUTED,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
				// new insert
				{
					ComputedHash:   "hash-100",
					ActorId:        "actor-7",
					WithEntityId:   "actor-8",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        1900,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        2000,
						Nanos:        0,
					},
					MinDate:           may10,
					MaxDate:           may12,
					SubscriptionId:    "sub-3",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-2",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
				// ds invalidated
				{
					ComputedHash:   "hash-5",
					ActorId:        "actor-7",
					WithEntityId:   "actor-8",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        800,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        900,
						Nanos:        0,
					},
					MinDate:           may5,
					MaxDate:           may12,
					SubscriptionId:    "sub-3",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-2",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_DS_INVALIDATED,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			setupMocks: func(curTimeGenerator *mock_utils.MockCurrentTimeGenerator) {
				curTimeGenerator.EXPECT().GetCurrentTimeInIST().Return(may6.AsTime())
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			curTimeGen := mock_utils.NewMockCurrentTimeGenerator(ctrl)
			txnExecutor := storagev2.NewGormTxnExecutor(db)
			tt.setupMocks(curTimeGen)
			m := subscriptionmanager.NewSubscriptionManagerImpl(upcomingTxnDao, subDao, curTimeGen, txnExecutor)
			if err := m.UpsertSubscriptionData(tt.args.ctx, tt.args.subData); (err != nil) != tt.wantErr {
				t.Errorf("UpsertSubscriptionData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			actorId := tt.args.subData.Subscription.GetActorId()
			txns, err := upcomingTxnDao.GetByActor(context.Background(), actorId)
			if err != nil {
				t.Errorf("UpsertSubscriptionData() failed to get upcoming txns for matching : %v", err)
				return
			}
			sub, err := subDao.GetByActor(context.Background(), actorId)
			if err != nil {
				t.Errorf("UpsertSubscriptionData() failed to get subscription for matching : %v", err)
				return
			}
			if len(sub) != 1 {
				t.Errorf("UpsertSubscriptionData() len(subs) = %d, cannot match matching : %v", len(sub), err)
				return
			}
			if !isDeepEqualSubscription(sub[0], tt.wantSubscription) {
				t.Errorf("UpsertSubscriptionData() want subscription : %v\ngot : %v", tt.wantSubscription, sub[0])
				return
			}
			if !isDeepEqualUpcomingTransactionsArr(txns, tt.wantTxns) {
				t.Errorf("UpsertSubscriptionData() want txns : %v\ngot : %v", tt.wantTxns, txns)
				return
			}

		})
	}
}

func TestSubscriptionManagerImpl_GetUpcomingTransactionsWithEntityAfter(t *testing.T) {
	t.Parallel()
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(subsManagerTS.conf.BudgetingDb, true)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}
	subDao := dao.NewSubscriptionDaoPgdb(db)
	upcomingTxnDao := dao.NewUpcomingTransactionDaoPgdb(db)
	type args struct {
		ctx          context.Context
		actorId      string
		withEntityId string
		maxDateAfter *timestamppb.Timestamp
	}
	tests := []struct {
		name    string
		args    args
		want    []*modelPb.UpcomingTransaction
		wantErr bool
	}{
		{
			name: "actor id is empty, failure",
			args: args{
				ctx:          context.Background(),
				actorId:      "",
				withEntityId: "actor-8",
				maxDateAfter: may10,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "with_entity_id is empty, failure",
			args: args{
				ctx:          nil,
				actorId:      "actor-7",
				withEntityId: "",
				maxDateAfter: may10,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "max date is not given, failure",
			args: args{
				ctx:          nil,
				actorId:      "actor-7",
				withEntityId: "actor-8",
				maxDateAfter: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success fetch 1 (all records)",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-7",
				withEntityId: "actor-8",
				maxDateAfter: may5,
			},
			want: []*modelPb.UpcomingTransaction{
				{
					ComputedHash:   "hash-3",
					ActorId:        "actor-7",
					WithEntityId:   "actor-8",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        600,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        700,
						Nanos:        0,
					},
					MinDate:           may5,
					MaxDate:           may10,
					SubscriptionId:    "sub-3",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-2",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_EXECUTED,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
				{
					ComputedHash:   "hash-4",
					ActorId:        "actor-7",
					WithEntityId:   "actor-8",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        700,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        800,
						Nanos:        0,
					},
					MinDate:           may5,
					MaxDate:           may11,
					SubscriptionId:    "sub-3",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-2",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
				{
					ComputedHash:   "hash-5",
					ActorId:        "actor-7",
					WithEntityId:   "actor-8",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        800,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        900,
						Nanos:        0,
					},
					MinDate:           may5,
					MaxDate:           may12,
					SubscriptionId:    "sub-3",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-2",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			wantErr: false,
		},
		{
			name: "success fetch 2 (records before max date ignored)",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-7",
				withEntityId: "actor-8",
				maxDateAfter: may11,
			},
			want: []*modelPb.UpcomingTransaction{
				{
					ComputedHash:   "hash-5",
					ActorId:        "actor-7",
					WithEntityId:   "actor-8",
					WithEntityType: modelPb.EntityType_ENTITY_TYPE_ACTOR,
					MinAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        800,
						Nanos:        0,
					},
					MaxAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        900,
						Nanos:        0,
					},
					MinDate:           may5,
					MaxDate:           may12,
					SubscriptionId:    "sub-3",
					CreditDebit:       paymentPb.AccountingEntryType_DEBIT,
					LastTransactionId: "txn-2",
					ExecutionStatus:   modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET,
					Source:            modelPb.TxnSource_TXN_SOURCE_DS,
					Type:              modelPb.TxnType_TXN_TYPE_P2P,
				},
			},
			wantErr: false,
		},
		{
			name: "success fetch 3 (no record for filter conditions)",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-7",
				withEntityId: "actor-8",
				maxDateAfter: may12,
			},
			want:    []*modelPb.UpcomingTransaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			m := subscriptionmanager.NewSubscriptionManagerImpl(upcomingTxnDao, subDao, nil, nil)
			got, err := m.GetUpcomingTransactionsWithEntityAfter(tt.args.ctx, tt.args.actorId, tt.args.withEntityId, tt.args.maxDateAfter)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUpcomingTransactionsWithEntityAfter() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualUpcomingTransactionsArr(got, tt.want) {
				t.Errorf("GetUpcomingTransactionsWithEntityAfter() \nwant txns : %v\ngot : %v", tt.want, got)
				return
			}
		})
	}
}

func TestSubscriptionManagerImpl_UpdateTxnStatusByActorAndTxnId(t *testing.T) {
	t.Parallel()
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(subsManagerTS.conf.BudgetingDb, true)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}
	subDao := dao.NewSubscriptionDaoPgdb(db)
	upcomingTxnDao := dao.NewUpcomingTransactionDaoPgdb(db)
	type args struct {
		ctx       context.Context
		actorId   string
		txnId     string
		txnStatus modelPb.TxnStatus
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "actor id is empty, failure",
			args: args{
				ctx:       context.Background(),
				actorId:   "",
				txnId:     "e1e6f32b-8b8f-4276-a119-41f010e57a40",
				txnStatus: modelPb.TxnStatus_TXN_STATUS_USER_INVALIDATED,
			},
			wantErr: true,
		},
		{
			name: "txn id is empty, failure",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-7",
				txnId:     "",
				txnStatus: modelPb.TxnStatus_TXN_STATUS_USER_INVALIDATED,
			},
			wantErr: true,
		},
		{
			name: "txn status is unspecified, failure",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-7",
				txnId:     "e1e6f32b-8b8f-4276-a119-41f010e57a40",
				txnStatus: modelPb.TxnStatus_TXN_STATUS_UNSPECIFIED,
			},
			wantErr: true,
		},
		{
			name: "update success",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-7",
				txnId:     "e1e6f32b-8b8f-4276-a119-41f010e57a40",
				txnStatus: modelPb.TxnStatus_TXN_STATUS_USER_INVALIDATED,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			m := subscriptionmanager.NewSubscriptionManagerImpl(upcomingTxnDao, subDao, nil, nil)
			err := m.UpdateTxnStatusByActorAndTxnId(tt.args.ctx, tt.args.actorId, tt.args.txnId, tt.args.txnStatus)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateTxnStatusByActorAndTxnId() error = %v, wantErr %v", err, tt.wantErr)
			}
			if err != nil {
				return
			}
			daoResp, err := upcomingTxnDao.GetByActor(context.Background(), tt.args.actorId, dao.WhereUpcomingTxnIdIs(tt.args.txnId))
			if err != nil {
				t.Errorf("UpdateTxnStatusByActorAndTxnId() failed to validate if txn status is updated or not, error = %v", err)
				return
			}
			if len(daoResp) != 1 {
				t.Errorf("UpdateTxnStatusByActorAndTxnId() while validating status got %d records instead of 1", len(daoResp))
				return
			}
			if daoResp[0].GetExecutionStatus() != tt.args.txnStatus {
				t.Errorf("UpdateTxnStatusByActorAndTxnId() txn_status got : %s, want : %s", daoResp[0].GetExecutionStatus().String(), tt.args.txnStatus.String())
				return
			}
		})
	}
}

func isDeepEqualUpcomingTransactions(actual, expected *modelPb.UpcomingTransaction) bool {
	if expected != nil && actual != nil {
		expected.Id = actual.Id
		expected.UpdatedAt = actual.UpdatedAt
		expected.CreatedAt = actual.CreatedAt
		expected.DeletedAt = actual.DeletedAt
	}
	diff := cmp.Diff(actual, expected, protocmp.Transform())
	return diff == ""
}

func isDeepEqualUpcomingTransactionsArr(actual, expected []*modelPb.UpcomingTransaction) bool {
	if len(actual) != len(expected) {
		return false
	}
	for _, expectedEntry := range expected {
		found := false
		for _, actualEntry := range actual {
			if isDeepEqualUpcomingTransactions(actualEntry, expectedEntry) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}

func isDeepEqualSubscription(actual, expected *modelPb.Subscription) bool {
	if expected != nil && actual != nil {
		expected.Id = actual.Id
		expected.UpdatedAt = actual.UpdatedAt
		expected.CreatedAt = actual.CreatedAt
		expected.DeletedAt = actual.DeletedAt
	}
	diff := cmp.Diff(actual, expected, protocmp.Transform())
	return diff == ""
}
