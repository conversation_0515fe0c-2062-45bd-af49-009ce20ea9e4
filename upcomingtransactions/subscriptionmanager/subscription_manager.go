//go:generate mockgen -source=subscription_manager.go -destination=./mocks/mock_subscription_manager.go package=mocks
package subscriptionmanager

import (
	"context"
	"fmt"

	"github.com/google/wire"
	"google.golang.org/protobuf/types/known/timestamppb"

	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/gamma/insights/utils"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/upcomingtransactions/consumer/types"
	"github.com/epifi/gamma/upcomingtransactions/dao"
)

var (
	actorIdEmptyErr = fmt.Errorf("actor_id is empty")
)

var SubscriptionManagerWireSet = wire.NewSet(NewSubscriptionManagerImpl, wire.Bind(new(SubscriptionManager), new(*SubscriptionManagerImpl)))

type SubscriptionManager interface {
	UpsertSubscriptionData(ctx context.Context, subData *types.SubscriptionData) error
	GetUpcomingTransactionsWithEntityAfter(ctx context.Context, actorId string, withEntityId string, maxDateAfter *timestamppb.Timestamp) ([]*modelPb.UpcomingTransaction, error)
	UpdateTxnStatusByActorAndTxnId(ctx context.Context, actorId string, txnId string, txnStatus modelPb.TxnStatus) error
}

type SubscriptionManagerImpl struct {
	upcomingTxnDao   dao.UpcomingTransactionDao
	subscriptionDao  dao.SubscriptionDao
	curTimeGenerator utils.CurrentTimeGenerator
	txnExecutor      storagev2.TxnExecutor
}

func NewSubscriptionManagerImpl(upcomingTxnDao dao.UpcomingTransactionDao, subscriptionDao dao.SubscriptionDao,
	curTimeGenerator utils.CurrentTimeGenerator, txnExecutor storagev2.TxnExecutor) *SubscriptionManagerImpl {
	return &SubscriptionManagerImpl{
		upcomingTxnDao:   upcomingTxnDao,
		subscriptionDao:  subscriptionDao,
		curTimeGenerator: curTimeGenerator,
		txnExecutor:      txnExecutor,
	}
}

func (m *SubscriptionManagerImpl) UpsertSubscriptionData(ctx context.Context, subData *types.SubscriptionData) error {
	if subData == nil {
		return nil
	}
	if subData.Subscription == nil {
		return nil
	}

	txnErr := m.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// insert all new subscription and on conflict (sub-id) upsert all new subscriptions where state is not disabled by user already
		err := m.subscriptionDao.Upsert(txnCtx, []*modelPb.Subscription{subData.Subscription},
			dao.ClauseOnConflictDoUpdateAll([]string{"id"}, dao.WhereClauseSubscriptionStateNotIn(modelPb.SubscriptionState_SUBSCRIPTION_STATE_DISABLED_BY_USER)))
		if err != nil {
			return fmt.Errorf("failed to upsert subscriptions : %w", err)
		}

		subState, err := m.getStateOfSubscription(txnCtx, subData.Subscription.GetActorId(), subData.Subscription.GetId())
		if err != nil {
			return fmt.Errorf("failed to get subscriptions state from db : %w", err)
		}

		switch subState {
		case modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE:
			// insert all new transactions + on conflict (computed_hash) upsert transaction whose execution_state is not user_invalidated or executed
			err = m.upcomingTxnDao.Upsert(txnCtx, subData.Transactions, dao.ClauseOnConflictDoUpdateAll([]string{"computed_hash"},
				dao.WhereExecutionStatusNotInClause(modelPb.TxnStatus_TXN_STATUS_USER_INVALIDATED, modelPb.TxnStatus_TXN_STATUS_EXECUTED)))
			if err != nil {
				return fmt.Errorf("failed to upsert upcoming trasactions : %w", err)
			}
			// invalidate remaining txn in the subscription if they are not part of current txns in sub_data and not executed yet
			err = m.dsInvalidateTxnsBasedOnSubState(txnCtx, subData, subState)
		case modelPb.SubscriptionState_SUBSCRIPTION_STATE_EXPIRED:
			// invalidate all upcoming txns txn in the subscription which are not executed yet
			err = m.dsInvalidateTxnsBasedOnSubState(txnCtx, subData, subState)
		}
		return err
	})

	return txnErr
}

func (m *SubscriptionManagerImpl) getStateOfSubscription(ctx context.Context, actorId string, subId string) (modelPb.SubscriptionState, error) {
	subResp, err := m.subscriptionDao.GetByActor(ctx, actorId, dao.WhereIdIs(subId))
	if err != nil {
		return 0, fmt.Errorf("failed to subscription %s for actor : %w", subId, err)
	}
	if len(subResp) != 1 {
		return 0, fmt.Errorf("expected 1 subs in db, subId %s, got %d", subId, len(subResp))
	}
	return subResp[0].GetState(), nil
}

func (m *SubscriptionManagerImpl) dsInvalidateTxnsBasedOnSubState(ctx context.Context, subData *types.SubscriptionData, subState modelPb.SubscriptionState) error {
	curTime := m.curTimeGenerator.GetCurrentTimeInIST()
	hashArr := getHashArray(subData.Transactions)
	var filters []storagev2.FilterOption

	filters = append(filters, dao.WhereSubscriptionIdIs(subData.Subscription.GetId()))
	filters = append(filters, dao.WhereMaxDateAfter(curTime))

	// if there are some txns in req and sub state is inactive we disable only remaining txn, otherwise we ds invalidate all upcoming txns
	if len(hashArr) > 0 && subState == modelPb.SubscriptionState_SUBSCRIPTION_STATE_ACTIVE {
		filters = append(filters, dao.WhereComputedHashNotIn(hashArr))
	}
	filters = append(filters, dao.WhereExecutionStatusIs(modelPb.TxnStatus_TXN_STATUS_NOT_EXECUTED_YET))

	err := m.upcomingTxnDao.UpdateForActor(ctx, subData.Subscription.GetActorId(), &modelPb.UpcomingTransaction{
		ExecutionStatus: modelPb.TxnStatus_TXN_STATUS_DS_INVALIDATED,
	}, []modelPb.UpcomingTransactionFieldMask{
		modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_EXECUTION_STATUS,
	}, filters...)

	return err
}

func (m *SubscriptionManagerImpl) GetUpcomingTransactionsWithEntityAfter(ctx context.Context, actorId string, withEntityId string, maxDateAfter *timestamppb.Timestamp) ([]*modelPb.UpcomingTransaction, error) {
	if actorId == "" {
		return nil, actorIdEmptyErr
	}
	if withEntityId == "" {
		return nil, fmt.Errorf("with entity id is empty, cannot get upcoming txns with entity")
	}
	if maxDateAfter == nil {
		return nil, fmt.Errorf("max date is not provided, failure cannot get upcoming txns")
	}
	upTxns, err := m.upcomingTxnDao.GetByActor(ctx, actorId, dao.WhereWithEntityIdIs(withEntityId), dao.MaxDateAfterTimestamp(maxDateAfter))
	if err != nil {
		return nil, fmt.Errorf("failed to get upcoming txns for actor : %w", err)
	}
	return upTxns, nil
}

func (m *SubscriptionManagerImpl) UpdateTxnStatusByActorAndTxnId(ctx context.Context, actorId string, txnId string, txnStatus modelPb.TxnStatus) error {
	switch {
	case actorId == "":
		return actorIdEmptyErr
	case txnId == "":
		return fmt.Errorf("upcoming_txn_id is empty, cannot update txn status")
	case txnStatus == modelPb.TxnStatus_TXN_STATUS_UNSPECIFIED:
		return fmt.Errorf("txn status is unspecified, cannot update txn status")
	}
	return m.upcomingTxnDao.UpdateForActor(ctx, actorId, &modelPb.UpcomingTransaction{ExecutionStatus: txnStatus}, []modelPb.UpcomingTransactionFieldMask{
		modelPb.UpcomingTransactionFieldMask_UPCOMING_TRANSACTION_FIELD_MASK_EXECUTION_STATUS,
	}, dao.WhereUpcomingTxnIdIs(txnId))
}

func getHashArray(txns []*modelPb.UpcomingTransaction) []string {
	var hashArr []string
	for _, txn := range txns {
		hashArr = append(hashArr, txn.GetComputedHash())
	}
	return hashArr
}
