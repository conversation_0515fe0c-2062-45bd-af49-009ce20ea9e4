package developer

import (
	"context"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/upcomingtransactions/developer"
	"github.com/epifi/be-common/pkg/logger"
)

type UpcomingTransactionsDevService struct {
	fac *DevFactory
}

func NewUpcomingTransactionsDevService(fac *DevFactory) *UpcomingTransactionsDevService {
	return &UpcomingTransactionsDevService{
		fac: fac,
	}
}

func (c *UpcomingTransactionsDevService) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status: rpcPb.StatusOk(),
		EntityList: []string{
			developer.UpcomingTransactionsEntity_UPCOMING_TRANSACTIONS.String(),
			developer.UpcomingTransactionsEntity_SUBSCRIPTIONS.String(),
		},
	}, nil
}

func (c *UpcomingTransactionsDevService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := developer.UpcomingTransactionsEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in insights"),
		}, nil
	}
	paramFetcher, err := c.fac.getParameterListImpl(developer.UpcomingTransactionsEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in insights")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.UpcomingTransactionsEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (c *UpcomingTransactionsDevService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.UpcomingTransactionsEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in comms"),
		}, nil
	}
	dataFetcher, err := c.fac.getDataImpl(developer.UpcomingTransactionsEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in insights")
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.UpcomingTransactionsEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data")
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
