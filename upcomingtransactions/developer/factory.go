package developer

import (
	"fmt"

	"github.com/epifi/gamma/api/upcomingtransactions/developer"
	"github.com/epifi/gamma/upcomingtransactions/developer/processor"
)

type DevFactory struct {
	UpcomingTransaction *processor.UpcomingTransaction
	Subscription        *processor.Subscription
}

func NewDevFactory(upcomingTransaction *processor.UpcomingTransaction, subscription *processor.Subscription) *DevFactory {
	return &DevFactory{
		UpcomingTransaction: upcomingTransaction,
		Subscription:        subscription,
	}
}

func (d *DevFactory) getParameterListImpl(entity developer.UpcomingTransactionsEntity) (IParameterFetcher, error) {
	switch entity {
	case developer.UpcomingTransactionsEntity_UPCOMING_TRANSACTIONS:
		return d.UpcomingTransaction, nil
	case developer.UpcomingTransactionsEntity_SUBSCRIPTIONS:
		return d.Subscription, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}

func (d *DevFactory) getDataImpl(entity developer.UpcomingTransactionsEntity) (IDataFetcher, error) {
	switch entity {
	case developer.UpcomingTransactionsEntity_UPCOMING_TRANSACTIONS:
		return d.UpcomingTransaction, nil
	case developer.UpcomingTransactionsEntity_SUBSCRIPTIONS:
		return d.Subscription, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}
