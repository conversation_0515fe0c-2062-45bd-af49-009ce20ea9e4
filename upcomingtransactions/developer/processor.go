package developer

import (
	"context"

	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/upcomingtransactions/developer"
)

type IParameterFetcher interface {
	FetchParamList(ctx context.Context, entity developer.UpcomingTransactionsEntity) ([]*cxDsPb.ParameterMeta, error)
}

type IDataFetcher interface {
	FetchData(ctx context.Context, entity developer.UpcomingTransactionsEntity, filters []*cxDsPb.Filter) (string, error)
}
