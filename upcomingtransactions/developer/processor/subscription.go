//nolint:dupl
package processor

import (
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/upcomingtransactions/developer"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	dao2 "github.com/epifi/gamma/upcomingtransactions/dao"
)

const (
	NoSubscriptionsFound = "No subscriptions found for actor with given filter"
)

type Subscription struct {
	subscriptionDao dao2.SubscriptionDao
}

func NewSubscription(subscriptionDao dao2.SubscriptionDao) *Subscription {
	return &Subscription{
		subscriptionDao: subscriptionDao,
	}
}

func (s *Subscription) FetchParamList(ctx context.Context, entity developer.UpcomingTransactionsEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorIdParamName,
			Label:           "Actor Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            SubscriptionIdParamName,
			Label:           "Subscription Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (s *Subscription) FetchData(ctx context.Context, entity developer.UpcomingTransactionsEntity, filters []*db_state.Filter) (string, error) {
	var (
		actorId, subId string
		err            error
	)
	for _, filter := range filters {
		switch filter.ParameterName {
		case ActorIdParamName:
			actorId = filter.GetStringValue()
			if actorId == "" {
				logger.Error(ctx, "actor id cannot be empty, cannot fetch subscriptions using db states")
				return fmt.Sprintf("actor id is empty, cannot fetch subscription using db states"), nil
			}
		case SubscriptionIdParamName:
			subId = filter.GetStringValue()
		}
	}
	var filterOptions []storagev2.FilterOption
	if subId != "" {
		filterOptions = append(filterOptions, dao2.WhereIdIs(subId))
	}
	res, err := s.subscriptionDao.GetByActor(ctx, actorId, filterOptions...)
	if len(res) == 0 {
		logger.Info(ctx, "no subscriptions found for actor with given filters")
		return NoSubscriptionsFound, nil
	}

	jsonResp, err := json.Marshal(res)
	if err != nil {
		logger.Error(ctx, "db response json marshal failed, cannot get subscriptions", zap.Error(err))
		return err.Error(), nil
	}
	return string(jsonResp), nil
}
