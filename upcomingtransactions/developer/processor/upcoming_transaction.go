//nolint:dupl
package processor

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/upcomingtransactions/developer"
	modelPb "github.com/epifi/gamma/api/upcomingtransactions/model"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	dao2 "github.com/epifi/gamma/upcomingtransactions/dao"
)

const (
	ActorIdParamName        = "actor_id"
	SubscriptionIdParamName = "subscription_id"
	FromDateParamName       = "from_date"
	ToDateParamName         = "to_date"

	NoUpcomingTxnsFound = "No upcoming transactions found for actor with given filter"

	DateLayout = "2006-01-02"
)

type UpcomingTransaction struct {
	upcomingTransactionDao dao2.UpcomingTransactionDao
}

func NewUpcomingTransaction(upcomingTransactionsDao dao2.UpcomingTransactionDao) *UpcomingTransaction {
	return &UpcomingTransaction{
		upcomingTransactionDao: upcomingTransactionsDao,
	}
}

func (s *UpcomingTransaction) FetchParamList(ctx context.Context, entity developer.UpcomingTransactionsEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorIdParamName,
			Label:           "Actor Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            SubscriptionIdParamName,
			Label:           "Subscription Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            FromDateParamName,
			Label:           "From Date",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            ToDateParamName,
			Label:           "To Date",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (s *UpcomingTransaction) FetchData(ctx context.Context, entity developer.UpcomingTransactionsEntity, filters []*db_state.Filter) (string, error) {
	var (
		actorId, subId   string
		fromDate, toDate time.Time
		err              error
	)
	for _, filter := range filters {
		switch filter.ParameterName {

		case ActorIdParamName:
			actorId = filter.GetStringValue()
			if actorId == "" {
				logger.Error(ctx, "actor id cannot be empty, cannot fetch upcoming txns using db states")
				return fmt.Sprintf("actor id is empty, cannot fetch upcoming txns using db states"), nil
			}
		case SubscriptionIdParamName:
			subId = filter.GetStringValue()
		case FromDateParamName:
			fromDate, err = getTimeFromString(filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("invalid from_date: %s in input, cannot get upcoming txns using db states", filter.GetStringValue()))
				return fmt.Sprintf("invalid from_date: %s in input, cannot get upcoming txns using db states", filter.GetStringValue()), nil
			}
		case ToDateParamName:
			toDate, err = getTimeFromString(filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("invalid to_date: %s in input, cannot get upcoming txns using db states", filter.GetStringValue()))
				return fmt.Sprintf("invalid to_date: %s in input, cannot get upcoming txns using db states", filter.GetStringValue()), nil
			}
			toDate = datetime.EndOfDay(toDate)
		}
	}
	var filterOptions []storagev2.FilterOption
	filterOptions = append(filterOptions, dao2.WhereTxnInDateRange(fromDate, toDate))
	if subId != "" {
		filterOptions = append(filterOptions, dao2.WhereSubscriptionIdIs(subId))
	}
	res, err := s.upcomingTransactionDao.GetByActor(ctx, actorId, filterOptions...)
	if err != nil {
		logger.Error(ctx, "failed to fetch upcoming txn using db state", zap.Error(err))
		return fmt.Sprintf("failed to fetch upcoming txn using db state : %s", err.Error()), nil
	}
	if len(res) == 0 {
		logger.Info(ctx, "no upcoming transactions found for actor with given filters")
		return NoUpcomingTxnsFound, nil
	}

	maskAmountInUpcomingTxns(res)

	jsonResp, err := json.Marshal(res)
	if err != nil {
		logger.Error(ctx, "db response json marshal failed, cannot get upcoming transactions", zap.Error(err))
		return err.Error(), nil
	}
	return string(jsonResp), nil

}

// given a date string in DateLayout (2010-05-25) format, it then returns the parsed date converted to timestamp
func getTimeFromString(dateStr string) (time.Time, error) {
	layout := DateLayout
	parsedDate, dateParseErr := time.ParseInLocation(layout, dateStr, datetime.IST)
	if dateParseErr != nil {
		return time.Time{}, fmt.Errorf("failed to parse date to given DateLayout format : %w", dateParseErr)
	}
	return parsedDate, nil
}

func maskAmountInUpcomingTxns(txns []*modelPb.UpcomingTransaction) {
	for _, txn := range txns {
		txn.MinAmount = &money.Money{
			CurrencyCode: "INR",
			Units:        0,
		}
		txn.MaxAmount = &money.Money{
			CurrencyCode: "INR",
			Units:        0,
		}
	}
}
