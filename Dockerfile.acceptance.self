FROM 632884248997.dkr.ecr.ap-south-1.amazonaws.com/gamma-dependencies:latest

RUN curl -JLO "https://dl.filippo.io/mkcert/v1.4.4?for=linux/amd64" && \
    chmod +x mkcert-v*-linux-amd64 && \
    cp mkcert-v*-linux-amd64 /usr/local/bin/mkcert && \
    mkdir -p /root/.pki/

RUN mkcert -install && mkcert -cert-file /root/.pki/localhost.pem -key-file /root/.pki/localhost-key.pem localhost 127.0.0.1

# Accept service_name and go_proxy as build-arg
ARG dir
ARG goproxy

# Ref: https://stackoverflow.com/questions/35560894/is-docker-arg-allowed-within-cmd-instruction
ENV dir ${dir}
ENV alert ${alert}
ENV GOPROXY ${goproxy}

COPY . /go/src/github.com/epifi/gamma/
WORKDIR /go/src/github.com/epifi/gamma/

# Building test executable outside container for speed
RUN go test -c ./${dir} -o ./${dir}/output.test

# Run tests
CMD ACCEPTANCE_TEST=TRUE make test_acceptance target=${dir} testcase=${testcase}
