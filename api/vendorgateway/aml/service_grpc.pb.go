// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/aml/service.proto

package aml

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Aml_ScreenCustomer_FullMethodName    = "/vendorgateway.aml.Aml/ScreenCustomer"
	Aml_InitiateScreening_FullMethodName = "/vendorgateway.aml.Aml/InitiateScreening"
	Aml_ListCases_FullMethodName         = "/vendorgateway.aml.Aml/ListCases"
	Aml_GetCaseDetails_FullMethodName    = "/vendorgateway.aml.Aml/GetCaseDetails"
)

// AmlClient is the client API for Aml service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AmlClient interface {
	// RPC to check the user's details for any money laundering activities
	// by querying against the vendor's database of money launderers.
	ScreenCustomer(ctx context.Context, in *ScreenCustomerRequest, opts ...grpc.CallOption) (*ScreenCustomerResponse, error)
	// Initiate screening a user's details against money-laundering, terrorist financing, and/or adverse-media activities
	// by matching against configured watchlists
	InitiateScreening(ctx context.Context, in *InitiateScreeningRequest, opts ...grpc.CallOption) (*InitiateScreeningResponse, error)
	// List cases with information for cases that are created or modified in the given date range
	ListCases(ctx context.Context, in *ListCasesRequest, opts ...grpc.CallOption) (*ListCasesResponse, error)
	// Get details of a specific case ID
	GetCaseDetails(ctx context.Context, in *GetCaseDetailsRequest, opts ...grpc.CallOption) (*GetCaseDetailsResponse, error)
}

type amlClient struct {
	cc grpc.ClientConnInterface
}

func NewAmlClient(cc grpc.ClientConnInterface) AmlClient {
	return &amlClient{cc}
}

func (c *amlClient) ScreenCustomer(ctx context.Context, in *ScreenCustomerRequest, opts ...grpc.CallOption) (*ScreenCustomerResponse, error) {
	out := new(ScreenCustomerResponse)
	err := c.cc.Invoke(ctx, Aml_ScreenCustomer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *amlClient) InitiateScreening(ctx context.Context, in *InitiateScreeningRequest, opts ...grpc.CallOption) (*InitiateScreeningResponse, error) {
	out := new(InitiateScreeningResponse)
	err := c.cc.Invoke(ctx, Aml_InitiateScreening_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *amlClient) ListCases(ctx context.Context, in *ListCasesRequest, opts ...grpc.CallOption) (*ListCasesResponse, error) {
	out := new(ListCasesResponse)
	err := c.cc.Invoke(ctx, Aml_ListCases_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *amlClient) GetCaseDetails(ctx context.Context, in *GetCaseDetailsRequest, opts ...grpc.CallOption) (*GetCaseDetailsResponse, error) {
	out := new(GetCaseDetailsResponse)
	err := c.cc.Invoke(ctx, Aml_GetCaseDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AmlServer is the server API for Aml service.
// All implementations should embed UnimplementedAmlServer
// for forward compatibility
type AmlServer interface {
	// RPC to check the user's details for any money laundering activities
	// by querying against the vendor's database of money launderers.
	ScreenCustomer(context.Context, *ScreenCustomerRequest) (*ScreenCustomerResponse, error)
	// Initiate screening a user's details against money-laundering, terrorist financing, and/or adverse-media activities
	// by matching against configured watchlists
	InitiateScreening(context.Context, *InitiateScreeningRequest) (*InitiateScreeningResponse, error)
	// List cases with information for cases that are created or modified in the given date range
	ListCases(context.Context, *ListCasesRequest) (*ListCasesResponse, error)
	// Get details of a specific case ID
	GetCaseDetails(context.Context, *GetCaseDetailsRequest) (*GetCaseDetailsResponse, error)
}

// UnimplementedAmlServer should be embedded to have forward compatible implementations.
type UnimplementedAmlServer struct {
}

func (UnimplementedAmlServer) ScreenCustomer(context.Context, *ScreenCustomerRequest) (*ScreenCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScreenCustomer not implemented")
}
func (UnimplementedAmlServer) InitiateScreening(context.Context, *InitiateScreeningRequest) (*InitiateScreeningResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateScreening not implemented")
}
func (UnimplementedAmlServer) ListCases(context.Context, *ListCasesRequest) (*ListCasesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCases not implemented")
}
func (UnimplementedAmlServer) GetCaseDetails(context.Context, *GetCaseDetailsRequest) (*GetCaseDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCaseDetails not implemented")
}

// UnsafeAmlServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AmlServer will
// result in compilation errors.
type UnsafeAmlServer interface {
	mustEmbedUnimplementedAmlServer()
}

func RegisterAmlServer(s grpc.ServiceRegistrar, srv AmlServer) {
	s.RegisterService(&Aml_ServiceDesc, srv)
}

func _Aml_ScreenCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScreenCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlServer).ScreenCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aml_ScreenCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlServer).ScreenCustomer(ctx, req.(*ScreenCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aml_InitiateScreening_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateScreeningRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlServer).InitiateScreening(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aml_InitiateScreening_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlServer).InitiateScreening(ctx, req.(*InitiateScreeningRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aml_ListCases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlServer).ListCases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aml_ListCases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlServer).ListCases(ctx, req.(*ListCasesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aml_GetCaseDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCaseDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlServer).GetCaseDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aml_GetCaseDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlServer).GetCaseDetails(ctx, req.(*GetCaseDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Aml_ServiceDesc is the grpc.ServiceDesc for Aml service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Aml_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.aml.Aml",
	HandlerType: (*AmlServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ScreenCustomer",
			Handler:    _Aml_ScreenCustomer_Handler,
		},
		{
			MethodName: "InitiateScreening",
			Handler:    _Aml_InitiateScreening_Handler,
		},
		{
			MethodName: "ListCases",
			Handler:    _Aml_ListCases_Handler,
		},
		{
			MethodName: "GetCaseDetails",
			Handler:    _Aml_GetCaseDetails_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/aml/service.proto",
}
