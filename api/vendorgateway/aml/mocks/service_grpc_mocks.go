// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/aml/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	aml "github.com/epifi/gamma/api/vendorgateway/aml"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAmlClient is a mock of AmlClient interface.
type MockAmlClient struct {
	ctrl     *gomock.Controller
	recorder *MockAmlClientMockRecorder
}

// MockAmlClientMockRecorder is the mock recorder for MockAmlClient.
type MockAmlClientMockRecorder struct {
	mock *MockAmlClient
}

// NewMockAmlClient creates a new mock instance.
func NewMockAmlClient(ctrl *gomock.Controller) *MockAmlClient {
	mock := &MockAmlClient{ctrl: ctrl}
	mock.recorder = &MockAmlClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlClient) EXPECT() *MockAmlClientMockRecorder {
	return m.recorder
}

// GetCaseDetails mocks base method.
func (m *MockAmlClient) GetCaseDetails(ctx context.Context, in *aml.GetCaseDetailsRequest, opts ...grpc.CallOption) (*aml.GetCaseDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCaseDetails", varargs...)
	ret0, _ := ret[0].(*aml.GetCaseDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCaseDetails indicates an expected call of GetCaseDetails.
func (mr *MockAmlClientMockRecorder) GetCaseDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCaseDetails", reflect.TypeOf((*MockAmlClient)(nil).GetCaseDetails), varargs...)
}

// InitiateScreening mocks base method.
func (m *MockAmlClient) InitiateScreening(ctx context.Context, in *aml.InitiateScreeningRequest, opts ...grpc.CallOption) (*aml.InitiateScreeningResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateScreening", varargs...)
	ret0, _ := ret[0].(*aml.InitiateScreeningResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateScreening indicates an expected call of InitiateScreening.
func (mr *MockAmlClientMockRecorder) InitiateScreening(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateScreening", reflect.TypeOf((*MockAmlClient)(nil).InitiateScreening), varargs...)
}

// ListCases mocks base method.
func (m *MockAmlClient) ListCases(ctx context.Context, in *aml.ListCasesRequest, opts ...grpc.CallOption) (*aml.ListCasesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListCases", varargs...)
	ret0, _ := ret[0].(*aml.ListCasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCases indicates an expected call of ListCases.
func (mr *MockAmlClientMockRecorder) ListCases(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCases", reflect.TypeOf((*MockAmlClient)(nil).ListCases), varargs...)
}

// ScreenCustomer mocks base method.
func (m *MockAmlClient) ScreenCustomer(ctx context.Context, in *aml.ScreenCustomerRequest, opts ...grpc.CallOption) (*aml.ScreenCustomerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScreenCustomer", varargs...)
	ret0, _ := ret[0].(*aml.ScreenCustomerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScreenCustomer indicates an expected call of ScreenCustomer.
func (mr *MockAmlClientMockRecorder) ScreenCustomer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScreenCustomer", reflect.TypeOf((*MockAmlClient)(nil).ScreenCustomer), varargs...)
}

// MockAmlServer is a mock of AmlServer interface.
type MockAmlServer struct {
	ctrl     *gomock.Controller
	recorder *MockAmlServerMockRecorder
}

// MockAmlServerMockRecorder is the mock recorder for MockAmlServer.
type MockAmlServerMockRecorder struct {
	mock *MockAmlServer
}

// NewMockAmlServer creates a new mock instance.
func NewMockAmlServer(ctrl *gomock.Controller) *MockAmlServer {
	mock := &MockAmlServer{ctrl: ctrl}
	mock.recorder = &MockAmlServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlServer) EXPECT() *MockAmlServerMockRecorder {
	return m.recorder
}

// GetCaseDetails mocks base method.
func (m *MockAmlServer) GetCaseDetails(arg0 context.Context, arg1 *aml.GetCaseDetailsRequest) (*aml.GetCaseDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCaseDetails", arg0, arg1)
	ret0, _ := ret[0].(*aml.GetCaseDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCaseDetails indicates an expected call of GetCaseDetails.
func (mr *MockAmlServerMockRecorder) GetCaseDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCaseDetails", reflect.TypeOf((*MockAmlServer)(nil).GetCaseDetails), arg0, arg1)
}

// InitiateScreening mocks base method.
func (m *MockAmlServer) InitiateScreening(arg0 context.Context, arg1 *aml.InitiateScreeningRequest) (*aml.InitiateScreeningResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateScreening", arg0, arg1)
	ret0, _ := ret[0].(*aml.InitiateScreeningResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateScreening indicates an expected call of InitiateScreening.
func (mr *MockAmlServerMockRecorder) InitiateScreening(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateScreening", reflect.TypeOf((*MockAmlServer)(nil).InitiateScreening), arg0, arg1)
}

// ListCases mocks base method.
func (m *MockAmlServer) ListCases(arg0 context.Context, arg1 *aml.ListCasesRequest) (*aml.ListCasesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCases", arg0, arg1)
	ret0, _ := ret[0].(*aml.ListCasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCases indicates an expected call of ListCases.
func (mr *MockAmlServerMockRecorder) ListCases(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCases", reflect.TypeOf((*MockAmlServer)(nil).ListCases), arg0, arg1)
}

// ScreenCustomer mocks base method.
func (m *MockAmlServer) ScreenCustomer(arg0 context.Context, arg1 *aml.ScreenCustomerRequest) (*aml.ScreenCustomerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScreenCustomer", arg0, arg1)
	ret0, _ := ret[0].(*aml.ScreenCustomerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScreenCustomer indicates an expected call of ScreenCustomer.
func (mr *MockAmlServerMockRecorder) ScreenCustomer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScreenCustomer", reflect.TypeOf((*MockAmlServer)(nil).ScreenCustomer), arg0, arg1)
}

// MockUnsafeAmlServer is a mock of UnsafeAmlServer interface.
type MockUnsafeAmlServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAmlServerMockRecorder
}

// MockUnsafeAmlServerMockRecorder is the mock recorder for MockUnsafeAmlServer.
type MockUnsafeAmlServerMockRecorder struct {
	mock *MockUnsafeAmlServer
}

// NewMockUnsafeAmlServer creates a new mock instance.
func NewMockUnsafeAmlServer(ctrl *gomock.Controller) *MockUnsafeAmlServer {
	mock := &MockUnsafeAmlServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAmlServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAmlServer) EXPECT() *MockUnsafeAmlServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAmlServer mocks base method.
func (m *MockUnsafeAmlServer) mustEmbedUnimplementedAmlServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAmlServer")
}

// mustEmbedUnimplementedAmlServer indicates an expected call of mustEmbedUnimplementedAmlServer.
func (mr *MockUnsafeAmlServerMockRecorder) mustEmbedUnimplementedAmlServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAmlServer", reflect.TypeOf((*MockUnsafeAmlServer)(nil).mustEmbedUnimplementedAmlServer))
}
