// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/tss/tss.proto

package tss

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ScreenCustomerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XmlString string `protobuf:"bytes,1,opt,name=xml_string,json=data,proto3" json:"xml_string,omitempty"`
}

func (x *ScreenCustomerRequest) Reset() {
	*x = ScreenCustomerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenCustomerRequest) ProtoMessage() {}

func (x *ScreenCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenCustomerRequest.ProtoReflect.Descriptor instead.
func (*ScreenCustomerRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{0}
}

func (x *ScreenCustomerRequest) GetXmlString() string {
	if x != nil {
		return x.XmlString
	}
	return ""
}

type ScreenCustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResultXml string `protobuf:"bytes,1,opt,name=result_xml,json=Result,proto3" json:"result_xml,omitempty"`
	ErrorXml  string `protobuf:"bytes,2,opt,name=error_xml,json=Message,proto3" json:"error_xml,omitempty"`
}

func (x *ScreenCustomerResponse) Reset() {
	*x = ScreenCustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenCustomerResponse) ProtoMessage() {}

func (x *ScreenCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenCustomerResponse.ProtoReflect.Descriptor instead.
func (*ScreenCustomerResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{1}
}

func (x *ScreenCustomerResponse) GetResultXml() string {
	if x != nil {
		return x.ResultXml
	}
	return ""
}

func (x *ScreenCustomerResponse) GetErrorXml() string {
	if x != nil {
		return x.ErrorXml
	}
	return ""
}

type ProcessWebhookCallBackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiToken                         string                              `protobuf:"bytes,1,opt,name=api_token,json=apiToken,proto3" json:"api_token,omitempty"`
	RequestId                        string                              `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ParentCompany                    string                              `protobuf:"bytes,3,opt,name=parent_company,json=parentCompany,proto3" json:"parent_company,omitempty"`
	SourceSystem                     string                              `protobuf:"bytes,4,opt,name=source_system,json=sourceSystem,proto3" json:"source_system,omitempty"`
	ScreeningCaseRequestTransactions []*ScreeningCaseRequestTransactions `protobuf:"bytes,5,rep,name=screening_case_request_transactions,json=screeningCaseRequestTransactions,proto3" json:"screening_case_request_transactions,omitempty"`
}

func (x *ProcessWebhookCallBackRequest) Reset() {
	*x = ProcessWebhookCallBackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessWebhookCallBackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessWebhookCallBackRequest) ProtoMessage() {}

func (x *ProcessWebhookCallBackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessWebhookCallBackRequest.ProtoReflect.Descriptor instead.
func (*ProcessWebhookCallBackRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessWebhookCallBackRequest) GetApiToken() string {
	if x != nil {
		return x.ApiToken
	}
	return ""
}

func (x *ProcessWebhookCallBackRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ProcessWebhookCallBackRequest) GetParentCompany() string {
	if x != nil {
		return x.ParentCompany
	}
	return ""
}

func (x *ProcessWebhookCallBackRequest) GetSourceSystem() string {
	if x != nil {
		return x.SourceSystem
	}
	return ""
}

func (x *ProcessWebhookCallBackRequest) GetScreeningCaseRequestTransactions() []*ScreeningCaseRequestTransactions {
	if x != nil {
		return x.ScreeningCaseRequestTransactions
	}
	return nil
}

type ScreeningCaseRequestTransactions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionId              string `protobuf:"bytes,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	RecordIdentifier           string `protobuf:"bytes,2,opt,name=record_identifier,json=recordIdentifier,proto3" json:"record_identifier,omitempty"`
	CaseId                     int64  `protobuf:"varint,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	CaseUrl                    string `protobuf:"bytes,4,opt,name=case_url,json=caseUrl,proto3" json:"case_url,omitempty"`
	FinalDecision              string `protobuf:"bytes,5,opt,name=final_decision,json=finalDecision,proto3" json:"final_decision,omitempty"`
	Pep                        string `protobuf:"bytes,6,opt,name=pep,proto3" json:"pep,omitempty"`
	PepClassification          string `protobuf:"bytes,7,opt,name=pep_classification,json=pepClassification,proto3" json:"pep_classification,omitempty"`
	AdverseMedia               string `protobuf:"bytes,8,opt,name=adverse_media,json=adverseMedia,proto3" json:"adverse_media,omitempty"`
	AdverseMediaClassification string `protobuf:"bytes,9,opt,name=adverse_media_classification,json=adverseMediaClassification,proto3" json:"adverse_media_classification,omitempty"`
	ReputationalClassification string `protobuf:"bytes,10,opt,name=reputational_classification,json=reputationalClassification,proto3" json:"reputational_classification,omitempty"`
	FinalRemarks               string `protobuf:"bytes,11,opt,name=final_remarks,json=finalRemarks,proto3" json:"final_remarks,omitempty"`
	ApprovedOn                 string `protobuf:"bytes,12,opt,name=approved_on,json=approvedOn,proto3" json:"approved_on,omitempty"`
	ApprovedBy                 string `protobuf:"bytes,13,opt,name=approved_by,json=approvedBy,proto3" json:"approved_by,omitempty"`
}

func (x *ScreeningCaseRequestTransactions) Reset() {
	*x = ScreeningCaseRequestTransactions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreeningCaseRequestTransactions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreeningCaseRequestTransactions) ProtoMessage() {}

func (x *ScreeningCaseRequestTransactions) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreeningCaseRequestTransactions.ProtoReflect.Descriptor instead.
func (*ScreeningCaseRequestTransactions) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{3}
}

func (x *ScreeningCaseRequestTransactions) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetRecordIdentifier() string {
	if x != nil {
		return x.RecordIdentifier
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetCaseId() int64 {
	if x != nil {
		return x.CaseId
	}
	return 0
}

func (x *ScreeningCaseRequestTransactions) GetCaseUrl() string {
	if x != nil {
		return x.CaseUrl
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetFinalDecision() string {
	if x != nil {
		return x.FinalDecision
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetPep() string {
	if x != nil {
		return x.Pep
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetPepClassification() string {
	if x != nil {
		return x.PepClassification
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetAdverseMedia() string {
	if x != nil {
		return x.AdverseMedia
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetAdverseMediaClassification() string {
	if x != nil {
		return x.AdverseMediaClassification
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetReputationalClassification() string {
	if x != nil {
		return x.ReputationalClassification
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetFinalRemarks() string {
	if x != nil {
		return x.FinalRemarks
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetApprovedOn() string {
	if x != nil {
		return x.ApprovedOn
	}
	return ""
}

func (x *ScreeningCaseRequestTransactions) GetApprovedBy() string {
	if x != nil {
		return x.ApprovedBy
	}
	return ""
}

type ProcessWebhookCallBackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId                         string                               `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	RequestStatus                     string                               `protobuf:"bytes,2,opt,name=request_status,json=requestStatus,proto3" json:"request_status,omitempty"`
	ScreeningCaseResponseTransactions []*ScreeningCaseResponseTransactions `protobuf:"bytes,3,rep,name=screening_case_response_transactions,json=screeningCaseResponseTransactions,proto3" json:"screening_case_response_transactions,omitempty"`
}

func (x *ProcessWebhookCallBackResponse) Reset() {
	*x = ProcessWebhookCallBackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessWebhookCallBackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessWebhookCallBackResponse) ProtoMessage() {}

func (x *ProcessWebhookCallBackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessWebhookCallBackResponse.ProtoReflect.Descriptor instead.
func (*ProcessWebhookCallBackResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{4}
}

func (x *ProcessWebhookCallBackResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ProcessWebhookCallBackResponse) GetRequestStatus() string {
	if x != nil {
		return x.RequestStatus
	}
	return ""
}

func (x *ProcessWebhookCallBackResponse) GetScreeningCaseResponseTransactions() []*ScreeningCaseResponseTransactions {
	if x != nil {
		return x.ScreeningCaseResponseTransactions
	}
	return nil
}

type ScreeningCaseResponseTransactions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionId          string `protobuf:"bytes,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	TransactionStatus      string `protobuf:"bytes,2,opt,name=transaction_status,json=transactionStatus,proto3" json:"transaction_status,omitempty"`
	TransactionDescription string `protobuf:"bytes,3,opt,name=transaction_description,json=transactionDescription,proto3" json:"transaction_description,omitempty"`
}

func (x *ScreeningCaseResponseTransactions) Reset() {
	*x = ScreeningCaseResponseTransactions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreeningCaseResponseTransactions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreeningCaseResponseTransactions) ProtoMessage() {}

func (x *ScreeningCaseResponseTransactions) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreeningCaseResponseTransactions.ProtoReflect.Descriptor instead.
func (*ScreeningCaseResponseTransactions) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{5}
}

func (x *ScreeningCaseResponseTransactions) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *ScreeningCaseResponseTransactions) GetTransactionStatus() string {
	if x != nil {
		return x.TransactionStatus
	}
	return ""
}

func (x *ScreeningCaseResponseTransactions) GetTransactionDescription() string {
	if x != nil {
		return x.TransactionDescription
	}
	return ""
}

type ListCasesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ApiRequestDto *ListCasesRequestQuery `protobuf:"bytes,2,opt,name=api_request_dto,json=apiRequestDto,proto3" json:"api_request_dto,omitempty"`
}

func (x *ListCasesRequest) Reset() {
	*x = ListCasesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesRequest) ProtoMessage() {}

func (x *ListCasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesRequest.ProtoReflect.Descriptor instead.
func (*ListCasesRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{6}
}

func (x *ListCasesRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ListCasesRequest) GetApiRequestDto() *ListCasesRequestQuery {
	if x != nil {
		return x.ApiRequestDto
	}
	return nil
}

type ListCasesRequestQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Single or comma separated values, Possible values: Open,Pending,Completed
	CaseCategory     string `protobuf:"bytes,1,opt,name=case_category,json=caseCategory,proto3" json:"case_category,omitempty"`
	SourceSystemName string `protobuf:"bytes,2,opt,name=source_system_name,json=sourceSystemName,proto3" json:"source_system_name,omitempty"`
	// Timestamp in ISO 8601 Format: yyyy-MM-ddTHH:mm:sszzz
	FromDateTime string `protobuf:"bytes,3,opt,name=from_date_time,json=fromDateTime,proto3" json:"from_date_time,omitempty"`
	// Timestamp in ISO 8601 Format: yyyy-MM-ddTHH:mm:sszzz
	ToDateTime string `protobuf:"bytes,4,opt,name=to_date_time,json=toDateTime,proto3" json:"to_date_time,omitempty"`
	// Single or comma separated values, Possible values: Initial,WatchlistAdded
	CaseType string `protobuf:"bytes,5,opt,name=case_type,json=caseType,proto3" json:"case_type,omitempty"`
}

func (x *ListCasesRequestQuery) Reset() {
	*x = ListCasesRequestQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCasesRequestQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesRequestQuery) ProtoMessage() {}

func (x *ListCasesRequestQuery) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesRequestQuery.ProtoReflect.Descriptor instead.
func (*ListCasesRequestQuery) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{7}
}

func (x *ListCasesRequestQuery) GetCaseCategory() string {
	if x != nil {
		return x.CaseCategory
	}
	return ""
}

func (x *ListCasesRequestQuery) GetSourceSystemName() string {
	if x != nil {
		return x.SourceSystemName
	}
	return ""
}

func (x *ListCasesRequestQuery) GetFromDateTime() string {
	if x != nil {
		return x.FromDateTime
	}
	return ""
}

func (x *ListCasesRequestQuery) GetToDateTime() string {
	if x != nil {
		return x.ToDateTime
	}
	return ""
}

func (x *ListCasesRequestQuery) GetCaseType() string {
	if x != nil {
		return x.CaseType
	}
	return ""
}

type ListCasesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// List of validation codes
	ValidationCodes string `protobuf:"bytes,2,opt,name=validation_codes,json=validationCodes,proto3" json:"validation_codes,omitempty"`
	// List of validation descriptions
	ValidationDescriptions string                 `protobuf:"bytes,3,opt,name=validation_descriptions,json=validationDescriptions,proto3" json:"validation_descriptions,omitempty"`
	ResponseData           *ListCasesResponseData `protobuf:"bytes,4,opt,name=response_data,json=responseData,proto3" json:"response_data,omitempty"`
}

func (x *ListCasesResponse) Reset() {
	*x = ListCasesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesResponse) ProtoMessage() {}

func (x *ListCasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesResponse.ProtoReflect.Descriptor instead.
func (*ListCasesResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{8}
}

func (x *ListCasesResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ListCasesResponse) GetValidationCodes() string {
	if x != nil {
		return x.ValidationCodes
	}
	return ""
}

func (x *ListCasesResponse) GetValidationDescriptions() string {
	if x != nil {
		return x.ValidationDescriptions
	}
	return ""
}

func (x *ListCasesResponse) GetResponseData() *ListCasesResponseData {
	if x != nil {
		return x.ResponseData
	}
	return nil
}

type ListCasesResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseCount   int32          `protobuf:"varint,1,opt,name=case_count,json=caseCount,proto3" json:"case_count,omitempty"`
	CaseDetails []*CaseDetails `protobuf:"bytes,2,rep,name=case_details,json=caseDetails,proto3" json:"case_details,omitempty"`
}

func (x *ListCasesResponseData) Reset() {
	*x = ListCasesResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCasesResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesResponseData) ProtoMessage() {}

func (x *ListCasesResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesResponseData.ProtoReflect.Descriptor instead.
func (*ListCasesResponseData) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{9}
}

func (x *ListCasesResponseData) GetCaseCount() int32 {
	if x != nil {
		return x.CaseCount
	}
	return 0
}

func (x *ListCasesResponseData) GetCaseDetails() []*CaseDetails {
	if x != nil {
		return x.CaseDetails
	}
	return nil
}

type GetCaseDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId     string                     `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ApiRequestDto *GetCaseDetailsRequestData `protobuf:"bytes,2,opt,name=api_request_dto,json=apiRequestDto,proto3" json:"api_request_dto,omitempty"`
}

func (x *GetCaseDetailsRequest) Reset() {
	*x = GetCaseDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCaseDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseDetailsRequest) ProtoMessage() {}

func (x *GetCaseDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetCaseDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{10}
}

func (x *GetCaseDetailsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetCaseDetailsRequest) GetApiRequestDto() *GetCaseDetailsRequestData {
	if x != nil {
		return x.ApiRequestDto
	}
	return nil
}

type GetCaseDetailsRequestData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseId string `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
}

func (x *GetCaseDetailsRequestData) Reset() {
	*x = GetCaseDetailsRequestData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCaseDetailsRequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseDetailsRequestData) ProtoMessage() {}

func (x *GetCaseDetailsRequestData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseDetailsRequestData.ProtoReflect.Descriptor instead.
func (*GetCaseDetailsRequestData) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{11}
}

func (x *GetCaseDetailsRequestData) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

type GetCaseDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// List of validation codes
	ValidationCodes string `protobuf:"bytes,2,opt,name=validation_codes,json=validationCodes,proto3" json:"validation_codes,omitempty"`
	// List of validation descriptions
	ValidationDescriptions string                      `protobuf:"bytes,3,opt,name=validation_descriptions,json=validationDescriptions,proto3" json:"validation_descriptions,omitempty"`
	ResponseData           *GetCaseDetailsResponseData `protobuf:"bytes,4,opt,name=response_data,json=responseData,proto3" json:"response_data,omitempty"`
}

func (x *GetCaseDetailsResponse) Reset() {
	*x = GetCaseDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCaseDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseDetailsResponse) ProtoMessage() {}

func (x *GetCaseDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetCaseDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{12}
}

func (x *GetCaseDetailsResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetCaseDetailsResponse) GetValidationCodes() string {
	if x != nil {
		return x.ValidationCodes
	}
	return ""
}

func (x *GetCaseDetailsResponse) GetValidationDescriptions() string {
	if x != nil {
		return x.ValidationDescriptions
	}
	return ""
}

func (x *GetCaseDetailsResponse) GetResponseData() *GetCaseDetailsResponseData {
	if x != nil {
		return x.ResponseData
	}
	return nil
}

type GetCaseDetailsResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseDetails *CaseDetails `protobuf:"bytes,2,opt,name=case_details,json=caseDetails,proto3" json:"case_details,omitempty"`
}

func (x *GetCaseDetailsResponseData) Reset() {
	*x = GetCaseDetailsResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_tss_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCaseDetailsResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseDetailsResponseData) ProtoMessage() {}

func (x *GetCaseDetailsResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_tss_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseDetailsResponseData.ProtoReflect.Descriptor instead.
func (*GetCaseDetailsResponseData) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_tss_proto_rawDescGZIP(), []int{13}
}

func (x *GetCaseDetailsResponseData) GetCaseDetails() *CaseDetails {
	if x != nil {
		return x.CaseDetails
	}
	return nil
}

var File_api_vendors_tss_tss_proto protoreflect.FileDescriptor

var file_api_vendors_tss_tss_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73,
	0x73, 0x2f, 0x74, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73, 0x73, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x31, 0x0a, 0x15,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x0a, 0x78, 0x6d, 0x6c, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x50, 0x0a, 0x16, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x0a, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x5f, 0x78, 0x6d, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x78,
	0x6d, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0xa5, 0x02, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x57, 0x65, 0x62,
	0x68, 0x6f, 0x6f, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x70, 0x69, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x70, 0x69, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x7c, 0x0a, 0x23, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x20, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69,
	0x6e, 0x67, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa1, 0x04, 0x0a, 0x20, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25,
	0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x66, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x70, 0x65, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x65, 0x70, 0x12,
	0x2d, 0x0a, 0x12, 0x70, 0x65, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x70,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x73, 0x65, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x64, 0x76, 0x65, 0x72, 0x73, 0x65, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x12, 0x40, 0x0a, 0x1c, 0x61, 0x64, 0x76, 0x65, 0x72, 0x73, 0x65, 0x5f, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x73, 0x65, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x1b, 0x72, 0x65, 0x70, 0x75, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x72, 0x65, 0x70, 0x75,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66,
	0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x4f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x42, 0x79, 0x22, 0xe7, 0x01,
	0x0a, 0x1e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7f, 0x0a, 0x24, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74,
	0x73, 0x73, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x21, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x43,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xb2, 0x01, 0x0a, 0x21, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x0a,
	0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x17, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x7d, 0x0a, 0x10,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x4a, 0x0a, 0x0f, 0x61, 0x70, 0x69, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64,
	0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x0d, 0x61, 0x70,
	0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x74, 0x6f, 0x22, 0xcf, 0x01, 0x0a, 0x15,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61,
	0x73, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x72, 0x6f, 0x6d,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0c, 0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xdf, 0x01,
	0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x37, 0x0a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x47, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x73, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x61,
	0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0c, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x63, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x22, 0x86, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x4e, 0x0a,
	0x0f, 0x61, 0x70, 0x69, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x74, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x74, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d,
	0x61, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x74, 0x6f, 0x22, 0x34, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x22, 0xe9, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x29, 0x0a,
	0x10, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x59, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a,
	0x0c, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73,
	0x73, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x63,
	0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x50, 0x0a, 0x26, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x74, 0x73, 0x73, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_tss_tss_proto_rawDescOnce sync.Once
	file_api_vendors_tss_tss_proto_rawDescData = file_api_vendors_tss_tss_proto_rawDesc
)

func file_api_vendors_tss_tss_proto_rawDescGZIP() []byte {
	file_api_vendors_tss_tss_proto_rawDescOnce.Do(func() {
		file_api_vendors_tss_tss_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_tss_tss_proto_rawDescData)
	})
	return file_api_vendors_tss_tss_proto_rawDescData
}

var file_api_vendors_tss_tss_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_vendors_tss_tss_proto_goTypes = []interface{}{
	(*ScreenCustomerRequest)(nil),             // 0: vendors.tss.ScreenCustomerRequest
	(*ScreenCustomerResponse)(nil),            // 1: vendors.tss.ScreenCustomerResponse
	(*ProcessWebhookCallBackRequest)(nil),     // 2: vendors.tss.ProcessWebhookCallBackRequest
	(*ScreeningCaseRequestTransactions)(nil),  // 3: vendors.tss.ScreeningCaseRequestTransactions
	(*ProcessWebhookCallBackResponse)(nil),    // 4: vendors.tss.ProcessWebhookCallBackResponse
	(*ScreeningCaseResponseTransactions)(nil), // 5: vendors.tss.ScreeningCaseResponseTransactions
	(*ListCasesRequest)(nil),                  // 6: vendors.tss.ListCasesRequest
	(*ListCasesRequestQuery)(nil),             // 7: vendors.tss.ListCasesRequestQuery
	(*ListCasesResponse)(nil),                 // 8: vendors.tss.ListCasesResponse
	(*ListCasesResponseData)(nil),             // 9: vendors.tss.ListCasesResponseData
	(*GetCaseDetailsRequest)(nil),             // 10: vendors.tss.GetCaseDetailsRequest
	(*GetCaseDetailsRequestData)(nil),         // 11: vendors.tss.GetCaseDetailsRequestData
	(*GetCaseDetailsResponse)(nil),            // 12: vendors.tss.GetCaseDetailsResponse
	(*GetCaseDetailsResponseData)(nil),        // 13: vendors.tss.GetCaseDetailsResponseData
	(*CaseDetails)(nil),                       // 14: vendors.tss.CaseDetails
}
var file_api_vendors_tss_tss_proto_depIdxs = []int32{
	3,  // 0: vendors.tss.ProcessWebhookCallBackRequest.screening_case_request_transactions:type_name -> vendors.tss.ScreeningCaseRequestTransactions
	5,  // 1: vendors.tss.ProcessWebhookCallBackResponse.screening_case_response_transactions:type_name -> vendors.tss.ScreeningCaseResponseTransactions
	7,  // 2: vendors.tss.ListCasesRequest.api_request_dto:type_name -> vendors.tss.ListCasesRequestQuery
	9,  // 3: vendors.tss.ListCasesResponse.response_data:type_name -> vendors.tss.ListCasesResponseData
	14, // 4: vendors.tss.ListCasesResponseData.case_details:type_name -> vendors.tss.CaseDetails
	11, // 5: vendors.tss.GetCaseDetailsRequest.api_request_dto:type_name -> vendors.tss.GetCaseDetailsRequestData
	13, // 6: vendors.tss.GetCaseDetailsResponse.response_data:type_name -> vendors.tss.GetCaseDetailsResponseData
	14, // 7: vendors.tss.GetCaseDetailsResponseData.case_details:type_name -> vendors.tss.CaseDetails
	8,  // [8:8] is the sub-list for method output_type
	8,  // [8:8] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_vendors_tss_tss_proto_init() }
func file_api_vendors_tss_tss_proto_init() {
	if File_api_vendors_tss_tss_proto != nil {
		return
	}
	file_api_vendors_tss_case_details_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_tss_tss_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenCustomerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenCustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessWebhookCallBackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreeningCaseRequestTransactions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessWebhookCallBackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreeningCaseResponseTransactions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCasesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCasesRequestQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCasesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCasesResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCaseDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCaseDetailsRequestData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCaseDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_tss_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCaseDetailsResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_tss_tss_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_tss_tss_proto_goTypes,
		DependencyIndexes: file_api_vendors_tss_tss_proto_depIdxs,
		MessageInfos:      file_api_vendors_tss_tss_proto_msgTypes,
	}.Build()
	File_api_vendors_tss_tss_proto = out.File
	file_api_vendors_tss_tss_proto_rawDesc = nil
	file_api_vendors_tss_tss_proto_goTypes = nil
	file_api_vendors_tss_tss_proto_depIdxs = nil
}
