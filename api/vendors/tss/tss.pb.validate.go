// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/tss/tss.proto

package tss

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ScreenCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenCustomerRequestMultiError, or nil if none found.
func (m *ScreenCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for XmlString

	if len(errors) > 0 {
		return ScreenCustomerRequestMultiError(errors)
	}

	return nil
}

// ScreenCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by ScreenCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type ScreenCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenCustomerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenCustomerRequestMultiError) AllErrors() []error { return m }

// ScreenCustomerRequestValidationError is the validation error returned by
// ScreenCustomerRequest.Validate if the designated constraints aren't met.
type ScreenCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenCustomerRequestValidationError) ErrorName() string {
	return "ScreenCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenCustomerRequestValidationError{}

// Validate checks the field values on ScreenCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenCustomerResponseMultiError, or nil if none found.
func (m *ScreenCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResultXml

	// no validation rules for ErrorXml

	if len(errors) > 0 {
		return ScreenCustomerResponseMultiError(errors)
	}

	return nil
}

// ScreenCustomerResponseMultiError is an error wrapping multiple validation
// errors returned by ScreenCustomerResponse.ValidateAll() if the designated
// constraints aren't met.
type ScreenCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenCustomerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenCustomerResponseMultiError) AllErrors() []error { return m }

// ScreenCustomerResponseValidationError is the validation error returned by
// ScreenCustomerResponse.Validate if the designated constraints aren't met.
type ScreenCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenCustomerResponseValidationError) ErrorName() string {
	return "ScreenCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenCustomerResponseValidationError{}

// Validate checks the field values on ProcessWebhookCallBackRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessWebhookCallBackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessWebhookCallBackRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessWebhookCallBackRequestMultiError, or nil if none found.
func (m *ProcessWebhookCallBackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessWebhookCallBackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApiToken

	// no validation rules for RequestId

	// no validation rules for ParentCompany

	// no validation rules for SourceSystem

	for idx, item := range m.GetScreeningCaseRequestTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessWebhookCallBackRequestValidationError{
						field:  fmt.Sprintf("ScreeningCaseRequestTransactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessWebhookCallBackRequestValidationError{
						field:  fmt.Sprintf("ScreeningCaseRequestTransactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessWebhookCallBackRequestValidationError{
					field:  fmt.Sprintf("ScreeningCaseRequestTransactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessWebhookCallBackRequestMultiError(errors)
	}

	return nil
}

// ProcessWebhookCallBackRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessWebhookCallBackRequest.ValidateAll()
// if the designated constraints aren't met.
type ProcessWebhookCallBackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessWebhookCallBackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessWebhookCallBackRequestMultiError) AllErrors() []error { return m }

// ProcessWebhookCallBackRequestValidationError is the validation error
// returned by ProcessWebhookCallBackRequest.Validate if the designated
// constraints aren't met.
type ProcessWebhookCallBackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessWebhookCallBackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessWebhookCallBackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessWebhookCallBackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessWebhookCallBackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessWebhookCallBackRequestValidationError) ErrorName() string {
	return "ProcessWebhookCallBackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessWebhookCallBackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessWebhookCallBackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessWebhookCallBackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessWebhookCallBackRequestValidationError{}

// Validate checks the field values on ScreeningCaseRequestTransactions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ScreeningCaseRequestTransactions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreeningCaseRequestTransactions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ScreeningCaseRequestTransactionsMultiError, or nil if none found.
func (m *ScreeningCaseRequestTransactions) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreeningCaseRequestTransactions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransactionId

	// no validation rules for RecordIdentifier

	// no validation rules for CaseId

	// no validation rules for CaseUrl

	// no validation rules for FinalDecision

	// no validation rules for Pep

	// no validation rules for PepClassification

	// no validation rules for AdverseMedia

	// no validation rules for AdverseMediaClassification

	// no validation rules for ReputationalClassification

	// no validation rules for FinalRemarks

	// no validation rules for ApprovedOn

	// no validation rules for ApprovedBy

	if len(errors) > 0 {
		return ScreeningCaseRequestTransactionsMultiError(errors)
	}

	return nil
}

// ScreeningCaseRequestTransactionsMultiError is an error wrapping multiple
// validation errors returned by
// ScreeningCaseRequestTransactions.ValidateAll() if the designated
// constraints aren't met.
type ScreeningCaseRequestTransactionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreeningCaseRequestTransactionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreeningCaseRequestTransactionsMultiError) AllErrors() []error { return m }

// ScreeningCaseRequestTransactionsValidationError is the validation error
// returned by ScreeningCaseRequestTransactions.Validate if the designated
// constraints aren't met.
type ScreeningCaseRequestTransactionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreeningCaseRequestTransactionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreeningCaseRequestTransactionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreeningCaseRequestTransactionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreeningCaseRequestTransactionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreeningCaseRequestTransactionsValidationError) ErrorName() string {
	return "ScreeningCaseRequestTransactionsValidationError"
}

// Error satisfies the builtin error interface
func (e ScreeningCaseRequestTransactionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreeningCaseRequestTransactions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreeningCaseRequestTransactionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreeningCaseRequestTransactionsValidationError{}

// Validate checks the field values on ProcessWebhookCallBackResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessWebhookCallBackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessWebhookCallBackResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessWebhookCallBackResponseMultiError, or nil if none found.
func (m *ProcessWebhookCallBackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessWebhookCallBackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for RequestStatus

	for idx, item := range m.GetScreeningCaseResponseTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessWebhookCallBackResponseValidationError{
						field:  fmt.Sprintf("ScreeningCaseResponseTransactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessWebhookCallBackResponseValidationError{
						field:  fmt.Sprintf("ScreeningCaseResponseTransactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessWebhookCallBackResponseValidationError{
					field:  fmt.Sprintf("ScreeningCaseResponseTransactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessWebhookCallBackResponseMultiError(errors)
	}

	return nil
}

// ProcessWebhookCallBackResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessWebhookCallBackResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessWebhookCallBackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessWebhookCallBackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessWebhookCallBackResponseMultiError) AllErrors() []error { return m }

// ProcessWebhookCallBackResponseValidationError is the validation error
// returned by ProcessWebhookCallBackResponse.Validate if the designated
// constraints aren't met.
type ProcessWebhookCallBackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessWebhookCallBackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessWebhookCallBackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessWebhookCallBackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessWebhookCallBackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessWebhookCallBackResponseValidationError) ErrorName() string {
	return "ProcessWebhookCallBackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessWebhookCallBackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessWebhookCallBackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessWebhookCallBackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessWebhookCallBackResponseValidationError{}

// Validate checks the field values on ScreeningCaseResponseTransactions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ScreeningCaseResponseTransactions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreeningCaseResponseTransactions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ScreeningCaseResponseTransactionsMultiError, or nil if none found.
func (m *ScreeningCaseResponseTransactions) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreeningCaseResponseTransactions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransactionId

	// no validation rules for TransactionStatus

	// no validation rules for TransactionDescription

	if len(errors) > 0 {
		return ScreeningCaseResponseTransactionsMultiError(errors)
	}

	return nil
}

// ScreeningCaseResponseTransactionsMultiError is an error wrapping multiple
// validation errors returned by
// ScreeningCaseResponseTransactions.ValidateAll() if the designated
// constraints aren't met.
type ScreeningCaseResponseTransactionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreeningCaseResponseTransactionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreeningCaseResponseTransactionsMultiError) AllErrors() []error { return m }

// ScreeningCaseResponseTransactionsValidationError is the validation error
// returned by ScreeningCaseResponseTransactions.Validate if the designated
// constraints aren't met.
type ScreeningCaseResponseTransactionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreeningCaseResponseTransactionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreeningCaseResponseTransactionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreeningCaseResponseTransactionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreeningCaseResponseTransactionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreeningCaseResponseTransactionsValidationError) ErrorName() string {
	return "ScreeningCaseResponseTransactionsValidationError"
}

// Error satisfies the builtin error interface
func (e ScreeningCaseResponseTransactionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreeningCaseResponseTransactions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreeningCaseResponseTransactionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreeningCaseResponseTransactionsValidationError{}

// Validate checks the field values on ListCasesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListCasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCasesRequestMultiError, or nil if none found.
func (m *ListCasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetApiRequestDto()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCasesRequestValidationError{
					field:  "ApiRequestDto",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCasesRequestValidationError{
					field:  "ApiRequestDto",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApiRequestDto()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCasesRequestValidationError{
				field:  "ApiRequestDto",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCasesRequestMultiError(errors)
	}

	return nil
}

// ListCasesRequestMultiError is an error wrapping multiple validation errors
// returned by ListCasesRequest.ValidateAll() if the designated constraints
// aren't met.
type ListCasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCasesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCasesRequestMultiError) AllErrors() []error { return m }

// ListCasesRequestValidationError is the validation error returned by
// ListCasesRequest.Validate if the designated constraints aren't met.
type ListCasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCasesRequestValidationError) ErrorName() string { return "ListCasesRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListCasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCasesRequestValidationError{}

// Validate checks the field values on ListCasesRequestQuery with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCasesRequestQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCasesRequestQuery with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCasesRequestQueryMultiError, or nil if none found.
func (m *ListCasesRequestQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCasesRequestQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseCategory

	// no validation rules for SourceSystemName

	// no validation rules for FromDateTime

	// no validation rules for ToDateTime

	// no validation rules for CaseType

	if len(errors) > 0 {
		return ListCasesRequestQueryMultiError(errors)
	}

	return nil
}

// ListCasesRequestQueryMultiError is an error wrapping multiple validation
// errors returned by ListCasesRequestQuery.ValidateAll() if the designated
// constraints aren't met.
type ListCasesRequestQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCasesRequestQueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCasesRequestQueryMultiError) AllErrors() []error { return m }

// ListCasesRequestQueryValidationError is the validation error returned by
// ListCasesRequestQuery.Validate if the designated constraints aren't met.
type ListCasesRequestQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCasesRequestQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCasesRequestQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCasesRequestQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCasesRequestQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCasesRequestQueryValidationError) ErrorName() string {
	return "ListCasesRequestQueryValidationError"
}

// Error satisfies the builtin error interface
func (e ListCasesRequestQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCasesRequestQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCasesRequestQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCasesRequestQueryValidationError{}

// Validate checks the field values on ListCasesResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListCasesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCasesResponseMultiError, or nil if none found.
func (m *ListCasesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCasesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for ValidationCodes

	// no validation rules for ValidationDescriptions

	if all {
		switch v := interface{}(m.GetResponseData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCasesResponseValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCasesResponseValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCasesResponseValidationError{
				field:  "ResponseData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCasesResponseMultiError(errors)
	}

	return nil
}

// ListCasesResponseMultiError is an error wrapping multiple validation errors
// returned by ListCasesResponse.ValidateAll() if the designated constraints
// aren't met.
type ListCasesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCasesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCasesResponseMultiError) AllErrors() []error { return m }

// ListCasesResponseValidationError is the validation error returned by
// ListCasesResponse.Validate if the designated constraints aren't met.
type ListCasesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCasesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCasesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCasesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCasesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCasesResponseValidationError) ErrorName() string {
	return "ListCasesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCasesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCasesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCasesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCasesResponseValidationError{}

// Validate checks the field values on ListCasesResponseData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCasesResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCasesResponseData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCasesResponseDataMultiError, or nil if none found.
func (m *ListCasesResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCasesResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseCount

	for idx, item := range m.GetCaseDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCasesResponseDataValidationError{
						field:  fmt.Sprintf("CaseDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCasesResponseDataValidationError{
						field:  fmt.Sprintf("CaseDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCasesResponseDataValidationError{
					field:  fmt.Sprintf("CaseDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListCasesResponseDataMultiError(errors)
	}

	return nil
}

// ListCasesResponseDataMultiError is an error wrapping multiple validation
// errors returned by ListCasesResponseData.ValidateAll() if the designated
// constraints aren't met.
type ListCasesResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCasesResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCasesResponseDataMultiError) AllErrors() []error { return m }

// ListCasesResponseDataValidationError is the validation error returned by
// ListCasesResponseData.Validate if the designated constraints aren't met.
type ListCasesResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCasesResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCasesResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCasesResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCasesResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCasesResponseDataValidationError) ErrorName() string {
	return "ListCasesResponseDataValidationError"
}

// Error satisfies the builtin error interface
func (e ListCasesResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCasesResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCasesResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCasesResponseDataValidationError{}

// Validate checks the field values on GetCaseDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseDetailsRequestMultiError, or nil if none found.
func (m *GetCaseDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetApiRequestDto()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsRequestValidationError{
					field:  "ApiRequestDto",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsRequestValidationError{
					field:  "ApiRequestDto",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApiRequestDto()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsRequestValidationError{
				field:  "ApiRequestDto",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCaseDetailsRequestMultiError(errors)
	}

	return nil
}

// GetCaseDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetCaseDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCaseDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseDetailsRequestMultiError) AllErrors() []error { return m }

// GetCaseDetailsRequestValidationError is the validation error returned by
// GetCaseDetailsRequest.Validate if the designated constraints aren't met.
type GetCaseDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseDetailsRequestValidationError) ErrorName() string {
	return "GetCaseDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseDetailsRequestValidationError{}

// Validate checks the field values on GetCaseDetailsRequestData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseDetailsRequestData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseDetailsRequestData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseDetailsRequestDataMultiError, or nil if none found.
func (m *GetCaseDetailsRequestData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseDetailsRequestData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseId

	if len(errors) > 0 {
		return GetCaseDetailsRequestDataMultiError(errors)
	}

	return nil
}

// GetCaseDetailsRequestDataMultiError is an error wrapping multiple validation
// errors returned by GetCaseDetailsRequestData.ValidateAll() if the
// designated constraints aren't met.
type GetCaseDetailsRequestDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseDetailsRequestDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseDetailsRequestDataMultiError) AllErrors() []error { return m }

// GetCaseDetailsRequestDataValidationError is the validation error returned by
// GetCaseDetailsRequestData.Validate if the designated constraints aren't met.
type GetCaseDetailsRequestDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseDetailsRequestDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseDetailsRequestDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseDetailsRequestDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseDetailsRequestDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseDetailsRequestDataValidationError) ErrorName() string {
	return "GetCaseDetailsRequestDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseDetailsRequestDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseDetailsRequestData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseDetailsRequestDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseDetailsRequestDataValidationError{}

// Validate checks the field values on GetCaseDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseDetailsResponseMultiError, or nil if none found.
func (m *GetCaseDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for ValidationCodes

	// no validation rules for ValidationDescriptions

	if all {
		switch v := interface{}(m.GetResponseData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsResponseValidationError{
				field:  "ResponseData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCaseDetailsResponseMultiError(errors)
	}

	return nil
}

// GetCaseDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetCaseDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCaseDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseDetailsResponseMultiError) AllErrors() []error { return m }

// GetCaseDetailsResponseValidationError is the validation error returned by
// GetCaseDetailsResponse.Validate if the designated constraints aren't met.
type GetCaseDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseDetailsResponseValidationError) ErrorName() string {
	return "GetCaseDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseDetailsResponseValidationError{}

// Validate checks the field values on GetCaseDetailsResponseData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseDetailsResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseDetailsResponseData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseDetailsResponseDataMultiError, or nil if none found.
func (m *GetCaseDetailsResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseDetailsResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCaseDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsResponseDataValidationError{
					field:  "CaseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsResponseDataValidationError{
					field:  "CaseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaseDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsResponseDataValidationError{
				field:  "CaseDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCaseDetailsResponseDataMultiError(errors)
	}

	return nil
}

// GetCaseDetailsResponseDataMultiError is an error wrapping multiple
// validation errors returned by GetCaseDetailsResponseData.ValidateAll() if
// the designated constraints aren't met.
type GetCaseDetailsResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseDetailsResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseDetailsResponseDataMultiError) AllErrors() []error { return m }

// GetCaseDetailsResponseDataValidationError is the validation error returned
// by GetCaseDetailsResponseData.Validate if the designated constraints aren't met.
type GetCaseDetailsResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseDetailsResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseDetailsResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseDetailsResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseDetailsResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseDetailsResponseDataValidationError) ErrorName() string {
	return "GetCaseDetailsResponseDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseDetailsResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseDetailsResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseDetailsResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseDetailsResponseDataValidationError{}
