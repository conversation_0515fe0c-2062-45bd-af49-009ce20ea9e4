import csv
import random
from pathlib import Path


CSV_PATH = Path("./Simulation Input Format - Filled - Sheet1.csv")
RISK_FRACTION = 0.10  # 10%


def load_rows(path: Path):
    with path.open(newline="", encoding="utf-8") as f:
        reader = csv.reader(f)
        return [row for row in reader]


def persist_rows(path: Path, rows):
    with path.open("w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerows(rows)


def mark_risky(row, header_index):
    # Introduce synthetic suspicious patterns WITHOUT using real individuals
    # Fields to tweak: email, PAN, mobile, passport, aadhaar (keep blank), voter, GSTIN
    def set_if_exists(field, value):
        idx = header_index.get(field)
        if idx is not None and idx < len(row):
            row[idx] = value

    disposable_domains = [
        "trashmail.io",
        "tempmail.test",
        "mailinator.com",
        "10minutemail.dev",
        "example.invalid",
    ]

    # Email with disposable domain
    email_idx = header_index.get("PersonalEmail")
    first_idx = header_index.get("FirstName")
    last_idx = header_index.get("LastName")
    if email_idx is not None:
        first = row[first_idx] if first_idx is not None else "user"
        last = row[last_idx] if last_idx is not None else "risk"
        row[email_idx] = f"{first.lower()}.{last.lower()}{random.randint(100,999)}@{random.choice(disposable_domains)}"

    # PAN with unusual pattern (still alnum but improbable)
    set_if_exists("PAN", "**********")

    # Mobile with repeated digits
    set_if_exists("PersonalMobileNumber", random.choice(["**********", "**********", "**********"]))

    # Passport with obviously placeholder-like code
    set_if_exists("PassportNumber", "XXXXXXX")

    # Driving license odd placeholder
    set_if_exists("DrivingLicenseNumber", "DL-INVALID")

    # NationalID placeholder
    set_if_exists("NationalID", "BLACKLIST-CHECK")

    # Aadhaar remains blank to avoid realistic identifiers
    set_if_exists("AadhaarNumber", "")

    # GSTIN improbable
    set_if_exists("GSTIN", "00AAAAA0000A0Z0")

    # Gender with improbable code to flag downstream validation
    set_if_exists("Gender", "9")

    # Keep addresses but optionally mismatch countries on correspondence to add anomaly
    corr_country_idx = header_index.get("CorrespondenceAddressCountry")
    if corr_country_idx is not None:
        row[corr_country_idx] = random.choice(["ZZZ", "N/A", "IND"])  # mostly IND, but include odd codes

    # Source system keep as is
    return row


def build_header_index(header_row):
    index = {}
    for i, name in enumerate(header_row):
        index[name.strip()] = i
    return index


def main():
    rows = load_rows(CSV_PATH)
    if not rows:
        print("No rows found")
        return

    header = rows[0]
    header_index = build_header_index(header)

    # Data rows start from line 6 in provided file; but generalize: skip non-data rows where first col not int
    data_start = 1
    for i in range(1, min(len(rows), 10)):
        try:
            int((rows[i][0] or "").strip())
            data_start = i
            break
        except Exception:
            continue

    data_rows = rows[data_start:]
    # Filter to rows that look like data (first column int)
    valid_indices = []
    for idx, r in enumerate(data_rows):
        try:
            int((r[0] or "").strip())
            valid_indices.append(idx)
        except Exception:
            pass

    count_to_mark = max(1, int(len(valid_indices) * RISK_FRACTION))
    risky_local_indices = set(random.sample(valid_indices, count_to_mark))

    for idx in risky_local_indices:
        data_rows[idx] = mark_risky(data_rows[idx], header_index)

    rows[data_start:] = data_rows
    persist_rows(CSV_PATH, rows)
    print(f"Marked {count_to_mark} rows (~{int(RISK_FRACTION*100)}%) as synthetic high-risk")


if __name__ == "__main__":
    main()




