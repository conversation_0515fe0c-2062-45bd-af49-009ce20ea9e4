import csv
import random
import string
from datetime import datetime, timedelta


CSV_PATH = "./Simulation Input Format - Filled - Sheet1.csv"


def random_string(letters: int) -> str:
    return "".join(random.choices(string.ascii_uppercase, k=letters))


def random_alnum(length: int) -> str:
    chars = string.ascii_uppercase + string.digits
    return "".join(random.choices(chars, k=length))


def random_pan() -> str:
    # PAN format: 5 letters + 4 digits + 1 letter
    return random_string(5) + "".join(random.choices(string.digits, k=4)) + random_string(1)


def random_mobile() -> str:
    # 10 digit number, starting 6-9 (India typical)
    start = random.choice(["6", "7", "8", "9"])
    return start + "".join(random.choices(string.digits, k=9))


def random_email(first: str, last: str) -> str:
    domains = ["example.com", "mail.test", "sample.org", "demo.net"]
    return f"{first.lower()}.{last.lower()}{random.randint(10, 9999)}@{random.choice(domains)}"


def random_date_of_birth() -> str:
    # Age between 18 and 70
    today = datetime.today()
    min_birth = today - timedelta(days=70 * 365)
    max_birth = today - timedelta(days=18 * 365)
    rand_day = min_birth + timedelta(seconds=random.randint(0, int((max_birth - min_birth).total_seconds())))
    return rand_day.strftime("%d/%b/%Y")


def random_passport() -> str:
    # Simple alnum between 7-9 chars
    return random_alnum(random.randint(7, 9))


def random_dl() -> str:
    # Simple DL pattern: 2 letters + 2 digits + 11 digits
    return random_string(2) + "".join(random.choices(string.digits, k=13))


def random_gender_code() -> str:
    # Use codes as numeric strings to stay consistent with sample ("1")
    return random.choice(["1", "2", "3"])  # example set


def random_zip() -> str:
    return "".join(random.choices(string.digits, k=6))


STATES_TO_CITIES = {
    "Karnataka": ["Bengaluru", "Mysuru", "Mangaluru"],
    "Maharashtra": ["Mumbai", "Pune", "Nagpur"],
    "Tamil Nadu": ["Chennai", "Coimbatore", "Madurai"],
    "Delhi": ["New Delhi"],
    "Telangana": ["Hyderabad", "Warangal"],
    "West Bengal": ["Kolkata", "Howrah"],
    "Gujarat": ["Ahmedabad", "Surat", "Vadodara"],
    "Rajasthan": ["Jaipur", "Udaipur"],
}


FIRST_NAMES = [
    "Aarav",
    "Vivaan",
    "Aditya",
    "Vihaan",
    "Arjun",
    "Ishaan",
    "Kabir",
    "Rohan",
    "Neha",
    "Aisha",
    "Diya",
    "Ananya",
    "Ira",
    "Kavya",
    "Maya",
]


MIDDLE_NAMES = ["Kumar", "Raj", "Singh", "Prasad", "Rao", ""]


LAST_NAMES = [
    "Sharma",
    "Verma",
    "Mehta",
    "Reddy",
    "Iyer",
    "Das",
    "Naidu",
    "Patil",
    "Saxena",
    "Gupta",
]


def random_name():
    first = random.choice(FIRST_NAMES)
    middle = random.choice(MIDDLE_NAMES)
    last = random.choice(LAST_NAMES)
    return first, middle, last


def generate_row(record_id: int):
    # Columns per header in row 1
    # Note: Keep Country as IND and use ISO-like formatting as in sample
    first, middle, last = random_name()
    dob = random_date_of_birth()
    nationality = "IND"
    pan = random_pan()
    mobile = random_mobile()
    email = random_email(first, last)
    passport = random_passport() if random.random() < 0.6 else ""
    dl = random_dl() if random.random() < 0.5 else ""
    national_id = ""  # leaving blank unless needed
    din = ""
    cin = ""
    lei = ""
    aadhaar = ""  # do not generate real-like identifiers
    udhyam = ""
    voter = ""
    isin = ""
    gstin = ""
    gender = random_gender_code()

    perm_state = random.choice(list(STATES_TO_CITIES.keys()))
    perm_city = random.choice(STATES_TO_CITIES[perm_state])
    perm_zip = random_zip()
    perm_country = "IND"

    corr_state = random.choice(list(STATES_TO_CITIES.keys()))
    corr_city = random.choice(STATES_TO_CITIES[corr_state])
    corr_zip = random_zip()
    corr_country = "IND"

    return [
        str(record_id),  # RecordIdentifier
        "Epifi Tech",  # SourceSystemName
        str(random.randint(1000, 9999)),  # SourceSystemCustomerCode
        f"APP{random.randint(1000, 999999)}",  # ApplicationRefNumber
        first,  # FirstName
        middle,  # MiddleName
        last,  # LastName
        dob,  # DateofBirth (DD-MMM-YYYY)
        nationality,  # Nationality
        pan,  # PAN
        mobile,  # PersonalMobileNumber
        email,  # PersonalEmail
        passport,  # PassportNumber
        dl,  # DrivingLicenseNumber
        national_id,  # NationalID
        din,  # DirectorIdentificationNumber
        cin,  # CompanyIdentificationNumber
        lei,  # LegalEntityIdentifier
        aadhaar,  # AadhaarNumber
        udhyam,  # UdhyamRC
        voter,  # VoterIdNumber
        isin,  # ISIN
        gstin,  # GSTIN
        gender,  # Gender
        perm_zip,  # PermanentAddressZipCode
        perm_state,  # PermanentAddressState
        perm_city,  # PermanentAddressCity
        perm_country,  # PermanentAddressCountry
        corr_zip,  # CorrespondenceAddressZipCode
        corr_state,  # CorrespondenceAddressState
        corr_city,  # CorrespondenceAddressCity
        corr_country,  # CorrespondenceAddressCountry
    ]


def find_starting_record_id() -> int:
    # Determine next RecordIdentifier by reading last non-empty data row
    last_id = 0
    with open(CSV_PATH, newline="", encoding="utf-8") as f:
        for idx, line in enumerate(f, start=1):
            pass  # iterate to end for efficiency without storing
    # Now re-read only the last 100 lines or so to find the last data row
    with open(CSV_PATH, newline="", encoding="utf-8") as f:
        lines = f.readlines()
    for raw in reversed(lines):
        row = [col.strip() for col in raw.rstrip("\n").split(",")]
        if not row or not row[0] or row[0].startswith("Note:") or row[0].startswith("Sample values"):
            continue
        # Row 1 is header; rows 2-5 are notes in the provided file
        try:
            last_id = int(row[0])
            break
        except ValueError:
            continue
    return last_id + 1 if last_id > 0 else 2  # row 6 has id 1, so next is 2


def main(num_rows: int = 500):
    start_id = find_starting_record_id()
    rows = [generate_row(start_id + i) for i in range(num_rows)]

    # Append mode; avoid csv.writer to preserve exact commas as in sample
    with open(CSV_PATH, "a", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        for r in rows:
            writer.writerow(r)

    print(f"Appended {num_rows} rows starting from RecordIdentifier {start_id}")


if __name__ == "__main__":
    main()


