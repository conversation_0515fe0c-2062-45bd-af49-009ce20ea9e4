repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.2.0
    hooks:
      # Prevent giant files from being committed
      - id: check-added-large-files

      # Attempts to load all json files to verify syntax
      - id: check-json

      # Attempts to load all yaml files to verify syntax
      - id: check-yaml

      # Check for files that contain merge conflict strings
      - id: check-merge-conflict

      # TODO(team): This check is failing as we have cert & key in simulator dir
      # Checks for the existence of private keys.
      # - id: detect-private-key

      # Makes sure files end in a newline and only a newline
      # Note: This hook is disabled because of a lot of files in this repo not following the practice currently
      # - id: end-of-file-fixer

      # Replaces or checks mixed line ending
      - id: mixed-line-ending
        args: ['--fix=no']

      # Protect specific branches from direct check-ins
      - id: no-commit-to-branch
        args: [--branch, master]
