FROM 632884248997.dkr.ecr.ap-south-1.amazonaws.com/postgres:14.9

ENV POSTGRES_USER=root
ENV POSTGRES_HOST_AUTH_METHOD=trust
ENV POSTGRES_DB=postgres
COPY ./scripts/preonboarded_data_dump/pgdb_backup.sh /pgdb_backup.sh
COPY ./testing/integration/output/db-backup/pgdb-backup/ /output/pgdb-backup/
# Copy db migration files
COPY ./db/ /output/db/
RUN chmod +x /pgdb_backup.sh
EXPOSE 5432
ENTRYPOINT ["/pgdb_backup.sh"]
